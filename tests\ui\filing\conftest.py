#!/usr/bin/env python3
"""
备案流程测试配置
提供备案测试专用的配置和 fixtures
"""

import pytest
import allure
from pathlib import Path
import sys

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

# 导入备案专用 fixtures
from fixtures.filing_fixtures import *


@pytest.fixture(scope="session", autouse=True)
def filing_test_session_setup():
    """
    备案测试会话级别设置
    自动应用到所有备案测试会话
    """
    from components.log_manager import get_logger
    logger = get_logger(__name__)
    
    logger.info("=" * 60)
    logger.info("开始 SCF 备案流程测试会话")
    logger.info("=" * 60)
    
    # 会话级别的设置
    # 例如：清理旧的测试数据、初始化测试环境等
    
    yield
    
    logger.info("=" * 60)
    logger.info("结束 SCF 备案流程测试会话")
    logger.info("=" * 60)


@pytest.fixture(scope="class", autouse=True)
def filing_test_class_setup(request):
    """
    备案测试类级别设置
    自动应用到所有备案测试类
    """
    from components.log_manager import get_logger
    logger = get_logger(__name__)
    
    class_name = request.cls.__name__ if request.cls else "Unknown"
    logger.info(f"开始测试类: {class_name}")
    
    yield
    
    logger.info(f"结束测试类: {class_name}")


# 测试数据配置
FILING_TEST_CONFIG = {
    "base_url": "http://172.18.12.128:8080",
    "login_url": "http://172.18.12.128:8080/login",
    "filing_url": "http://172.18.12.128:8080/filing",
    "timeout": {
        "default": 30000,
        "file_upload": 60000,
        "form_submit": 45000
    },
    "retry": {
        "max_attempts": 3,
        "delay": 2000
    },
    "file_limits": {
        "max_size_mb": 10,
        "allowed_formats": [".pdf", ".doc", ".docx", ".xls", ".xlsx"]
    }
}


@pytest.fixture(scope="session")
def filing_config():
    """备案测试配置 fixture"""
    return FILING_TEST_CONFIG


# 测试分组标记
pytestmark = [
    pytest.mark.filing,  # 标记为备案测试
    allure.epic("SCF备案系统"),  # Allure epic
]


def pytest_collection_modifyitems(config, items):
    """
    修改测试收集，添加自动标记
    """
    for item in items:
        # 为备案测试添加自动标记
        if "filing" in str(item.fspath):
            item.add_marker(pytest.mark.filing)
            
            # 根据文件名添加特定标记
            if "normal" in str(item.fspath):
                item.add_marker(pytest.mark.filing_normal)
            elif "exception" in str(item.fspath):
                item.add_marker(pytest.mark.filing_exception)
            elif "boundary" in str(item.fspath):
                item.add_marker(pytest.mark.filing_boundary)


def pytest_runtest_setup(item):
    """
    测试运行前设置
    """
    from components.log_manager import get_logger
    logger = get_logger(__name__)
    
    # 记录测试开始
    logger.info(f"开始执行测试: {item.name}")
    
    # 检查测试标记
    markers = [mark.name for mark in item.iter_markers()]
    if markers:
        logger.info(f"测试标记: {', '.join(markers)}")


def pytest_runtest_teardown(item, nextitem):
    """
    测试运行后清理
    """
    from components.log_manager import get_logger
    logger = get_logger(__name__)
    
    logger.info(f"完成测试: {item.name}")


def pytest_runtest_makereport(item, call):
    """
    生成测试报告
    """
    if call.when == "call":
        # 测试执行阶段
        from components.log_manager import get_logger
        logger = get_logger(__name__)
        
        if call.excinfo is None:
            logger.info(f"测试通过: {item.name}")
        else:
            logger.error(f"测试失败: {item.name} - {call.excinfo.value}")


# 自定义测试跳过条件
def pytest_runtest_setup(item):
    """
    根据条件跳过测试
    """
    # 检查是否有必要的测试文件
    if item.get_closest_marker("requires_files"):
        from pathlib import Path
        test_files_dir = Path(__file__).parent.parent.parent.parent / "test_data" / "files"
        required_files = [
            "异常信息共享系统V1.5需求.docx",
            "格式二模版.xlsx",
            "供应链金融测试报告V1.6.0.pdf"
        ]
        
        missing_files = []
        for filename in required_files:
            if not (test_files_dir / filename).exists():
                missing_files.append(filename)
        
        if missing_files:
            pytest.skip(f"缺少必要的测试文件: {missing_files}")


# 测试环境检查
@pytest.fixture(scope="session", autouse=True)
def check_test_environment():
    """
    检查测试环境
    """
    from components.log_manager import get_logger
    import requests
    
    logger = get_logger(__name__)
    
    # 检查测试服务器是否可访问
    try:
        response = requests.get(FILING_TEST_CONFIG["base_url"], timeout=10)
        if response.status_code == 200:
            logger.info("测试服务器连接正常")
        else:
            logger.warning(f"测试服务器响应异常: {response.status_code}")
    except Exception as e:
        logger.error(f"无法连接到测试服务器: {e}")
        pytest.skip("测试服务器不可访问")
    
    yield
    
    logger.info("测试环境检查完成")
