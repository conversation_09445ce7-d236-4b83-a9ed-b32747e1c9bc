{"name": "test_captcha_functionality[chromium]", "status": "passed", "description": "测试验证码功能", "steps": [{"name": "导航到登录页面", "status": "passed", "steps": [{"name": "导航到登录页面", "status": "passed", "steps": [{"name": "导航到页面: 'http://*************:8080/login'", "status": "passed", "parameters": [{"name": "url", "value": "'http://*************:8080/login'"}, {"name": "wait_until", "value": "'domcontentloaded'"}], "start": 1751852805035, "stop": 1751852806082}], "parameters": [{"name": "base_url", "value": "'http://*************:8080'"}], "start": 1751852805035, "stop": 1751852806082}, {"name": "点击账号登录标签", "status": "passed", "steps": [{"name": "智能点击: 'text=账号登录'", "status": "passed", "steps": [{"name": "等待元素稳定: 'text=账号登录'", "status": "passed", "parameters": [{"name": "locator", "value": "'text=账号登录'"}, {"name": "timeout", "value": "None"}], "start": 1751852806092, "stop": 1751852806761}], "parameters": [{"name": "locator", "value": "'text=账号登录'"}, {"name": "force", "value": "False"}, {"name": "timeout", "value": "None"}], "start": 1751852806083, "stop": 1751852806849}], "start": 1751852806082, "stop": 1751852806849}], "start": 1751852805035, "stop": 1751852806849}, {"name": "测试验证码刷新功能", "status": "passed", "steps": [{"name": "检查元素是否可见: '.captcha-image'", "status": "passed", "parameters": [{"name": "locator", "value": "'.captcha-image'"}, {"name": "timeout", "value": "None"}], "start": 1751852806849, "stop": 1751852811858}], "start": 1751852806849, "stop": 1751852811858}, {"name": "测试验证码输入", "status": "passed", "steps": [{"name": "输入验证码: '1234'", "status": "passed", "steps": [{"name": "智能填充: 'role=textbox[name='请输入图形验证码']' = '1234'", "status": "passed", "steps": [{"name": "等待元素稳定: 'role=textbox[name='请输入图形验证码']'", "status": "passed", "parameters": [{"name": "locator", "value": "'role=textbox[name='请输入图形验证码']'"}, {"name": "timeout", "value": "None"}], "start": 1751852811858, "stop": 1751852812210}], "parameters": [{"name": "locator", "value": "'role=textbox[name='请输入图形验证码']'"}, {"name": "text", "value": "'1234'"}, {"name": "clear_first", "value": "True"}, {"name": "timeout", "value": "None"}], "start": 1751852811858, "stop": 1751852812251}], "parameters": [{"name": "<PERSON><PERSON>a", "value": "'1234'"}], "start": 1751852811858, "stop": 1751852812251}], "start": 1751852811858, "stop": 1751852812255}], "attachments": [{"name": "log", "source": "f44d4f15-ed61-4c51-9382-0f4479370f27-attachment.txt", "type": "text/plain"}, {"name": "stdout", "source": "12912c09-29e8-4591-8d39-de0f02d9a796-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "browser_name", "value": "'chromium'"}], "start": 1751852804993, "stop": 1751852812255, "uuid": "bfc884bd-60c2-4685-b8bc-af9c8660536d", "historyId": "9c48d705d01d7be2c7e42debe8eca4c9", "testCaseId": "c5ee54436ca1b45fcb049d6e49e06abd", "fullName": "tests.ui.smoke.test_scf_login.TestSCFLogin#test_captcha_functionality", "labels": [{"name": "severity", "value": "normal"}, {"name": "epic", "value": "供应链金融备案系统"}, {"name": "feature", "value": "用户登录"}, {"name": "story", "value": "验证码功能测试"}, {"name": "tag", "value": "component"}, {"name": "parentSuite", "value": "tests.ui.smoke"}, {"name": "suite", "value": "test_scf_login"}, {"name": "subSuite", "value": "TestSC<PERSON><PERSON>in"}, {"name": "host", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "thread", "value": "24760-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.ui.smoke.test_scf_login"}]}