{"name": "test_captcha_functionality[chromium]", "status": "passed", "description": "测试验证码功能", "steps": [{"name": "导航到登录页面", "status": "passed", "steps": [{"name": "导航到登录页面", "status": "passed", "steps": [{"name": "导航到页面: 'http://*************:8080/login'", "status": "passed", "parameters": [{"name": "url", "value": "'http://*************:8080/login'"}, {"name": "wait_until", "value": "'domcontentloaded'"}], "start": 1751854663215, "stop": 1751854664101}], "parameters": [{"name": "base_url", "value": "'http://*************:8080'"}], "start": 1751854663215, "stop": 1751854664101}, {"name": "点击账号登录标签", "status": "passed", "steps": [{"name": "等待元素出现: 'text=账号登录'", "status": "passed", "parameters": [{"name": "locator", "value": "'text=账号登录'"}, {"name": "state", "value": "'visible'"}, {"name": "timeout", "value": "10000"}], "start": 1751854664101, "stop": 1751854664336}, {"name": "智能点击: 'text=账号登录'", "status": "passed", "steps": [{"name": "等待元素稳定: 'text=账号登录'", "status": "passed", "parameters": [{"name": "locator", "value": "'text=账号登录'"}, {"name": "timeout", "value": "None"}], "start": 1751854664337, "stop": 1751854664683}], "parameters": [{"name": "locator", "value": "'text=账号登录'"}, {"name": "force", "value": "False"}, {"name": "timeout", "value": "None"}], "start": 1751854664337, "stop": 1751854664761}], "start": 1751854664101, "stop": 1751854665775}], "start": 1751854663215, "stop": 1751854665775}, {"name": "测试验证码刷新功能", "status": "passed", "steps": [{"name": "检查元素是否可见: '.arco-image img'", "status": "passed", "parameters": [{"name": "locator", "value": "'.arco-image img'"}, {"name": "timeout", "value": "None"}], "start": 1751854665775, "stop": 1751854665788}, {"name": "刷新验证码", "status": "passed", "steps": [{"name": "等待元素出现: '#kaptchaCode'", "status": "passed", "parameters": [{"name": "locator", "value": "'#kaptchaCode'"}, {"name": "state", "value": "'visible'"}, {"name": "timeout", "value": "10000"}], "start": 1751854665788, "stop": 1751854665793}, {"name": "等待元素出现: '.arco-image img'", "status": "passed", "parameters": [{"name": "locator", "value": "'.arco-image img'"}, {"name": "state", "value": "'visible'"}, {"name": "timeout", "value": "5000"}], "start": 1751854667818, "stop": 1751854667823}], "start": 1751854665788, "stop": 1751854667823}, {"name": "检查元素是否可见: 'input[placeholder='请输入图形验证码']'", "status": "passed", "parameters": [{"name": "locator", "value": "'input[placeholder='请输入图形验证码']'"}, {"name": "timeout", "value": "None"}], "start": 1751854667823, "stop": 1751854667828}], "start": 1751854665775, "stop": 1751854667828}, {"name": "测试验证码输入", "status": "passed", "steps": [{"name": "输入图形验证码: '1234'", "status": "passed", "steps": [{"name": "智能填充: 'input[placeholder='请输入图形验证码']' = '1234'", "status": "passed", "steps": [{"name": "等待元素稳定: 'input[placeholder='请输入图形验证码']'", "status": "passed", "parameters": [{"name": "locator", "value": "'input[placeholder='请输入图形验证码']'"}, {"name": "timeout", "value": "None"}], "start": 1751854667828, "stop": 1751854668178}], "parameters": [{"name": "locator", "value": "'input[placeholder='请输入图形验证码']'"}, {"name": "text", "value": "'1234'"}, {"name": "clear_first", "value": "True"}, {"name": "timeout", "value": "None"}], "start": 1751854667828, "stop": 1751854668230}], "parameters": [{"name": "<PERSON><PERSON>a", "value": "'1234'"}], "start": 1751854667828, "stop": 1751854668230}], "start": 1751854667828, "stop": 1751854668234}], "attachments": [{"name": "log", "source": "bda1d0fd-71a0-459a-b5e1-c1d2293790b6-attachment.txt", "type": "text/plain"}, {"name": "stdout", "source": "6f63cb75-9dbe-479b-aad1-225045f3a719-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "browser_name", "value": "'chromium'"}], "start": 1751854663172, "stop": 1751854668235, "uuid": "f96ead92-4ed6-4faf-8367-2535e002f8ca", "historyId": "9c48d705d01d7be2c7e42debe8eca4c9", "testCaseId": "c5ee54436ca1b45fcb049d6e49e06abd", "fullName": "tests.ui.smoke.test_scf_login.TestSCFLogin#test_captcha_functionality", "labels": [{"name": "story", "value": "验证码功能测试"}, {"name": "severity", "value": "normal"}, {"name": "feature", "value": "用户登录"}, {"name": "epic", "value": "供应链金融备案系统"}, {"name": "tag", "value": "component"}, {"name": "parentSuite", "value": "tests.ui.smoke"}, {"name": "suite", "value": "test_scf_login"}, {"name": "subSuite", "value": "TestSC<PERSON><PERSON>in"}, {"name": "host", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "thread", "value": "26480-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.ui.smoke.test_scf_login"}]}