{"name": "test_minimal_registration_process[chromium]", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: Locator.wait_for: Unexpected token \"=\" while parsing css selector \"#bcVele text=否\". Did you mean to CSS.escape it?\nCall log:\n  - waiting for #bcVele text=否 to be visible", "trace": "test_scf_registration.py:40: in test_minimal_registration_process\n    workflow.complete_registration_process(\n..\\..\\..\\workflows\\scf_registration_workflow.py:59: in complete_registration_process\n    self._fill_business_compliance(application_data.get(\"business_compliance\", {}))\n..\\..\\..\\workflows\\scf_registration_workflow.py:169: in _fill_business_compliance\n    self.registration_page.fill_business_compliance_form(compliance_data)\n..\\..\\..\\pages\\scf_registration_page.py:249: in fill_business_compliance_form\n    self.select_yes_no_option(field, option, description)\n..\\..\\..\\pages\\scf_registration_page.py:126: in select_yes_no_option\n    self.click(option_selector)\n..\\..\\..\\components\\retry_util.py:135: in wrapper\n    raise last_exception\n..\\..\\..\\components\\retry_util.py:88: in wrapper\n    result = func(*args, **kwargs)\n             ^^^^^^^^^^^^^^^^^^^^^\n..\\..\\..\\pages\\base_page.py:107: in click\n    element = self.wait_for_element_stable(locator)\n              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n..\\..\\..\\pages\\base_page.py:50: in wait_for_element_stable\n    element.wait_for(state=\"visible\", timeout=self.default_timeout)\n..\\..\\..\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py:17937: in wait_for\n    self._sync(self._impl_obj.wait_for(timeout=timeout, state=state))\n..\\..\\..\\.venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py:693: in wait_for\n    await self._frame.wait_for_selector(\n..\\..\\..\\.venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py:341: in wait_for_selector\n    await self._channel.send(\n..\\..\\..\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:69: in send\n    return await self._connection.wrap_api_call(\n..\\..\\..\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:558: in wrap_api_call\n    raise rewrite_error(error, f\"{parsed_st['apiName']}: {error}\") from None\nE   playwright._impl._errors.Error: Locator.wait_for: Unexpected token \"=\" while parsing css selector \"#bcVele text=否\". Did you mean to CSS.escape it?\nE   Call log:\nE     - waiting for #bcVele text=否 to be visible"}, "description": "测试最小化备案申请流程", "steps": [{"name": "执行最小化备案申请流程", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: Locator.wait_for: Unexpected token \"=\" while parsing css selector \"#bcVele text=否\". Did you mean to CSS.escape it?\nCall log:\n  - waiting for #bcVele text=否 to be visible\n\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\tests\\ui\\smoke\\test_scf_registration.py\", line 40, in test_minimal_registration_process\n    workflow.complete_registration_process(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\workflows\\scf_registration_workflow.py\", line 59, in complete_registration_process\n    self._fill_business_compliance(application_data.get(\"business_compliance\", {}))\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\workflows\\scf_registration_workflow.py\", line 169, in _fill_business_compliance\n    self.registration_page.fill_business_compliance_form(compliance_data)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_registration_page.py\", line 249, in fill_business_compliance_form\n    self.select_yes_no_option(field, option, description)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_registration_page.py\", line 126, in select_yes_no_option\n    self.click(option_selector)\n  File \"G:\\nifa\\playwright-python-template\\components\\retry_util.py\", line 135, in wrapper\n    raise last_exception\n  File \"G:\\nifa\\playwright-python-template\\components\\retry_util.py\", line 88, in wrapper\n    result = func(*args, **kwargs)\n             ^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 107, in click\n    element = self.wait_for_element_stable(locator)\n              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 50, in wait_for_element_stable\n    element.wait_for(state=\"visible\", timeout=self.default_timeout)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 17937, in wait_for\n    self._sync(self._impl_obj.wait_for(timeout=timeout, state=state))\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 115, in _sync\n    return task.result()\n           ^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py\", line 693, in wait_for\n    await self._frame.wait_for_selector(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py\", line 341, in wait_for_selector\n    await self._channel.send(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 69, in send\n    return await self._connection.wrap_api_call(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 558, in wrap_api_call\n    raise rewrite_error(error, f\"{parsed_st['apiName']}: {error}\") from None\n"}, "steps": [{"name": "执行完整的备案申请流程", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: Locator.wait_for: Unexpected token \"=\" while parsing css selector \"#bcVele text=否\". Did you mean to CSS.escape it?\nCall log:\n  - waiting for #bcVele text=否 to be visible\n\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\workflows\\scf_registration_workflow.py\", line 59, in complete_registration_process\n    self._fill_business_compliance(application_data.get(\"business_compliance\", {}))\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\workflows\\scf_registration_workflow.py\", line 169, in _fill_business_compliance\n    self.registration_page.fill_business_compliance_form(compliance_data)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_registration_page.py\", line 249, in fill_business_compliance_form\n    self.select_yes_no_option(field, option, description)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_registration_page.py\", line 126, in select_yes_no_option\n    self.click(option_selector)\n  File \"G:\\nifa\\playwright-python-template\\components\\retry_util.py\", line 135, in wrapper\n    raise last_exception\n  File \"G:\\nifa\\playwright-python-template\\components\\retry_util.py\", line 88, in wrapper\n    result = func(*args, **kwargs)\n             ^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 107, in click\n    element = self.wait_for_element_stable(locator)\n              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 50, in wait_for_element_stable\n    element.wait_for(state=\"visible\", timeout=self.default_timeout)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 17937, in wait_for\n    self._sync(self._impl_obj.wait_for(timeout=timeout, state=state))\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 115, in _sync\n    return task.result()\n           ^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py\", line 693, in wait_for\n    await self._frame.wait_for_selector(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py\", line 341, in wait_for_selector\n    await self._channel.send(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 69, in send\n    return await self._connection.wrap_api_call(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 558, in wrap_api_call\n    raise rewrite_error(error, f\"{parsed_st['apiName']}: {error}\") from None\n"}, "steps": [{"name": "登录系统", "status": "passed", "steps": [{"name": "导航到登录页面", "status": "passed", "steps": [{"name": "导航到页面: 'http://*************:8080/login'", "status": "passed", "parameters": [{"name": "url", "value": "'http://*************:8080/login'"}, {"name": "wait_until", "value": "'domcontentloaded'"}], "start": 1751856081721, "stop": 1751856083098}], "parameters": [{"name": "base_url", "value": "'http://*************:8080'"}], "start": 1751856081721, "stop": 1751856083098}, {"name": "执行登录操作", "status": "passed", "steps": [{"name": "执行登录操作（带重试机制）", "status": "passed", "steps": [{"name": "点击账号登录标签", "status": "passed", "steps": [{"name": "等待元素出现: 'text=账号登录'", "status": "passed", "parameters": [{"name": "locator", "value": "'text=账号登录'"}, {"name": "state", "value": "'visible'"}, {"name": "timeout", "value": "10000"}], "start": 1751856083099, "stop": 1751856083125}, {"name": "智能点击: 'text=账号登录'", "status": "passed", "steps": [{"name": "等待元素稳定: 'text=账号登录'", "status": "passed", "parameters": [{"name": "locator", "value": "'text=账号登录'"}, {"name": "timeout", "value": "None"}], "start": 1751856083125, "stop": 1751856083469}], "parameters": [{"name": "locator", "value": "'text=账号登录'"}, {"name": "force", "value": "False"}, {"name": "timeout", "value": "None"}], "start": 1751856083125, "stop": 1751856084531}], "start": 1751856083099, "stop": 1751856085544}, {"name": "输入用户名: 'scf_4nuioc'", "status": "passed", "steps": [{"name": "智能填充: 'input[placeholder='请输入账号']' = 'scf_4nuioc'", "status": "passed", "steps": [{"name": "等待元素稳定: 'input[placeholder='请输入账号']'", "status": "passed", "parameters": [{"name": "locator", "value": "'input[placeholder='请输入账号']'"}, {"name": "timeout", "value": "None"}], "start": 1751856085545, "stop": 1751856085896}], "parameters": [{"name": "locator", "value": "'input[placeholder='请输入账号']'"}, {"name": "text", "value": "'scf_4nuioc'"}, {"name": "clear_first", "value": "True"}, {"name": "timeout", "value": "None"}], "start": 1751856085545, "stop": 1751856087462}], "parameters": [{"name": "username", "value": "'scf_4nuioc'"}], "start": 1751856085544, "stop": 1751856087462}, {"name": "输入密码", "status": "passed", "steps": [{"name": "智能填充: 'input[type='password']' = 'Scf123456.'", "status": "passed", "steps": [{"name": "等待元素稳定: 'input[type='password']'", "status": "passed", "parameters": [{"name": "locator", "value": "'input[type='password']'"}, {"name": "timeout", "value": "None"}], "start": 1751856087463, "stop": 1751856087811}], "parameters": [{"name": "locator", "value": "'input[type='password']'"}, {"name": "text", "value": "'Scf123456.'"}, {"name": "clear_first", "value": "True"}, {"name": "timeout", "value": "None"}], "start": 1751856087463, "stop": 1751856089361}], "parameters": [{"name": "password", "value": "'Scf123456.'"}], "start": 1751856087462, "stop": 1751856089361}, {"name": "自动识别并输入验证码", "status": "passed", "steps": [{"name": "截取并保存验证码图片", "status": "passed", "steps": [{"name": "等待元素出现: '.arco-image img'", "status": "passed", "parameters": [{"name": "locator", "value": "'.arco-image img'"}, {"name": "state", "value": "'visible'"}, {"name": "timeout", "value": "10000"}], "start": 1751856089363, "stop": 1751856089374}], "attachments": [{"name": "验证码图片_尝试1", "source": "1d0f42bf-acb6-41d9-8d44-86c4e7b8f1df-attachment.png", "type": "image/png"}], "parameters": [{"name": "attempt", "value": "1"}], "start": 1751856089362, "stop": 1751856090452}, {"name": "OCR识别验证码", "status": "passed", "parameters": [{"name": "captcha_image", "value": "<class 'bytes'>"}, {"name": "attempt", "value": "1"}], "start": 1751856090452, "stop": 1751856090468}, {"name": "输入图形验证码: '3055'", "status": "passed", "steps": [{"name": "智能填充: 'input[placeholder='请输入图形验证码']' = '3055'", "status": "passed", "steps": [{"name": "等待元素稳定: 'input[placeholder='请输入图形验证码']'", "status": "passed", "parameters": [{"name": "locator", "value": "'input[placeholder='请输入图形验证码']'"}, {"name": "timeout", "value": "None"}], "start": 1751856090468, "stop": 1751856090813}], "parameters": [{"name": "locator", "value": "'input[placeholder='请输入图形验证码']'"}, {"name": "text", "value": "'3055'"}, {"name": "clear_first", "value": "True"}, {"name": "timeout", "value": "None"}], "start": 1751856090468, "stop": 1751856092382}], "parameters": [{"name": "<PERSON><PERSON>a", "value": "'3055'"}], "start": 1751856090468, "stop": 1751856092382}], "parameters": [{"name": "max_retries", "value": "1"}, {"name": "min_confidence", "value": "0.6"}], "start": 1751856089362, "stop": 1751856092382}, {"name": "点击登录按钮", "status": "passed", "steps": [{"name": "智能点击: 'button[type='submit']'", "status": "passed", "steps": [{"name": "等待元素稳定: 'button[type='submit']'", "status": "passed", "parameters": [{"name": "locator", "value": "'button[type='submit']'"}, {"name": "timeout", "value": "None"}], "start": 1751856092383, "stop": 1751856092740}], "parameters": [{"name": "locator", "value": "'button[type='submit']'"}, {"name": "force", "value": "False"}, {"name": "timeout", "value": "None"}], "start": 1751856092383, "stop": 1751856093848}], "start": 1751856092382, "stop": 1751856093848}, {"name": "检查登录状态", "status": "passed", "parameters": [{"name": "timeout", "value": "5000"}], "start": 1751856093848, "stop": 1751856094861}], "parameters": [{"name": "username", "value": "'scf_4nuioc'"}, {"name": "password", "value": "'Scf123456.'"}, {"name": "max_captcha_retries", "value": "5"}], "start": 1751856083099, "stop": 1751856094862}], "parameters": [{"name": "username", "value": "'scf_4nuioc'"}, {"name": "password", "value": "'Scf123456.'"}, {"name": "<PERSON><PERSON>a", "value": "None"}, {"name": "auto_captcha", "value": "True"}], "start": 1751856083099, "stop": 1751856094862}, {"name": "验证登录成功", "status": "passed", "steps": [{"name": "等待页面加载完成", "status": "passed", "parameters": [{"name": "state", "value": "'networkidle'"}, {"name": "timeout", "value": "None"}], "start": 1751856094862, "stop": 1751856094863}], "parameters": [{"name": "expected_url_pattern", "value": "None"}], "start": 1751856094862, "stop": 1751856094864}], "parameters": [{"name": "credentials", "value": "{'username': 'scf_4nuioc', 'password': 'Scf123456.', 'description': '标准测试用户'}"}, {"name": "base_url", "value": "'http://*************:8080'"}], "start": 1751856081720, "stop": 1751856094864}, {"name": "进入备案申请", "status": "passed", "steps": [{"name": "点击备案登记菜单", "status": "passed", "steps": [{"name": "智能点击: 'text=备案登记'", "status": "passed", "steps": [{"name": "等待元素稳定: 'text=备案登记'", "status": "passed", "parameters": [{"name": "locator", "value": "'text=备案登记'"}, {"name": "timeout", "value": "None"}], "start": 1751856094864, "stop": 1751856095215}], "parameters": [{"name": "locator", "value": "'text=备案登记'"}, {"name": "force", "value": "False"}, {"name": "timeout", "value": "None"}], "start": 1751856094864, "stop": 1751856096281}], "start": 1751856094864, "stop": 1751856096281}, {"name": "点击备案申请按钮", "status": "passed", "steps": [{"name": "智能点击: 'role=button[name='备案申请']'", "status": "passed", "steps": [{"name": "等待元素稳定: 'role=button[name='备案申请']'", "status": "passed", "parameters": [{"name": "locator", "value": "'role=button[name='备案申请']'"}, {"name": "timeout", "value": "None"}], "start": 1751856096282, "stop": 1751856096629}], "parameters": [{"name": "locator", "value": "'role=button[name='备案申请']'"}, {"name": "force", "value": "False"}, {"name": "timeout", "value": "None"}], "start": 1751856096282, "stop": 1751856097916}], "start": 1751856096281, "stop": 1751856097916}], "start": 1751856094864, "stop": 1751856097916}, {"name": "填写基础材料", "status": "passed", "steps": [{"name": "批量上传文件到指定区域", "status": "passed", "steps": [{"name": "上传文件到 bfmArtAssocId: data/test_files/异常信息共享系统V1.5需求.docx", "status": "passed", "steps": [{"name": "检查元素是否可见: '#bfmArtAssocId text=点击此处上传附件'", "status": "passed", "parameters": [{"name": "locator", "value": "'#bfmArtAssocId text=点击此处上传附件'"}, {"name": "timeout", "value": "None"}], "start": 1751856097917, "stop": 1751856097921}], "start": 1751856097917, "stop": 1751856097921}], "parameters": [{"name": "file_uploads", "value": "{'bfmArtAssocId': 'data/test_files/异常信息共享系统V1.5需求.docx'}"}], "start": 1751856097917, "stop": 1751856097921}, {"name": "点击下一步", "status": "passed", "steps": [{"name": "智能点击: 'role=button[name='下一步']'", "status": "passed", "steps": [{"name": "等待元素稳定: 'role=button[name='下一步']'", "status": "passed", "parameters": [{"name": "locator", "value": "'role=button[name='下一步']'"}, {"name": "timeout", "value": "None"}], "start": 1751856097921, "stop": 1751856098280}], "parameters": [{"name": "locator", "value": "'role=button[name='下一步']'"}, {"name": "force", "value": "False"}, {"name": "timeout", "value": "None"}], "start": 1751856097921, "stop": 1751856099350}, {"name": "等待页面加载完成", "status": "passed", "parameters": [{"name": "state", "value": "'networkidle'"}, {"name": "timeout", "value": "None"}], "start": 1751856099350, "stop": 1751856099352}], "start": 1751856097921, "stop": 1751856099352}], "parameters": [{"name": "materials_data", "value": "{'file_uploads': {'bfmArtAssocId': 'data/test_files/异常信息共享系统V1.5需求.docx'}}"}], "start": 1751856097916, "stop": 1751856099352}, {"name": "填写业务合规信息", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: Locator.wait_for: Unexpected token \"=\" while parsing css selector \"#bcVele text=否\". Did you mean to CSS.escape it?\nCall log:\n  - waiting for #bcVele text=否 to be visible\n\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\workflows\\scf_registration_workflow.py\", line 169, in _fill_business_compliance\n    self.registration_page.fill_business_compliance_form(compliance_data)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_registration_page.py\", line 249, in fill_business_compliance_form\n    self.select_yes_no_option(field, option, description)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_registration_page.py\", line 126, in select_yes_no_option\n    self.click(option_selector)\n  File \"G:\\nifa\\playwright-python-template\\components\\retry_util.py\", line 135, in wrapper\n    raise last_exception\n  File \"G:\\nifa\\playwright-python-template\\components\\retry_util.py\", line 88, in wrapper\n    result = func(*args, **kwargs)\n             ^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 107, in click\n    element = self.wait_for_element_stable(locator)\n              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 50, in wait_for_element_stable\n    element.wait_for(state=\"visible\", timeout=self.default_timeout)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 17937, in wait_for\n    self._sync(self._impl_obj.wait_for(timeout=timeout, state=state))\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 115, in _sync\n    return task.result()\n           ^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py\", line 693, in wait_for\n    await self._frame.wait_for_selector(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py\", line 341, in wait_for_selector\n    await self._channel.send(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 69, in send\n    return await self._connection.wrap_api_call(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 558, in wrap_api_call\n    raise rewrite_error(error, f\"{parsed_st['apiName']}: {error}\") from None\n"}, "steps": [{"name": "填写业务合规表单", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: Locator.wait_for: Unexpected token \"=\" while parsing css selector \"#bcVele text=否\". Did you mean to CSS.escape it?\nCall log:\n  - waiting for #bcVele text=否 to be visible\n\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_registration_page.py\", line 249, in fill_business_compliance_form\n    self.select_yes_no_option(field, option, description)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_registration_page.py\", line 126, in select_yes_no_option\n    self.click(option_selector)\n  File \"G:\\nifa\\playwright-python-template\\components\\retry_util.py\", line 135, in wrapper\n    raise last_exception\n  File \"G:\\nifa\\playwright-python-template\\components\\retry_util.py\", line 88, in wrapper\n    result = func(*args, **kwargs)\n             ^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 107, in click\n    element = self.wait_for_element_stable(locator)\n              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 50, in wait_for_element_stable\n    element.wait_for(state=\"visible\", timeout=self.default_timeout)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 17937, in wait_for\n    self._sync(self._impl_obj.wait_for(timeout=timeout, state=state))\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 115, in _sync\n    return task.result()\n           ^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py\", line 693, in wait_for\n    await self._frame.wait_for_selector(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py\", line 341, in wait_for_selector\n    await self._channel.send(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 69, in send\n    return await self._connection.wrap_api_call(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 558, in wrap_api_call\n    raise rewrite_error(error, f\"{parsed_st['apiName']}: {error}\") from None\n"}, "steps": [{"name": "选择是/否选项: '否' 在区域 'bcVele'", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: Locator.wait_for: Unexpected token \"=\" while parsing css selector \"#bcVele text=否\". Did you mean to CSS.escape it?\nCall log:\n  - waiting for #bcVele text=否 to be visible\n\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_registration_page.py\", line 126, in select_yes_no_option\n    self.click(option_selector)\n  File \"G:\\nifa\\playwright-python-template\\components\\retry_util.py\", line 135, in wrapper\n    raise last_exception\n  File \"G:\\nifa\\playwright-python-template\\components\\retry_util.py\", line 88, in wrapper\n    result = func(*args, **kwargs)\n             ^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 107, in click\n    element = self.wait_for_element_stable(locator)\n              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 50, in wait_for_element_stable\n    element.wait_for(state=\"visible\", timeout=self.default_timeout)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 17937, in wait_for\n    self._sync(self._impl_obj.wait_for(timeout=timeout, state=state))\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 115, in _sync\n    return task.result()\n           ^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py\", line 693, in wait_for\n    await self._frame.wait_for_selector(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py\", line 341, in wait_for_selector\n    await self._channel.send(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 69, in send\n    return await self._connection.wrap_api_call(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 558, in wrap_api_call\n    raise rewrite_error(error, f\"{parsed_st['apiName']}: {error}\") from None\n"}, "steps": [{"name": "智能点击: '#bcVele text=否'", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: Locator.wait_for: Unexpected token \"=\" while parsing css selector \"#bcVele text=否\". Did you mean to CSS.escape it?\nCall log:\n  - waiting for #bcVele text=否 to be visible\n\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 107, in click\n    element = self.wait_for_element_stable(locator)\n              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 50, in wait_for_element_stable\n    element.wait_for(state=\"visible\", timeout=self.default_timeout)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 17937, in wait_for\n    self._sync(self._impl_obj.wait_for(timeout=timeout, state=state))\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 115, in _sync\n    return task.result()\n           ^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py\", line 693, in wait_for\n    await self._frame.wait_for_selector(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py\", line 341, in wait_for_selector\n    await self._channel.send(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 69, in send\n    return await self._connection.wrap_api_call(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 558, in wrap_api_call\n    raise rewrite_error(error, f\"{parsed_st['apiName']}: {error}\") from None\n"}, "steps": [{"name": "等待元素稳定: '#bcVele text=否'", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: Locator.wait_for: Unexpected token \"=\" while parsing css selector \"#bcVele text=否\". Did you mean to CSS.escape it?\nCall log:\n  - waiting for #bcVele text=否 to be visible\n\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 50, in wait_for_element_stable\n    element.wait_for(state=\"visible\", timeout=self.default_timeout)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 17937, in wait_for\n    self._sync(self._impl_obj.wait_for(timeout=timeout, state=state))\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 115, in _sync\n    return task.result()\n           ^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py\", line 693, in wait_for\n    await self._frame.wait_for_selector(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py\", line 341, in wait_for_selector\n    await self._channel.send(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 69, in send\n    return await self._connection.wrap_api_call(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 558, in wrap_api_call\n    raise rewrite_error(error, f\"{parsed_st['apiName']}: {error}\") from None\n"}, "parameters": [{"name": "locator", "value": "'#bcVele text=否'"}, {"name": "timeout", "value": "None"}], "start": 1751856099353, "stop": 1751856099356}], "parameters": [{"name": "locator", "value": "'#bcVele text=否'"}, {"name": "force", "value": "False"}, {"name": "timeout", "value": "None"}], "start": 1751856099353, "stop": 1751856099356}, {"name": "智能点击: '#bcVele text=否'", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: Locator.wait_for: Unexpected token \"=\" while parsing css selector \"#bcVele text=否\". Did you mean to CSS.escape it?\nCall log:\n  - waiting for #bcVele text=否 to be visible\n\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 107, in click\n    element = self.wait_for_element_stable(locator)\n              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 50, in wait_for_element_stable\n    element.wait_for(state=\"visible\", timeout=self.default_timeout)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 17937, in wait_for\n    self._sync(self._impl_obj.wait_for(timeout=timeout, state=state))\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 115, in _sync\n    return task.result()\n           ^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py\", line 693, in wait_for\n    await self._frame.wait_for_selector(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py\", line 341, in wait_for_selector\n    await self._channel.send(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 69, in send\n    return await self._connection.wrap_api_call(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 558, in wrap_api_call\n    raise rewrite_error(error, f\"{parsed_st['apiName']}: {error}\") from None\n"}, "steps": [{"name": "等待元素稳定: '#bcVele text=否'", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: Locator.wait_for: Unexpected token \"=\" while parsing css selector \"#bcVele text=否\". Did you mean to CSS.escape it?\nCall log:\n  - waiting for #bcVele text=否 to be visible\n\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 50, in wait_for_element_stable\n    element.wait_for(state=\"visible\", timeout=self.default_timeout)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 17937, in wait_for\n    self._sync(self._impl_obj.wait_for(timeout=timeout, state=state))\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 115, in _sync\n    return task.result()\n           ^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py\", line 693, in wait_for\n    await self._frame.wait_for_selector(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py\", line 341, in wait_for_selector\n    await self._channel.send(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 69, in send\n    return await self._connection.wrap_api_call(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 558, in wrap_api_call\n    raise rewrite_error(error, f\"{parsed_st['apiName']}: {error}\") from None\n"}, "parameters": [{"name": "locator", "value": "'#bcVele text=否'"}, {"name": "timeout", "value": "None"}], "start": 1751856099835, "stop": 1751856099839}], "parameters": [{"name": "locator", "value": "'#bcVele text=否'"}, {"name": "force", "value": "False"}, {"name": "timeout", "value": "None"}], "start": 1751856099834, "stop": 1751856099839}, {"name": "智能点击: '#bcVele text=否'", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: Locator.wait_for: Unexpected token \"=\" while parsing css selector \"#bcVele text=否\". Did you mean to CSS.escape it?\nCall log:\n  - waiting for #bcVele text=否 to be visible\n\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 107, in click\n    element = self.wait_for_element_stable(locator)\n              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 50, in wait_for_element_stable\n    element.wait_for(state=\"visible\", timeout=self.default_timeout)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 17937, in wait_for\n    self._sync(self._impl_obj.wait_for(timeout=timeout, state=state))\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 115, in _sync\n    return task.result()\n           ^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py\", line 693, in wait_for\n    await self._frame.wait_for_selector(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py\", line 341, in wait_for_selector\n    await self._channel.send(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 69, in send\n    return await self._connection.wrap_api_call(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 558, in wrap_api_call\n    raise rewrite_error(error, f\"{parsed_st['apiName']}: {error}\") from None\n"}, "steps": [{"name": "等待元素稳定: '#bcVele text=否'", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: Locator.wait_for: Unexpected token \"=\" while parsing css selector \"#bcVele text=否\". Did you mean to CSS.escape it?\nCall log:\n  - waiting for #bcVele text=否 to be visible\n\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 50, in wait_for_element_stable\n    element.wait_for(state=\"visible\", timeout=self.default_timeout)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 17937, in wait_for\n    self._sync(self._impl_obj.wait_for(timeout=timeout, state=state))\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 115, in _sync\n    return task.result()\n           ^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py\", line 693, in wait_for\n    await self._frame.wait_for_selector(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py\", line 341, in wait_for_selector\n    await self._channel.send(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 69, in send\n    return await self._connection.wrap_api_call(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 558, in wrap_api_call\n    raise rewrite_error(error, f\"{parsed_st['apiName']}: {error}\") from None\n"}, "parameters": [{"name": "locator", "value": "'#bcVele text=否'"}, {"name": "timeout", "value": "None"}], "start": 1751856100784, "stop": 1751856100788}], "parameters": [{"name": "locator", "value": "'#bcVele text=否'"}, {"name": "force", "value": "False"}, {"name": "timeout", "value": "None"}], "start": 1751856100784, "stop": 1751856100788}], "parameters": [{"name": "area_id", "value": "'bc<PERSON><PERSON>'"}, {"name": "option", "value": "'否'"}, {"name": "description", "value": "'最小化测试'"}], "start": 1751856099353, "stop": 1751856100789}], "parameters": [{"name": "form_data", "value": "{'bcVele': {'option': '否', 'description': '最小化测试'}}"}], "start": 1751856099353, "stop": 1751856100790}], "parameters": [{"name": "compliance_data", "value": "{'bcVele': {'option': '否', 'description': '最小化测试'}}"}], "start": 1751856099352, "stop": 1751856100791}, {"name": "截图并附加到报告", "status": "passed", "attachments": [{"name": "备案申请流程失败", "source": "6aa49b62-8ff0-4bf8-8fe4-f08a8377dda0-attachment.png", "type": "image/png"}], "parameters": [{"name": "name", "value": "'备案申请流程失败'"}], "start": 1751856100791, "stop": 1751856100923}], "parameters": [{"name": "login_credentials", "value": "{'username': 'scf_4nuioc', 'password': 'Scf123456.', 'description': '标准测试用户'}"}, {"name": "application_data", "value": "{'basic_materials': {'file_uploads': {'bfmArtAssocId': 'data/test_files/异常信息共享系统V1.5需求.docx'}}, 'business_compliance': {'bcVele': {'option': '否', 'description': '最小化测试'}}, 'institution': '天津中互金数据科技有限公司'}"}, {"name": "base_url", "value": "'http://*************:8080'"}], "start": 1751856081720, "stop": 1751856100923}], "start": 1751856081720, "stop": 1751856100925}], "attachments": [{"name": "失败截图", "source": "eaec7514-f58f-4f77-90dc-63c1351c3b9f-attachment.png", "type": "image/png"}, {"name": "页面HTML", "source": "f0be512a-7270-4ae7-946e-ae3ee505aee9-attachment.html", "type": "text/html"}, {"name": "当前URL", "source": "bbc7ebcd-956b-4ae2-a6d1-39aa8ee1dada-attachment.txt", "type": "text/plain"}, {"name": "log", "source": "fadf683a-624f-4640-a8de-7162e993514c-attachment.txt", "type": "text/plain"}, {"name": "stdout", "source": "cdf2d28a-55e9-4dd0-a599-a07259e11994-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "browser_name", "value": "'chromium'"}], "start": 1751856081658, "stop": 1751856100925, "uuid": "c7d2fd0a-8154-46b1-a5a9-4c2e80075d3c", "historyId": "51ce2efbdcb9bf9576a5ef0f28a8c2b1", "testCaseId": "2a131723a09418cae3744a996cf6d25c", "fullName": "tests.ui.smoke.test_scf_registration.TestSCFRegistration#test_minimal_registration_process", "labels": [{"name": "epic", "value": "供应链金融备案系统"}, {"name": "severity", "value": "blocker"}, {"name": "feature", "value": "备案申请"}, {"name": "story", "value": "最小化备案申请流程"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "tests.ui.smoke"}, {"name": "suite", "value": "test_scf_registration"}, {"name": "subSuite", "value": "TestSCFRegistration"}, {"name": "host", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "thread", "value": "26448-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.ui.smoke.test_scf_registration"}]}