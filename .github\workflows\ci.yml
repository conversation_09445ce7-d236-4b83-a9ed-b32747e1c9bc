name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:
    inputs:
      test_suite:
        description: '测试套件选择'
        required: false
        default: 'all'
        type: choice
        options:
          - all
          - smoke
          - regression
          - e2e
      parallel:
        description: '是否并行执行'
        required: false
        default: true
        type: boolean

env:
  PYTHON_VERSION: '3.12'
  UV_CACHE_DIR: /tmp/.uv-cache

jobs:
  # 代码质量检查
  quality-check:
    name: 代码质量检查
    runs-on: ubuntu-latest
    steps:
      - name: Checkout代码
        uses: actions/checkout@v4

      - name: 安装uv
        uses: astral-sh/setup-uv@v3
        with:
          version: "latest"

      - name: 设置Python
        run: uv python install ${{ env.PYTHON_VERSION }}

      - name: 创建虚拟环境
        run: uv venv

      - name: 安装依赖
        run: uv pip install -e ".[dev]"

      - name: 运行Ruff检查
        run: uv run ruff check .

      - name: 运行Ruff格式检查
        run: uv run ruff format --check .

      - name: 运行Black格式检查
        run: uv run black --check .

      - name: 运行MyPy类型检查
        run: uv run mypy . --ignore-missing-imports

  # 单元测试
  unit-tests:
    name: 单元测试
    runs-on: ubuntu-latest
    needs: quality-check
    steps:
      - name: Checkout代码
        uses: actions/checkout@v4

      - name: 安装uv
        uses: astral-sh/setup-uv@v3

      - name: 设置Python
        run: uv python install ${{ env.PYTHON_VERSION }}

      - name: 安装依赖
        run: uv pip install -e ".[dev]"

      - name: 运行单元测试
        run: |
          uv run pytest tests/unit/ \
            --cov=components \
            --cov=pages \
            --cov=workflows \
            --cov-report=xml \
            --cov-report=html \
            -v

      - name: 上传覆盖率报告
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage.xml
          flags: unittests
          name: codecov-umbrella

  # E2E测试矩阵
  e2e-tests:
    name: E2E测试
    runs-on: ubuntu-latest
    needs: quality-check
    strategy:
      fail-fast: false
      matrix:
        browser: [chromium, firefox, webkit]
        test-suite: [smoke, component, e2e]
        exclude:
          # 排除一些组合以节省资源
          - browser: webkit
            test-suite: component
          - browser: firefox
            test-suite: e2e
    
    steps:
      - name: Checkout代码
        uses: actions/checkout@v4

      - name: 安装uv
        uses: astral-sh/setup-uv@v3

      - name: 设置Python
        run: uv python install ${{ env.PYTHON_VERSION }}

      - name: 安装依赖
        run: uv pip install -e ".[dev]"

      - name: 安装Playwright浏览器
        run: uv run playwright install ${{ matrix.browser }} --with-deps

      - name: 启动测试应用
        run: |
          cd test-app
          python -m http.server 8080 &
          sleep 5

      - name: 运行E2E测试
        run: |
          TEST_SUITE="${{ matrix.test-suite }}"
          BROWSER="${{ matrix.browser }}"
          PARALLEL="${{ github.event.inputs.parallel || 'true' }}"
          
          if [ "$PARALLEL" = "true" ]; then
            PARALLEL_ARGS="-n auto"
          else
            PARALLEL_ARGS=""
          fi
          
          uv run pytest tests/ui/$TEST_SUITE/ \
            --browser=$BROWSER \
            --env=ci \
            --alluredir=allure-results-$BROWSER-$TEST_SUITE \
            --tb=short \
            --maxfail=5 \
            $PARALLEL_ARGS \
            -v

      - name: 上传测试结果
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: allure-results-${{ matrix.browser }}-${{ matrix.test-suite }}
          path: allure-results-${{ matrix.browser }}-${{ matrix.test-suite }}
          retention-days: 30

      - name: 上传失败截图
        uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: screenshots-${{ matrix.browser }}-${{ matrix.test-suite }}
          path: |
            logs/
            test-results/
          retention-days: 7

  # 生成测试报告
  generate-report:
    name: 生成测试报告
    runs-on: ubuntu-latest
    needs: [unit-tests, e2e-tests]
    if: always()
    steps:
      - name: Checkout代码
        uses: actions/checkout@v4

      - name: 下载所有测试结果
        uses: actions/download-artifact@v4
        with:
          path: allure-results

      - name: 合并测试结果
        run: |
          mkdir -p merged-results
          find allure-results -name "*.json" -exec cp {} merged-results/ \;
          find allure-results -name "*.txt" -exec cp {} merged-results/ \;
          find allure-results -name "*.properties" -exec cp {} merged-results/ \;

      - name: 生成Allure报告
        uses: simple-elf/allure-report-action@master
        if: always()
        with:
          allure_results: merged-results
          allure_report: allure-report
          gh_pages: gh-pages
          allure_history: allure-history

      - name: 部署报告到GitHub Pages
        uses: peaceiris/actions-gh-pages@v3
        if: github.ref == 'refs/heads/main'
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: allure-history
          destination_dir: reports

      - name: 评论PR报告链接
        uses: actions/github-script@v7
        if: github.event_name == 'pull_request'
        with:
          script: |
            const reportUrl = `https://${{ github.repository_owner }}.github.io/${{ github.event.repository.name }}/reports/`;
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: `## 📊 测试报告\n\n测试报告已生成: [查看详细报告](${reportUrl})\n\n- 🔍 包含所有测试结果和失败详情\n- 📸 失败用例的截图和轨迹\n- 📈 测试趋势和历史数据`
            });

  # 性能测试（可选）
  performance-tests:
    name: 性能测试
    runs-on: ubuntu-latest
    needs: quality-check
    if: github.ref == 'refs/heads/main' || contains(github.event.pull_request.labels.*.name, 'performance')
    steps:
      - name: Checkout代码
        uses: actions/checkout@v4

      - name: 安装uv
        uses: astral-sh/setup-uv@v3

      - name: 设置Python
        run: uv python install ${{ env.PYTHON_VERSION }}

      - name: 安装依赖
        run: uv pip install -e ".[dev]"

      - name: 安装Playwright
        run: uv run playwright install chromium --with-deps

      - name: 运行性能测试
        run: |
          uv run pytest tests/performance/ \
            --env=ci \
            --alluredir=allure-results-performance \
            -v

      - name: 上传性能测试结果
        uses: actions/upload-artifact@v4
        with:
          name: performance-results
          path: allure-results-performance

  # 安全扫描
  security-scan:
    name: 安全扫描
    runs-on: ubuntu-latest
    steps:
      - name: Checkout代码
        uses: actions/checkout@v4

      - name: 运行Bandit安全扫描
        uses: tj-actions/bandit@v5.1
        with:
          options: "-r . -f json -o bandit-report.json"

      - name: 上传安全扫描结果
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: security-scan-results
          path: bandit-report.json

  # 通知
  notify:
    name: 通知
    runs-on: ubuntu-latest
    needs: [quality-check, unit-tests, e2e-tests, generate-report]
    if: always()
    steps:
      - name: 发送成功通知
        if: needs.e2e-tests.result == 'success'
        run: |
          echo "✅ 所有测试通过！"
          # 这里可以添加 Slack、Teams、钉钉等通知

      - name: 发送失败通知
        if: needs.e2e-tests.result == 'failure'
        run: |
          echo "❌ 测试失败，请检查报告"
          # 这里可以添加失败通知逻辑
