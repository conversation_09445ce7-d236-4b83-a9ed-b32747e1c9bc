DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.address`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.address` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.automotive`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.automotive` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.bank`.
DEBUG    faker.factory:factory.py:88 Specified locale `en_US` is not available for provider `faker.providers.bank`. Locale reset to `en_GB` for this provider.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.barcode`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.barcode` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.color`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.color` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.company`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.company` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.credit_card`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.credit_card` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.currency`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.currency` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.date_time`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.date_time` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:108 Provider `faker.providers.doi` does not feature localization. Specified locale `en_US` is not utilized for this provider.
DEBUG    faker.factory:factory.py:108 Provider `faker.providers.emoji` does not feature localization. Specified locale `en_US` is not utilized for this provider.
DEBUG    faker.factory:factory.py:108 Provider `faker.providers.file` does not feature localization. Specified locale `en_US` is not utilized for this provider.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.geo`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.geo` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.internet`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.internet` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.isbn`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.isbn` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.job`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.job` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.lorem`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.lorem` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.misc`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.misc` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.passport`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.passport` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.person`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.person` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.phone_number`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.phone_number` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:108 Provider `faker.providers.profile` does not feature localization. Specified locale `en_US` is not utilized for this provider.
DEBUG    faker.factory:factory.py:108 Provider `faker.providers.python` does not feature localization. Specified locale `en_US` is not utilized for this provider.
DEBUG    faker.factory:factory.py:108 Provider `faker.providers.sbn` does not feature localization. Specified locale `en_US` is not utilized for this provider.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.ssn`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.ssn` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:108 Provider `faker.providers.user_agent` does not feature localization. Specified locale `en_US` is not utilized for this provider.
INFO     fixtures.scf_fixtures:scf_fixtures.py:32 加载SCF系统配置成功
INFO     fixtures.scf_fixtures:scf_fixtures.py:52 加载SCF测试数据成功
DEBUG    asyncio:proactor_events.py:634 Using proactor: IocpProactor
INFO     fixtures.scf_fixtures:scf_fixtures.py:79 准备SCF登录凭证
INFO     components.ocr_util:ocr_util.py:60 OCR引擎初始化成功
DEBUG    fixtures.scf_fixtures:scf_fixtures.py:172 测试文件验证通过: bfmArtAssocId -> G:\nifa\playwright-python-template\data\test_files\异常信息共享系统V1.5需求.docx
INFO     SCFRegistrationWorkflow:scf_registration_workflow.py:46 开始执行完整的备案申请流程
INFO     SCFRegistrationWorkflow:scf_registration_workflow.py:110 开始登录系统
INFO     SCFLoginPage:scf_login_page.py:55 导航到登录页面: http://172.18.12.128:8080/login
INFO     pages.base_page:base_page.py:30 导航到页面: http://172.18.12.128:8080/login
INFO     SCFLoginPage:scf_login_page.py:448 开始登录流程，用户名: scf_4nuioc
INFO     SCFLoginPage:scf_login_page.py:366 开始登录流程（带重试），用户名: scf_4nuioc, 最大重试次数: 5
INFO     SCFLoginPage:scf_login_page.py:62 点击账号登录标签
INFO     pages.base_page:base_page.py:190 等待元素 text=账号登录 状态: visible
DEBUG    components.retry_util:retry_util.py:87 执行函数 click，尝试 1/3
INFO     pages.base_page:base_page.py:106 点击元素: text=账号登录
DEBUG    pages.base_page:base_page.py:65 元素 text=账号登录 已稳定
INFO     SCFLoginPage:scf_login_page.py:95 输入用户名: scf_4nuioc
DEBUG    components.retry_util:retry_util.py:87 执行函数 fill，尝试 1/3
INFO     pages.base_page:base_page.py:130 填充文本到元素 input[placeholder='请输入账号']: scf_4nuioc
DEBUG    pages.base_page:base_page.py:65 元素 input[placeholder='请输入账号'] 已稳定
INFO     SCFLoginPage:scf_login_page.py:119 输入密码
DEBUG    components.retry_util:retry_util.py:87 执行函数 fill，尝试 1/3
INFO     pages.base_page:base_page.py:130 填充文本到元素 input[type='password']: Scf123456.
DEBUG    pages.base_page:base_page.py:65 元素 input[type='password'] 已稳定
INFO     SCFLoginPage:scf_login_page.py:378 登录尝试 1/5
INFO     SCFLoginPage:scf_login_page.py:257 开始自动识别验证码，最大重试次数: 1, 最小置信度: 0.6
INFO     SCFLoginPage:scf_login_page.py:261 验证码识别尝试 1/1
DEBUG    SCFLoginPage:scf_login_page.py:177 尝试选择器: .arco-image img
INFO     pages.base_page:base_page.py:190 等待元素 .arco-image img 状态: visible
INFO     SCFLoginPage:scf_login_page.py:181 找到验证码元素，使用选择器: .arco-image img
INFO     SCFLoginPage:scf_login_page.py:200 验证码图片已保存到: logs/captcha_debug_attempt_1.png
DEBUG    PIL.PngImagePlugin:PngImagePlugin.py:201 STREAM b'IHDR' 16 13
DEBUG    PIL.PngImagePlugin:PngImagePlugin.py:201 STREAM b'sRGB' 41 1
DEBUG    PIL.PngImagePlugin:PngImagePlugin.py:201 STREAM b'IDAT' 54 6036
DEBUG    PIL.PngImagePlugin:PngImagePlugin.py:201 STREAM b'IHDR' 16 13
DEBUG    PIL.PngImagePlugin:PngImagePlugin.py:201 STREAM b'sRGB' 41 1
DEBUG    PIL.PngImagePlugin:PngImagePlugin.py:201 STREAM b'IDAT' 54 6036
WARNING  components.ocr_util:ocr_util.py:260 图像复杂度分析失败: name 'ImageFilter' is not defined
INFO     components.ocr_util:ocr_util.py:87 OCR识别结果: 3055, 置信度: 0.8
INFO     SCFLoginPage:scf_login_page.py:227 尝试 1 - OCR识别结果: '3055', 置信度: 0.800
INFO     SCFLoginPage:scf_login_page.py:271 验证码识别成功: '3055', 置信度: 0.800
INFO     SCFLoginPage:scf_login_page.py:132 输入图形验证码: 3055
DEBUG    components.retry_util:retry_util.py:87 执行函数 fill，尝试 1/3
INFO     pages.base_page:base_page.py:130 填充文本到元素 input[placeholder='请输入图形验证码']: 3055
DEBUG    pages.base_page:base_page.py:65 元素 input[placeholder='请输入图形验证码'] 已稳定
INFO     SCFLoginPage:scf_login_page.py:387 尝试 1 - 使用验证码: 3055
INFO     SCFLoginPage:scf_login_page.py:319 点击登录按钮
DEBUG    components.retry_util:retry_util.py:87 执行函数 click，尝试 1/3
INFO     pages.base_page:base_page.py:106 点击元素: button[type='submit']
DEBUG    pages.base_page:base_page.py:65 元素 button[type='submit'] 已稳定
INFO     SCFLoginPage:scf_login_page.py:393 检查登录状态...
INFO     SCFLoginPage:scf_login_page.py:346 已离开登录页面，当前URL: http://172.18.12.128:8080/system-register/system-register
INFO     SCFLoginPage:scf_login_page.py:395 登录成功！尝试次数: 1
INFO     SCFLoginPage:scf_login_page.py:467 登录操作完成
INFO     SCFLoginPage:scf_login_page.py:523 验证登录成功状态
INFO     pages.base_page:base_page.py:410 等待页面加载状态: networkidle
INFO     SCFLoginPage:scf_login_page.py:538 登录成功验证通过
INFO     SCFRegistrationWorkflow:scf_registration_workflow.py:127 系统登录成功
INFO     SCFRegistrationWorkflow:scf_registration_workflow.py:132 进入备案申请
INFO     SCFRegistrationPage:scf_registration_page.py:51 点击备案登记菜单
DEBUG    components.retry_util:retry_util.py:87 执行函数 click，尝试 1/3
INFO     pages.base_page:base_page.py:106 点击元素: text=备案登记
DEBUG    pages.base_page:base_page.py:65 元素 text=备案登记 已稳定
INFO     SCFRegistrationPage:scf_registration_page.py:58 点击备案申请按钮
DEBUG    components.retry_util:retry_util.py:87 执行函数 click，尝试 1/3
INFO     pages.base_page:base_page.py:106 点击元素: role=button[name='备案申请']
DEBUG    pages.base_page:base_page.py:65 元素 role=button[name='备案申请'] 已稳定
INFO     SCFRegistrationWorkflow:scf_registration_workflow.py:144 填写基础材料
INFO     SCFRegistrationPage:scf_registration_page.py:93 批量上传文件，共 1 个文件
WARNING  SCFRegistrationPage:scf_registration_page.py:106 上传区域不可见: bfmArtAssocId
INFO     SCFRegistrationPage:scf_registration_page.py:168 点击下一步按钮
DEBUG    components.retry_util:retry_util.py:87 执行函数 click，尝试 1/3
INFO     pages.base_page:base_page.py:106 点击元素: role=button[name='下一步']
DEBUG    pages.base_page:base_page.py:65 元素 role=button[name='下一步'] 已稳定
INFO     pages.base_page:base_page.py:410 等待页面加载状态: networkidle
INFO     SCFRegistrationWorkflow:scf_registration_workflow.py:166 填写业务合规信息
INFO     SCFRegistrationPage:scf_registration_page.py:235 填写业务合规表单
INFO     SCFRegistrationPage:scf_registration_page.py:120 在区域 bcVele 选择选项: 否
DEBUG    components.retry_util:retry_util.py:87 执行函数 click，尝试 1/3
INFO     pages.base_page:base_page.py:106 点击元素: #bcVele text=否
WARNING  components.retry_util:retry_util.py:97 函数 click 第 1 次尝试失败: Locator.wait_for: Unexpected token "=" while parsing css selector "#bcVele text=否". Did you mean to CSS.escape it?
Call log:
  - waiting for #bcVele text=否 to be visible

DEBUG    components.retry_util:retry_util.py:115 等待 0.48 秒后重试
DEBUG    components.retry_util:retry_util.py:87 执行函数 click，尝试 2/3
INFO     pages.base_page:base_page.py:106 点击元素: #bcVele text=否
WARNING  components.retry_util:retry_util.py:97 函数 click 第 2 次尝试失败: Locator.wait_for: Unexpected token "=" while parsing css selector "#bcVele text=否". Did you mean to CSS.escape it?
Call log:
  - waiting for #bcVele text=否 to be visible

DEBUG    components.retry_util:retry_util.py:115 等待 0.94 秒后重试
DEBUG    components.retry_util:retry_util.py:87 执行函数 click，尝试 3/3
INFO     pages.base_page:base_page.py:106 点击元素: #bcVele text=否
WARNING  components.retry_util:retry_util.py:97 函数 click 第 3 次尝试失败: Locator.wait_for: Unexpected token "=" while parsing css selector "#bcVele text=否". Did you mean to CSS.escape it?
Call log:
  - waiting for #bcVele text=否 to be visible

ERROR    components.retry_util:retry_util.py:124 函数 click 在 3 次尝试后仍然失败
ERROR    SCFRegistrationWorkflow:scf_registration_workflow.py:97 备案申请流程执行失败: Locator.wait_for: Unexpected token "=" while parsing css selector "#bcVele text=否". Did you mean to CSS.escape it?
Call log:
  - waiting for #bcVele text=否 to be visible

INFO     SCFRegistrationWorkflow:base_workflow.py:175 截图: 备案申请流程失败