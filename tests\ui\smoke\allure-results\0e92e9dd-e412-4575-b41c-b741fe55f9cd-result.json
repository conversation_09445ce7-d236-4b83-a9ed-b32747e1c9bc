{"name": "test_minimal_registration_process[chromium]", "status": "skipped", "statusDetails": {"message": "Skipped: 测试文件缺失，跳过测试: ['bfmArtAssocId']", "trace": "('G:\\\\nifa\\\\playwright-python-template\\\\tests\\\\ui\\\\smoke\\\\test_scf_registration.py', 36, \"Skipped: 测试文件缺失，跳过测试: ['bfmArtAssocId']\")"}, "description": "测试最小化备案申请流程", "attachments": [{"name": "log", "source": "2e55f062-14d6-4db2-8e56-74da91621a9d-attachment.txt", "type": "text/plain"}, {"name": "stdout", "source": "6716b62d-e326-4324-b24f-249a2b8998b5-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "browser_name", "value": "'chromium'"}], "start": 1751854939997, "stop": 1751854940037, "uuid": "e89fb66a-171d-4efe-92f5-ed7903987e15", "historyId": "51ce2efbdcb9bf9576a5ef0f28a8c2b1", "testCaseId": "2a131723a09418cae3744a996cf6d25c", "fullName": "tests.ui.smoke.test_scf_registration.TestSCFRegistration#test_minimal_registration_process", "labels": [{"name": "severity", "value": "blocker"}, {"name": "story", "value": "最小化备案申请流程"}, {"name": "feature", "value": "备案申请"}, {"name": "epic", "value": "供应链金融备案系统"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "tests.ui.smoke"}, {"name": "suite", "value": "test_scf_registration"}, {"name": "subSuite", "value": "TestSCFRegistration"}, {"name": "host", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "thread", "value": "24016-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.ui.smoke.test_scf_registration"}]}