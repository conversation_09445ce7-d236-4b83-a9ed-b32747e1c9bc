from playwright.sync_api import Page, expect, Locator
from typing import Optional, Union, List
import time
import allure
from components.log_manager import get_logger
from components.retry_util import retry_on_exception

logger = get_logger(__name__)


class BasePage:
    """
    基础页面类，提供智能等待、稳定性检查和流畅接口
    """

    def __init__(self, page: Page):
        self.page = page
        self.default_timeout = 30000  # 30秒默认超时
        self.stability_timeout = 1000  # 1秒稳定性检查超时

    @allure.step("导航到页面: {url}")
    def navigate(self, url: str, wait_until: str = "domcontentloaded"):
        """
        导航到指定URL

        Args:
            url: 目标URL
            wait_until: 等待条件 ('load', 'domcontentloaded', 'networkidle')
        """
        logger.info(f"导航到页面: {url}")
        self.page.goto(url, wait_until=wait_until, timeout=self.default_timeout)
        return self

    @allure.step("等待元素稳定: {locator}")
    def wait_for_element_stable(self, locator: str, timeout: Optional[int] = None) -> Locator:
        """
        等待元素稳定（位置和尺寸不再变化）

        Args:
            locator: 元素定位符
            timeout: 超时时间（毫秒）

        Returns:
            Locator对象
        """
        timeout = timeout or self.stability_timeout
        element = self.page.locator(locator)

        # 首先等待元素可见
        element.wait_for(state="visible", timeout=self.default_timeout)

        # 检查元素位置稳定性
        previous_box = None
        stable_count = 0
        required_stable_checks = 3

        start_time = time.time()
        while time.time() - start_time < timeout / 1000:
            try:
                current_box = element.bounding_box()
                if current_box:
                    if previous_box and self._boxes_equal(previous_box, current_box):
                        stable_count += 1
                        if stable_count >= required_stable_checks:
                            logger.debug(f"元素 {locator} 已稳定")
                            return element
                    else:
                        stable_count = 0
                    previous_box = current_box
                time.sleep(0.1)
            except Exception as e:
                logger.warning(f"检查元素稳定性时出错: {e}")
                break

        logger.debug(f"元素 {locator} 稳定性检查完成")
        return element

    def _boxes_equal(self, box1: dict, box2: dict, tolerance: float = 1.0) -> bool:
        """
        比较两个边界框是否相等（允许一定误差）

        Args:
            box1: 第一个边界框
            box2: 第二个边界框
            tolerance: 允许的误差范围

        Returns:
            是否相等
        """
        return (abs(box1['x'] - box2['x']) <= tolerance and
                abs(box1['y'] - box2['y']) <= tolerance and
                abs(box1['width'] - box2['width']) <= tolerance and
                abs(box1['height'] - box2['height']) <= tolerance)

    @retry_on_exception(max_attempts=3, delay=0.5)
    @allure.step("智能点击: {locator}")
    def click(self, locator: str, force: bool = False, timeout: Optional[int] = None):
        """
        智能点击元素

        Args:
            locator: 元素定位符
            force: 是否强制点击
            timeout: 超时时间
        """
        logger.info(f"点击元素: {locator}")
        element = self.wait_for_element_stable(locator)

        # 确保元素可点击
        if not force:
            element.wait_for(state="visible", timeout=timeout or self.default_timeout)
            # 滚动到元素可见区域
            element.scroll_into_view_if_needed()

        element.click(force=force, timeout=timeout or self.default_timeout)
        return self

    @retry_on_exception(max_attempts=3, delay=0.5)
    @allure.step("智能填充: {locator} = {text}")
    def fill(self, locator: str, text: str, clear_first: bool = True, timeout: Optional[int] = None):
        """
        智能填充文本

        Args:
            locator: 元素定位符
            text: 要填充的文本
            clear_first: 是否先清空
            timeout: 超时时间
        """
        logger.info(f"填充文本到元素 {locator}: {text}")
        element = self.wait_for_element_stable(locator)

        # 确保元素可编辑
        element.wait_for(state="visible", timeout=timeout or self.default_timeout)
        element.scroll_into_view_if_needed()

        if clear_first:
            element.clear()

        element.fill(text, timeout=timeout or self.default_timeout)
        return self

    @allure.step("获取元素文本: {locator}")
    def get_text(self, locator: str, timeout: Optional[int] = None) -> str:
        """
        获取元素文本内容

        Args:
            locator: 元素定位符
            timeout: 超时时间

        Returns:
            元素文本内容
        """
        element = self.page.locator(locator)
        element.wait_for(state="visible", timeout=timeout or self.default_timeout)
        text = element.text_content()
        logger.debug(f"获取元素 {locator} 文本: {text}")
        return text or ""

    @allure.step("获取元素属性: {locator}.{attribute}")
    def get_attribute(self, locator: str, attribute: str, timeout: Optional[int] = None) -> Optional[str]:
        """
        获取元素属性值

        Args:
            locator: 元素定位符
            attribute: 属性名
            timeout: 超时时间

        Returns:
            属性值
        """
        element = self.page.locator(locator)
        element.wait_for(state="visible", timeout=timeout or self.default_timeout)
        value = element.get_attribute(attribute)
        logger.debug(f"获取元素 {locator} 属性 {attribute}: {value}")
        return value

    @allure.step("等待元素出现: {locator}")
    def wait_for_element(self, locator: str, state: str = "visible", timeout: Optional[int] = None):
        """
        等待元素出现

        Args:
            locator: 元素定位符
            state: 等待状态 ('attached', 'detached', 'visible', 'hidden')
            timeout: 超时时间
        """
        logger.info(f"等待元素 {locator} 状态: {state}")
        element = self.page.locator(locator)
        element.wait_for(state=state, timeout=timeout or self.default_timeout)
        return self

    @allure.step("等待元素消失: {locator}")
    def wait_for_element_hidden(self, locator: str, timeout: Optional[int] = None):
        """
        等待元素消失

        Args:
            locator: 元素定位符
            timeout: 超时时间
        """
        return self.wait_for_element(locator, state="hidden", timeout=timeout)

    @allure.step("检查元素是否可见: {locator}")
    def is_visible(self, locator: str, timeout: Optional[int] = None) -> bool:
        """
        检查元素是否可见

        Args:
            locator: 元素定位符
            timeout: 超时时间

        Returns:
            是否可见
        """
        try:
            element = self.page.locator(locator)
            element.wait_for(state="visible", timeout=timeout or 5000)  # 较短的超时时间
            return True
        except Exception:
            return False

    @allure.step("断言元素包含文本: {locator} 包含 '{text}'")
    def expect_element_to_have_text(self, locator: str, text: str, timeout: Optional[int] = None):
        """
        断言元素包含指定文本

        Args:
            locator: 元素定位符
            text: 期望的文本
            timeout: 超时时间
        """
        logger.info(f"断言元素 {locator} 包含文本: {text}")
        element = self.page.locator(locator)
        expect(element).to_have_text(text, timeout=timeout or self.default_timeout)
        return self

    @allure.step("断言元素可见: {locator}")
    def expect_element_to_be_visible(self, locator: str, timeout: Optional[int] = None):
        """
        断言元素可见

        Args:
            locator: 元素定位符
            timeout: 超时时间
        """
        logger.info(f"断言元素 {locator} 可见")
        element = self.page.locator(locator)
        expect(element).to_be_visible(timeout=timeout or self.default_timeout)
        return self

    @allure.step("断言元素隐藏: {locator}")
    def expect_element_to_be_hidden(self, locator: str, timeout: Optional[int] = None):
        """
        断言元素隐藏

        Args:
            locator: 元素定位符
            timeout: 超时时间
        """
        logger.info(f"断言元素 {locator} 隐藏")
        element = self.page.locator(locator)
        expect(element).to_be_hidden(timeout=timeout or self.default_timeout)
        return self

    @allure.step("断言元素启用: {locator}")
    def expect_element_to_be_enabled(self, locator: str, timeout: Optional[int] = None):
        """
        断言元素启用

        Args:
            locator: 元素定位符
            timeout: 超时时间
        """
        logger.info(f"断言元素 {locator} 启用")
        element = self.page.locator(locator)
        expect(element).to_be_enabled(timeout=timeout or self.default_timeout)
        return self

    @allure.step("断言页面标题: {title}")
    def expect_page_to_have_title(self, title: str, timeout: Optional[int] = None):
        """
        断言页面标题

        Args:
            title: 期望的标题
            timeout: 超时时间
        """
        logger.info(f"断言页面标题: {title}")
        expect(self.page).to_have_title(title, timeout=timeout or self.default_timeout)
        return self

    @allure.step("断言页面URL: {url}")
    def expect_page_to_have_url(self, url: str, timeout: Optional[int] = None):
        """
        断言页面URL

        Args:
            url: 期望的URL（支持正则表达式）
            timeout: 超时时间
        """
        logger.info(f"断言页面URL: {url}")
        expect(self.page).to_have_url(url, timeout=timeout or self.default_timeout)
        return self

    @allure.step("滚动到元素: {locator}")
    def scroll_to_element(self, locator: str):
        """
        滚动到指定元素

        Args:
            locator: 元素定位符
        """
        logger.info(f"滚动到元素: {locator}")
        element = self.page.locator(locator)
        element.scroll_into_view_if_needed()
        return self

    @allure.step("悬停在元素上: {locator}")
    def hover(self, locator: str, timeout: Optional[int] = None):
        """
        悬停在元素上

        Args:
            locator: 元素定位符
            timeout: 超时时间
        """
        logger.info(f"悬停在元素 {locator} 上")
        element = self.wait_for_element_stable(locator)
        element.hover(timeout=timeout or self.default_timeout)
        return self

    @allure.step("双击元素: {locator}")
    def double_click(self, locator: str, timeout: Optional[int] = None):
        """
        双击元素

        Args:
            locator: 元素定位符
            timeout: 超时时间
        """
        logger.info(f"双击元素: {locator}")
        element = self.wait_for_element_stable(locator)
        element.dblclick(timeout=timeout or self.default_timeout)
        return self

    @allure.step("右键点击元素: {locator}")
    def right_click(self, locator: str, timeout: Optional[int] = None):
        """
        右键点击元素

        Args:
            locator: 元素定位符
            timeout: 超时时间
        """
        logger.info(f"右键点击元素: {locator}")
        element = self.wait_for_element_stable(locator)
        element.click(button="right", timeout=timeout or self.default_timeout)
        return self

    @allure.step("选择下拉选项: {locator} = {value}")
    def select_option(self, locator: str, value: Union[str, List[str]], timeout: Optional[int] = None):
        """
        选择下拉选项

        Args:
            locator: 下拉框定位符
            value: 选项值
            timeout: 超时时间
        """
        logger.info(f"选择下拉选项 {locator}: {value}")
        element = self.wait_for_element_stable(locator)
        element.select_option(value, timeout=timeout or self.default_timeout)
        return self

    @allure.step("上传文件: {locator}")
    def upload_file(self, locator: str, file_path: str, timeout: Optional[int] = None):
        """
        上传文件

        Args:
            locator: 文件输入框定位符
            file_path: 文件路径
            timeout: 超时时间
        """
        logger.info(f"上传文件到 {locator}: {file_path}")
        element = self.page.locator(locator)
        element.set_input_files(file_path, timeout=timeout or self.default_timeout)
        return self

    def get_current_url(self) -> str:
        """获取当前页面URL"""
        return self.page.url

    def get_page_title(self) -> str:
        """获取页面标题"""
        return self.page.title()

    @allure.step("等待页面加载完成")
    def wait_for_load_state(self, state: str = "domcontentloaded", timeout: Optional[int] = None):
        """
        等待页面加载状态

        Args:
            state: 加载状态 ('load', 'domcontentloaded', 'networkidle')
            timeout: 超时时间
        """
        logger.info(f"等待页面加载状态: {state}")
        self.page.wait_for_load_state(state, timeout=timeout or self.default_timeout)
        return self

    @allure.step("截图保存")
    def take_screenshot(self, path: Optional[str] = None) -> bytes:
        """
        截图

        Args:
            path: 保存路径，如果不提供则返回字节数据

        Returns:
            截图字节数据
        """
        logger.info(f"截图保存到: {path or '内存'}")
        return self.page.screenshot(path=path)

    def set_default_timeout(self, timeout: int):
        """
        设置默认超时时间

        Args:
            timeout: 超时时间（毫秒）
        """
        self.default_timeout = timeout
        logger.info(f"设置默认超时时间: {timeout}ms")
        return self
