# 企业级 Web 自动化测试平台

[![CI/CD Pipeline](https://github.com/your-org/playwright-python-template/actions/workflows/ci.yml/badge.svg)](https://github.com/your-org/playwright-python-template/actions/workflows/ci.yml)
[![Quality Checks](https://github.com/your-org/playwright-python-template/actions/workflows/quality-checks.yml/badge.svg)](https://github.com/your-org/playwright-python-template/actions/workflows/quality-checks.yml)
[![Python 3.12+](https://img.shields.io/badge/python-3.12+-blue.svg)](https://www.python.org/downloads/)
[![Playwright](https://img.shields.io/badge/playwright-latest-green.svg)](https://playwright.dev/)
[![uv](https://img.shields.io/badge/uv-latest-orange.svg)](https://github.com/astral-sh/uv)

基于 Python + Playwright + uv 构建的现代化、高度模块化、可扩展且易于维护的企业级 Web 自动化测试平台。

## ✨ 特性

- 🚀 **现代化技术栈**: Python 3.12+ + Playwright + uv + Allure
- 🏗️ **分层架构**: POM (Page Object Model) + Workflow 业务流程层
- 🔧 **智能等待**: 内置智能等待和稳定性检查机制
- 📊 **详细报告**: Allure 报告集成，包含截图、日志、Trace
- 🐳 **容器化**: Docker + Docker Compose 支持
- 🔄 **CI/CD**: GitHub Actions 完整流水线
- 🛡️ **代码质量**: Ruff + Black + MyPy + Pre-commit
- 🧪 **多层测试**: 冒烟测试 + 组件测试 + 端到端测试
- 🔀 **并行执行**: 支持多浏览器并行测试
- 📱 **跨平台**: 支持 Chrome、Firefox、Safari

## 🏗️ 项目架构

```
├── .github/workflows/      # CI/CD 流水线配置
├── components/             # 通用可复用组件
│   ├── faker_util.py      # Faker 数据工厂
│   ├── log_manager.py     # 日志管理器
│   ├── ocr_util.py        # OCR 验证码识别
│   └── retry_util.py      # 重试装饰器
├── config/                 # 环境配置管理
│   ├── base.yaml          # 基础配置
│   ├── dev.yaml           # 开发环境配置
│   └── test.yaml          # 测试环境配置
├── data/                   # 测试数据
│   ├── static/            # 静态数据文件
│   └── ocr_samples/       # OCR 样本
├── fixtures/               # Pytest Fixtures
│   ├── conftest.py        # 全局核心 Fixtures
│   └── data_fixtures.py   # 数据注入与清理
├── pages/                  # 页面对象层 (POM)
│   ├── base_page.py       # 基础页面类
│   └── login_page.py      # 登录页面
├── tests/                  # 测试用例层
│   ├── ui/
│   │   ├── smoke/         # 冒烟测试
│   │   ├── component/     # 组件测试
│   │   └── e2e/           # 端到端测试
│   └── api/               # API 测试
├── workflows/              # 业务流程层
│   ├── base_workflow.py   # 基础流程类
│   └── login_workflow.py  # 登录业务流程
├── Dockerfile             # 容器镜像配置
├── docker-compose.yml     # 容器编排配置
└── pyproject.toml         # 项目配置文件
```

## 🚀 快速启动

### 环境要求

- Python 3.12+
- uv (推荐) 或 pip
- Docker (可选，用于容器化运行)

### 安装

1. **克隆项目**
   ```bash
   git clone https://github.com/your-org/playwright-python-template.git
   cd playwright-python-template
   ```

2. **安装 uv (如果尚未安装)**
   ```bash
   # macOS/Linux
   curl -LsSf https://astral.sh/uv/install.sh | sh

   # Windows
   powershell -c "irm https://astral.sh/uv/install.ps1 | iex"
   ```

3. **创建虚拟环境并安装依赖**
   ```bash
   uv venv
   uv pip install -e ".[dev]"
   ```

4. **安装 Playwright 浏览器**
   ```bash
   uv run playwright install
   ```

5. **安装 pre-commit 钩子**
   ```bash
   uv run pre-commit install
   ```

### 运行测试

1. **运行冒烟测试**
   ```bash
   uv run pytest tests/ui/smoke/ -v
   ```

2. **运行所有测试并生成报告**
   ```bash
   uv run pytest --alluredir=allure-results -v
   uv run allure serve allure-results
   ```

3. **运行特定浏览器测试**
   ```bash
   uv run pytest --browser=firefox tests/ui/smoke/ -v
   ```

4. **并行执行测试**
   ```bash
   uv run pytest -n auto tests/ui/ -v
   ```

### 容器化运行

1. **使用 Docker Compose 运行**
   ```bash
   # 构建并运行测试
   docker-compose up --build

   # 开发模式
   docker-compose -f docker-compose.yml -f docker-compose.dev.yml up
   ```

2. **查看 Allure 报告**
   ```bash
   # 报告服务会在 http://localhost:5050 启动
   open http://localhost:5050
   ```

## 📝 使用指南

### 编写测试用例

1. **创建页面对象**
   ```python
   from pages.base_page import BasePage

   class ProductPage(BasePage):
       def __init__(self, page):
           super().__init__(page)
           self.add_to_cart_button = "#add-to-cart"

       @allure.step("添加商品到购物车")
       def add_to_cart(self):
           self.click(self.add_to_cart_button)
           return self
   ```

2. **创建业务流程**
   ```python
   from workflows.base_workflow import BaseWorkflow

   class PurchaseWorkflow(BaseWorkflow):
       @allure.step("执行购买流程")
       def complete_purchase(self, product_name):
           # 业务逻辑实现
           pass
   ```

3. **编写测试用例**
   ```python
   @allure.feature("商品购买")
   class TestPurchase:
       @pytest.mark.smoke
       def test_purchase_success(self, page, env_config):
           workflow = PurchaseWorkflow(page)
           workflow.complete_purchase("测试商品")
   ```

### 配置管理

1. **环境配置**
   - 修改 `config/` 目录下的 YAML 文件
   - 使用 `--env` 参数指定环境: `pytest --env=dev`

2. **敏感信息**
   - 使用环境变量或 CI Secrets
   - 不要在代码中硬编码密码

### 数据管理

1. **使用 Faker 生成测试数据**
   ```python
   def test_with_fake_data(faker_factory):
       user_data = faker_factory.generate_user_data()
   ```

2. **从文件加载数据**
   ```python
   def test_with_file_data(test_data_from_file):
       data = test_data_from_file("users.json")
   ```

## 🔧 开发指南

### 代码规范

- 使用 Ruff 进行代码检查和格式化
- 使用 Black 进行代码格式化
- 使用 MyPy 进行类型检查
- 遵循 PEP 8 编码规范

### 提交规范

- 遵循 Conventional Commits 规范
- 提交前会自动运行 pre-commit 检查
- PR 需要通过所有质量检查

### 测试分层

1. **冒烟测试 (Smoke)**: 核心功能验证
2. **组件测试 (Component)**: 页面/组件级测试
3. **端到端测试 (E2E)**: 完整业务流程测试

## 📊 报告和监控

### Allure 报告

- 自动生成详细的测试报告
- 包含截图、日志、执行轨迹
- 支持历史趋势分析

### CI/CD 集成

- GitHub Actions 自动化流水线
- 多浏览器并行测试
- 自动部署测试报告

## 🐛 故障排除

### 常见问题

1. **浏览器安装失败**
   ```bash
   uv run playwright install --force
   ```

2. **依赖冲突**
   ```bash
   uv pip install --force-reinstall -e ".[dev]"
   ```

3. **权限问题 (Linux/macOS)**
   ```bash
   sudo uv run playwright install-deps
   ```

### 调试技巧

1. **启用调试模式**
   ```bash
   PWDEBUG=1 uv run pytest tests/ui/smoke/test_login.py::test_login -s
   ```

2. **查看 Trace**
   ```bash
   uv run playwright show-trace logs/traces/trace_test_name.zip
   ```

3. **保存失败截图**
   - 失败截图自动保存到 `logs/` 目录
   - Allure 报告中包含失败截图

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支: `git checkout -b feature/amazing-feature`
3. 提交更改: `git commit -m 'feat: add amazing feature'`
4. 推送分支: `git push origin feature/amazing-feature`
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [Playwright](https://playwright.dev/) - 现代化的端到端测试框架
- [uv](https://github.com/astral-sh/uv) - 极速的 Python 包管理器
- [Allure](https://docs.qameta.io/allure/) - 美观的测试报告框架
- [Ruff](https://github.com/astral-sh/ruff) - 极速的 Python 代码检查工具

---

**📧 联系我们**: <EMAIL>
**📖 文档**: [项目文档](https://your-org.github.io/playwright-python-template/)
**🐛 问题反馈**: [GitHub Issues](https://github.com/your-org/playwright-python-template/issues)
