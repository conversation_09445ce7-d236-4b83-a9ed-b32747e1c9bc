[35mDEBUG   [0m faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.address`.
[35mDEBUG   [0m faker.factory:factory.py:97 Provider `faker.providers.address` has been localized to `en_US`.
[35mDEBUG   [0m faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.automotive`.
[35mDEBUG   [0m faker.factory:factory.py:97 Provider `faker.providers.automotive` has been localized to `en_US`.
[35mDEBUG   [0m faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.bank`.
[35mDEBUG   [0m faker.factory:factory.py:88 Specified locale `en_US` is not available for provider `faker.providers.bank`. Locale reset to `en_GB` for this provider.
[35mDEBUG   [0m faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.barcode`.
[35mDEBUG   [0m faker.factory:factory.py:97 Provider `faker.providers.barcode` has been localized to `en_US`.
[35mDEBUG   [0m faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.color`.
[35mDEBUG   [0m faker.factory:factory.py:97 Provider `faker.providers.color` has been localized to `en_US`.
[35mDEBUG   [0m faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.company`.
[35mDEBUG   [0m faker.factory:factory.py:97 Provider `faker.providers.company` has been localized to `en_US`.
[35mDEBUG   [0m faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.credit_card`.
[35mDEBUG   [0m faker.factory:factory.py:97 Provider `faker.providers.credit_card` has been localized to `en_US`.
[35mDEBUG   [0m faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.currency`.
[35mDEBUG   [0m faker.factory:factory.py:97 Provider `faker.providers.currency` has been localized to `en_US`.
[35mDEBUG   [0m faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.date_time`.
[35mDEBUG   [0m faker.factory:factory.py:97 Provider `faker.providers.date_time` has been localized to `en_US`.
[35mDEBUG   [0m faker.factory:factory.py:108 Provider `faker.providers.doi` does not feature localization. Specified locale `en_US` is not utilized for this provider.
[35mDEBUG   [0m faker.factory:factory.py:108 Provider `faker.providers.emoji` does not feature localization. Specified locale `en_US` is not utilized for this provider.
[35mDEBUG   [0m faker.factory:factory.py:108 Provider `faker.providers.file` does not feature localization. Specified locale `en_US` is not utilized for this provider.
[35mDEBUG   [0m faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.geo`.
[35mDEBUG   [0m faker.factory:factory.py:97 Provider `faker.providers.geo` has been localized to `en_US`.
[35mDEBUG   [0m faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.internet`.
[35mDEBUG   [0m faker.factory:factory.py:97 Provider `faker.providers.internet` has been localized to `en_US`.
[35mDEBUG   [0m faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.isbn`.
[35mDEBUG   [0m faker.factory:factory.py:97 Provider `faker.providers.isbn` has been localized to `en_US`.
[35mDEBUG   [0m faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.job`.
[35mDEBUG   [0m faker.factory:factory.py:97 Provider `faker.providers.job` has been localized to `en_US`.
[35mDEBUG   [0m faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.lorem`.
[35mDEBUG   [0m faker.factory:factory.py:97 Provider `faker.providers.lorem` has been localized to `en_US`.
[35mDEBUG   [0m faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.misc`.
[35mDEBUG   [0m faker.factory:factory.py:97 Provider `faker.providers.misc` has been localized to `en_US`.
[35mDEBUG   [0m faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.passport`.
[35mDEBUG   [0m faker.factory:factory.py:97 Provider `faker.providers.passport` has been localized to `en_US`.
[35mDEBUG   [0m faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.person`.
[35mDEBUG   [0m faker.factory:factory.py:97 Provider `faker.providers.person` has been localized to `en_US`.
[35mDEBUG   [0m faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.phone_number`.
[35mDEBUG   [0m faker.factory:factory.py:97 Provider `faker.providers.phone_number` has been localized to `en_US`.
[35mDEBUG   [0m faker.factory:factory.py:108 Provider `faker.providers.profile` does not feature localization. Specified locale `en_US` is not utilized for this provider.
[35mDEBUG   [0m faker.factory:factory.py:108 Provider `faker.providers.python` does not feature localization. Specified locale `en_US` is not utilized for this provider.
[35mDEBUG   [0m faker.factory:factory.py:108 Provider `faker.providers.sbn` does not feature localization. Specified locale `en_US` is not utilized for this provider.
[35mDEBUG   [0m faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.ssn`.
[35mDEBUG   [0m faker.factory:factory.py:97 Provider `faker.providers.ssn` has been localized to `en_US`.
[35mDEBUG   [0m faker.factory:factory.py:108 Provider `faker.providers.user_agent` does not feature localization. Specified locale `en_US` is not utilized for this provider.
[35mDEBUG   [0m urllib3.connectionpool:connectionpool.py:241 Starting new HTTP connection (1): 172.18.12.128:8080
[35mDEBUG   [0m urllib3.connectionpool:connectionpool.py:544 http://172.18.12.128:8080 "GET / HTTP/1.1" 200 833
[32mINFO    [0m conftest:conftest.py:192 测试服务器连接正常
[32mINFO    [0m conftest:conftest.py:29 ============================================================
[32mINFO    [0m conftest:conftest.py:30 开始 SCF 备案流程测试会话
[32mINFO    [0m conftest:conftest.py:31 ============================================================
[35mDEBUG   [0m asyncio:proactor_events.py:634 Using proactor: IocpProactor
[32mINFO    [0m conftest:conftest.py:53 开始测试类: TestSCFFilingExceptions
[32mINFO    [0m fixtures.filing_fixtures:filing_fixtures.py:174 开始备案测试: test_required_fields_validation[chromium]
[32mINFO    [0m components.ocr_util:ocr_util.py:60 OCR引擎初始化成功
[32mINFO    [0m components.filing_data_manager:filing_data_manager.py:41 生成备案测试数据，场景: invalid
[32mINFO    [0m conftest:conftest.py:135 完成测试: test_required_fields_validation[chromium]
[32mINFO    [0m fixtures.filing_fixtures:filing_fixtures.py:199 备案测试完成: test_required_fields_validation[chromium]
[32mINFO    [0m conftest:conftest.py:57 结束测试类: TestSCFFilingExceptions
[32mINFO    [0m conftest:conftest.py:38 ============================================================
[32mINFO    [0m conftest:conftest.py:39 结束 SCF 备案流程测试会话
[32mINFO    [0m conftest:conftest.py:40 ============================================================
[32mINFO    [0m conftest:conftest.py:201 测试环境检查完成