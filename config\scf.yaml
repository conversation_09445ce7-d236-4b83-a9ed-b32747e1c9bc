# 供应链金融备案系统配置
# Supply Chain Finance (SCF) System Configuration

# 基础配置
base:
  name: "供应链金融备案系统"
  description: "Supply Chain Finance Registration System"
  version: "1.0.0"

# 系统URL配置
urls:
  base_url: "http://*************:8080"
  login_url: "/login"
  registration_url: "/registration"
  dashboard_url: "/dashboard"

# 登录凭证配置
credentials:
  # 标准用户
  standard_user:
    username: "scf_4nuioc"
    password: "Scf123456."
    description: "标准测试用户"
  
  # 管理员用户
  admin_user:
    username: "admin"
    password: "Admin123456."
    description: "管理员用户"
  
  # 测试用户
  test_user:
    username: "test_user"
    password: "Test123456."
    description: "测试用户"

# 专业机构配置
institutions:
  default: "天津中互金数据科技有限公司"
  alternatives:
    - "天津中互金数据科技有限公司"
    - "其他专业机构1"
    - "其他专业机构2"

# 文件上传配置
file_uploads:
  # 测试文件路径（相对于项目根目录）
  test_files:
    document: "data/test_files/异常信息共享系统V1.5需求.docx"
    excel: "data/test_files/格式二模版.xlsx"
    pdf_report: "data/test_files/供应链金融测试报告V1.6.0.pdf"
  
  # 文件类型限制
  allowed_extensions:
    - ".pdf"
    - ".doc"
    - ".docx"
    - ".xls"
    - ".xlsx"
    - ".jpg"
    - ".jpeg"
    - ".png"
  
  # 文件大小限制（MB）
  max_file_size: 50

# 表单默认数据
form_defaults:
  # 情况说明默认文本
  description_text: "测试"
  
  # 默认选择（是/否）
  default_option: "否"
  
  # 文本区域最大字符数
  max_description_length: 1000

# 超时配置
timeouts:
  # 页面加载超时（毫秒）
  page_load: 30000
  
  # 元素等待超时（毫秒）
  element_wait: 10000
  
  # 文件上传超时（毫秒）
  file_upload: 60000
  
  # 表单提交超时（毫秒）
  form_submit: 30000

# 重试配置
retry:
  # 最大重试次数
  max_attempts: 3
  
  # 重试间隔（秒）
  delay: 2
  
  # 验证码识别重试次数
  captcha_retry: 5

# OCR配置
ocr:
  # 置信度阈值
  confidence_threshold: 0.8
  
  # 是否保存失败的验证码图片
  save_failed_images: true
  
  # 失败图片保存目录
  failed_images_dir: "logs/failed_captcha"

# 日志配置
logging:
  # 日志级别
  level: "INFO"
  
  # 是否记录敏感信息（密码等）
  log_sensitive_data: false
  
  # 步骤日志详细程度
  step_detail_level: "NORMAL"

# 测试数据配置
test_data:
  # 业务合规测试数据
  business_compliance:
    bcVele:
      option: "否"
      description: "测试业务合规说明"
    bcVbsaroa:
      option: "否"
      description: "测试业务合规说明"
    bcCoo:
      option: "否"
      description: "测试业务合规说明"
    bcPcabsmrc:
      option: "否"
      description: "测试业务合规说明"
    bcFswscsv:
      option: "是"
      description: "测试业务合规说明"
    bcRosctedt:
      option: "否"
      description: "测试业务合规说明"
    bcSmflee:
      option: "否"
      description: "测试业务合规说明"
    bcCbscud:
      option: "否"
      description: "测试业务合规说明"
  
  # 系统合规测试数据
  system_compliance:
    scoSsdama:
      option: "是"
      description: "测试系统合规说明"
    scoSccmfd:
      option: "否"
      description: "测试系统合规说明"
    scoSml3:
      option: "否"
      description: "测试系统合规说明"
    scoSmdoa:
      option: "否"
      description: "测试系统合规说明"
  
  # 业务管理测试数据
  business_management:
    bmBctms:
      option: "否"
      description: "测试业务管理说明"
    bmCicrm:
      option: "否"
      description: "测试业务管理说明"
    bmNmcsf:
      option: "否"
      description: "测试业务管理说明"
    bmNfsadp:
      option: "否"
      description: "测试业务管理说明"
    bmCamm:
      option: "否"
      description: "测试业务管理说明"
    bmCiv:
      option: "否"
      description: "测试业务管理说明"
    bmNsuvc:
      option: "否"
      description: "测试业务管理说明"
    bmFvrap:
      option: "否"
      description: "测试业务管理说明"
    bmClba:
      option: "否"
      description: "测试业务管理说明"
    bmCfia:
      option: "否"
      description: "测试业务管理说明"
    bmAmlct:
      option: "否"
      description: "测试业务管理说明"
    bmSscdm:
      option: "否"
      description: "测试业务管理说明"

# 环境特定配置
environments:
  dev:
    debug: true
    log_level: "DEBUG"
    save_screenshots: true
    slow_motion: 1000
  
  test:
    debug: false
    log_level: "INFO"
    save_screenshots: true
    slow_motion: 0
  
  prod:
    debug: false
    log_level: "WARNING"
    save_screenshots: false
    slow_motion: 0
