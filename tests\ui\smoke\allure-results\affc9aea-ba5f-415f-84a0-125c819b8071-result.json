{"name": "test_login_failure_scenarios[chromium-invalid_user-\\u5e94\\u8be5\\u663e\\u793a\\u9519\\u8bef\\u4fe1\\u606f]", "status": "passed", "description": "测试登录失败场景", "steps": [{"name": "使用invalid_user凭证登录", "status": "passed", "steps": [{"name": "登录系统", "status": "broken", "statusDetails": {"message": "KeyError: 'locator'\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\workflows\\scf_registration_workflow.py\", line 123, in _login_to_system\n    self.login_page.login(username, password, auto_captcha=True)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 157, in login\n    self.click_account_login_tab()\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 58, in click_account_login_tab\n    self.click(self.account_tab)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 201, in impl\n    with StepContext(self.title.format(*args, **params), params):\n                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "steps": [{"name": "导航到登录页面", "status": "passed", "steps": [{"name": "导航到页面: 'http://172.18.12.128:8080/login'", "status": "passed", "parameters": [{"name": "url", "value": "'http://172.18.12.128:8080/login'"}, {"name": "wait_until", "value": "'domcontentloaded'"}], "start": *************, "stop": *************}], "parameters": [{"name": "base_url", "value": "'http://172.18.12.128:8080'"}], "start": *************, "stop": *************}, {"name": "执行登录操作", "status": "broken", "statusDetails": {"message": "KeyError: 'locator'\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 157, in login\n    self.click_account_login_tab()\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 58, in click_account_login_tab\n    self.click(self.account_tab)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 201, in impl\n    with StepContext(self.title.format(*args, **params), params):\n                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "steps": [{"name": "点击账号登录标签", "status": "broken", "statusDetails": {"message": "KeyError: 'locator'\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 58, in click_account_login_tab\n    self.click(self.account_tab)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 201, in impl\n    with StepContext(self.title.format(*args, **params), params):\n                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "start": *************, "stop": *************}], "parameters": [{"name": "username", "value": "'invalid_user'"}, {"name": "password", "value": "'invalid_password'"}, {"name": "<PERSON><PERSON>a", "value": "None"}, {"name": "auto_captcha", "value": "True"}], "start": *************, "stop": *************}], "parameters": [{"name": "credentials", "value": "{'username': 'invalid_user', 'password': 'invalid_password'}"}, {"name": "base_url", "value": "'http://172.18.12.128:8080'"}], "start": *************, "stop": *************}], "attachments": [{"name": "登录失败信息", "source": "8c6be4b6-9a4b-4fa9-960c-8944ad6a4cb2-attachment.txt", "type": "text/plain"}], "start": *************, "stop": *************}, {"name": "验证登录失败行为", "status": "passed", "start": *************, "stop": *************}], "attachments": [{"name": "log", "source": "1cb14289-4f80-4d5a-8f47-7fe189f79240-attachment.txt", "type": "text/plain"}, {"name": "stderr", "source": "26474856-47a4-431e-bb15-84b33a9630bf-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "browser_name", "value": "'chromium'"}, {"name": "credentials_key", "value": "'invalid_user'"}, {"name": "expected_behavior", "value": "'应该显示错误信息'"}], "start": 1751635102555, "stop": *************, "uuid": "4e7f482f-de18-4f8c-951c-dd6b861ef569", "historyId": "14f7173cd53e4fc1432a01c8e67b0401", "testCaseId": "a99cc8e5e6e05ba82a9dc1ae7049de1b", "fullName": "tests.ui.smoke.test_scf_login.TestSCFLogin#test_login_failure_scenarios", "labels": [{"name": "feature", "value": "用户登录"}, {"name": "epic", "value": "供应链金融备案系统"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "登录失败场景"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "tests.ui.smoke"}, {"name": "suite", "value": "test_scf_login"}, {"name": "subSuite", "value": "TestSC<PERSON><PERSON>in"}, {"name": "host", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "thread", "value": "21436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.ui.smoke.test_scf_login"}]}