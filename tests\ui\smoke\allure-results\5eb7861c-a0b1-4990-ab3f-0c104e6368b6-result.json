{"name": "test_standard_user_login_success[chromium]", "status": "broken", "statusDetails": {"message": "KeyError: 'locator'", "trace": "test_scf_login.py:30: in test_standard_user_login_success\n    workflow._login_to_system(credentials, scf_base_url)\n..\\..\\..\\workflows\\scf_registration_workflow.py:123: in _login_to_system\n    self.login_page.login(username, password, auto_captcha=True)\n..\\..\\..\\pages\\scf_login_page.py:157: in login\n    self.click_account_login_tab()\n..\\..\\..\\pages\\scf_login_page.py:58: in click_account_login_tab\n    self.click(self.account_tab)\nE   KeyError: 'locator'"}, "description": "测试标准用户成功登录", "steps": [{"name": "执行标准用户登录", "status": "broken", "statusDetails": {"message": "KeyError: 'locator'\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\tests\\ui\\smoke\\test_scf_login.py\", line 30, in test_standard_user_login_success\n    workflow._login_to_system(credentials, scf_base_url)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\workflows\\scf_registration_workflow.py\", line 123, in _login_to_system\n    self.login_page.login(username, password, auto_captcha=True)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 157, in login\n    self.click_account_login_tab()\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 58, in click_account_login_tab\n    self.click(self.account_tab)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 201, in impl\n    with StepContext(self.title.format(*args, **params), params):\n                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "steps": [{"name": "登录系统", "status": "broken", "statusDetails": {"message": "KeyError: 'locator'\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\workflows\\scf_registration_workflow.py\", line 123, in _login_to_system\n    self.login_page.login(username, password, auto_captcha=True)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 157, in login\n    self.click_account_login_tab()\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 58, in click_account_login_tab\n    self.click(self.account_tab)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 201, in impl\n    with StepContext(self.title.format(*args, **params), params):\n                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "steps": [{"name": "导航到登录页面", "status": "passed", "steps": [{"name": "导航到页面: 'http://*************:8080/login'", "status": "passed", "parameters": [{"name": "url", "value": "'http://*************:8080/login'"}, {"name": "wait_until", "value": "'domcontentloaded'"}], "start": *************, "stop": *************}], "parameters": [{"name": "base_url", "value": "'http://*************:8080'"}], "start": *************, "stop": *************}, {"name": "执行登录操作", "status": "broken", "statusDetails": {"message": "KeyError: 'locator'\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 157, in login\n    self.click_account_login_tab()\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 58, in click_account_login_tab\n    self.click(self.account_tab)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 201, in impl\n    with StepContext(self.title.format(*args, **params), params):\n                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "steps": [{"name": "点击账号登录标签", "status": "broken", "statusDetails": {"message": "KeyError: 'locator'\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 58, in click_account_login_tab\n    self.click(self.account_tab)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 201, in impl\n    with StepContext(self.title.format(*args, **params), params):\n                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "start": *************, "stop": *************}], "parameters": [{"name": "username", "value": "'scf_4nuioc'"}, {"name": "password", "value": "'Scf123456.'"}, {"name": "<PERSON><PERSON>a", "value": "None"}, {"name": "auto_captcha", "value": "True"}], "start": *************, "stop": *************}], "parameters": [{"name": "credentials", "value": "{'username': 'scf_4nuioc', 'password': 'Scf123456.', 'description': '标准测试用户'}"}, {"name": "base_url", "value": "'http://*************:8080'"}], "start": *************, "stop": *************}], "start": *************, "stop": *************}], "attachments": [{"name": "失败截图", "source": "4cadfd61-72b6-43cc-90ab-7ed32ed0f964-attachment.png", "type": "image/png"}, {"name": "页面HTML", "source": "afe24c24-bb08-4333-b872-8cecec2b8a33-attachment.html", "type": "text/html"}, {"name": "当前URL", "source": "57cb953e-ab67-4285-b6cd-58d033e6b787-attachment.txt", "type": "text/plain"}, {"name": "log", "source": "4f1fd5ae-f60b-47c1-bb13-16ab1cadf3ff-attachment.txt", "type": "text/plain"}, {"name": "stderr", "source": "e5b589ec-8ab9-43f3-bf56-b350732ce566-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "browser_name", "value": "'chromium'"}], "start": 1751635100695, "stop": 1751635101337, "uuid": "b6b89dac-aa46-4353-ad00-d0c8a4c56d71", "historyId": "83c32d878021effc5e3dcc4d134c4bd1", "testCaseId": "2cf6f352a9af813bd18233473cb242da", "fullName": "tests.ui.smoke.test_scf_login.TestSCFLogin#test_standard_user_login_success", "labels": [{"name": "feature", "value": "用户登录"}, {"name": "epic", "value": "供应链金融备案系统"}, {"name": "story", "value": "标准用户登录"}, {"name": "severity", "value": "blocker"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "tests.ui.smoke"}, {"name": "suite", "value": "test_scf_login"}, {"name": "subSuite", "value": "TestSC<PERSON><PERSON>in"}, {"name": "host", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "thread", "value": "21436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.ui.smoke.test_scf_login"}]}