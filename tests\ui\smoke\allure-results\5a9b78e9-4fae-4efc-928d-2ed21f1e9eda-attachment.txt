INFO     fixtures.scf_fixtures:scf_fixtures.py:79 准备SCF登录凭证
INFO     components.ocr_util:ocr_util.py:60 OCR引擎初始化成功
INFO     SCFRegistrationWorkflow:scf_registration_workflow.py:110 开始登录系统
INFO     SCFLoginPage:scf_login_page.py:55 导航到登录页面: http://172.18.12.128:8080/login
INFO     pages.base_page:base_page.py:30 导航到页面: http://172.18.12.128:8080/login
INFO     SCFLoginPage:scf_login_page.py:448 开始登录流程，用户名: scf_4nuioc
INFO     SCFLoginPage:scf_login_page.py:366 开始登录流程（带重试），用户名: scf_4nuioc, 最大重试次数: 5
INFO     SCFLoginPage:scf_login_page.py:62 点击账号登录标签
INFO     pages.base_page:base_page.py:190 等待元素 text=账号登录 状态: visible
DEBUG    components.retry_util:retry_util.py:87 执行函数 click，尝试 1/3
INFO     pages.base_page:base_page.py:106 点击元素: text=账号登录
DEBUG    pages.base_page:base_page.py:65 元素 text=账号登录 已稳定
INFO     SCFLoginPage:scf_login_page.py:95 输入用户名: scf_4nuioc
DEBUG    components.retry_util:retry_util.py:87 执行函数 fill，尝试 1/3
INFO     pages.base_page:base_page.py:130 填充文本到元素 input[placeholder='请输入账号']: scf_4nuioc
DEBUG    pages.base_page:base_page.py:65 元素 input[placeholder='请输入账号'] 已稳定
INFO     SCFLoginPage:scf_login_page.py:119 输入密码
DEBUG    components.retry_util:retry_util.py:87 执行函数 fill，尝试 1/3
INFO     pages.base_page:base_page.py:130 填充文本到元素 input[type='password']: Scf123456.
DEBUG    pages.base_page:base_page.py:65 元素 input[type='password'] 已稳定
INFO     SCFLoginPage:scf_login_page.py:378 登录尝试 1/5
INFO     SCFLoginPage:scf_login_page.py:257 开始自动识别验证码，最大重试次数: 1, 最小置信度: 0.6
INFO     SCFLoginPage:scf_login_page.py:261 验证码识别尝试 1/1
DEBUG    SCFLoginPage:scf_login_page.py:177 尝试选择器: .arco-image img
INFO     pages.base_page:base_page.py:190 等待元素 .arco-image img 状态: visible
INFO     SCFLoginPage:scf_login_page.py:181 找到验证码元素，使用选择器: .arco-image img
INFO     SCFLoginPage:scf_login_page.py:200 验证码图片已保存到: logs/captcha_debug_attempt_1.png
DEBUG    PIL.PngImagePlugin:PngImagePlugin.py:201 STREAM b'IHDR' 16 13
DEBUG    PIL.PngImagePlugin:PngImagePlugin.py:201 STREAM b'sRGB' 41 1
DEBUG    PIL.PngImagePlugin:PngImagePlugin.py:201 STREAM b'IDAT' 54 4961
DEBUG    PIL.PngImagePlugin:PngImagePlugin.py:201 STREAM b'IHDR' 16 13
DEBUG    PIL.PngImagePlugin:PngImagePlugin.py:201 STREAM b'sRGB' 41 1
DEBUG    PIL.PngImagePlugin:PngImagePlugin.py:201 STREAM b'IDAT' 54 4961
WARNING  components.ocr_util:ocr_util.py:260 图像复杂度分析失败: name 'ImageFilter' is not defined
INFO     components.ocr_util:ocr_util.py:87 OCR识别结果: 1647, 置信度: 0.8
INFO     SCFLoginPage:scf_login_page.py:227 尝试 1 - OCR识别结果: '1647', 置信度: 0.800
INFO     SCFLoginPage:scf_login_page.py:271 验证码识别成功: '1647', 置信度: 0.800
INFO     SCFLoginPage:scf_login_page.py:132 输入图形验证码: 1647
DEBUG    components.retry_util:retry_util.py:87 执行函数 fill，尝试 1/3
INFO     pages.base_page:base_page.py:130 填充文本到元素 input[placeholder='请输入图形验证码']: 1647
DEBUG    pages.base_page:base_page.py:65 元素 input[placeholder='请输入图形验证码'] 已稳定
INFO     SCFLoginPage:scf_login_page.py:387 尝试 1 - 使用验证码: 1647
INFO     SCFLoginPage:scf_login_page.py:319 点击登录按钮
DEBUG    components.retry_util:retry_util.py:87 执行函数 click，尝试 1/3
INFO     pages.base_page:base_page.py:106 点击元素: button[type='submit']
DEBUG    pages.base_page:base_page.py:65 元素 button[type='submit'] 已稳定
INFO     SCFLoginPage:scf_login_page.py:393 检查登录状态...
INFO     SCFLoginPage:scf_login_page.py:346 已离开登录页面，当前URL: http://172.18.12.128:8080/system-register/system-register
INFO     SCFLoginPage:scf_login_page.py:395 登录成功！尝试次数: 1
INFO     SCFLoginPage:scf_login_page.py:467 登录操作完成
INFO     SCFLoginPage:scf_login_page.py:523 验证登录成功状态
INFO     pages.base_page:base_page.py:410 等待页面加载状态: networkidle
INFO     SCFLoginPage:scf_login_page.py:538 登录成功验证通过
INFO     SCFRegistrationWorkflow:scf_registration_workflow.py:127 系统登录成功
INFO     SCFRegistrationWorkflow:scf_registration_workflow.py:132 进入备案申请
INFO     SCFRegistrationPage:scf_registration_page.py:51 点击备案登记菜单
DEBUG    components.retry_util:retry_util.py:87 执行函数 click，尝试 1/3
INFO     pages.base_page:base_page.py:106 点击元素: text=备案登记
DEBUG    pages.base_page:base_page.py:65 元素 text=备案登记 已稳定
INFO     SCFRegistrationPage:scf_registration_page.py:58 点击备案申请按钮
DEBUG    components.retry_util:retry_util.py:87 执行函数 click，尝试 1/3
INFO     pages.base_page:base_page.py:106 点击元素: role=button[name='备案申请']
DEBUG    pages.base_page:base_page.py:65 元素 role=button[name='备案申请'] 已稳定
INFO     SCFRegistrationPage:scf_registration_page.py:168 点击下一步按钮
DEBUG    components.retry_util:retry_util.py:87 执行函数 click，尝试 1/3
INFO     pages.base_page:base_page.py:106 点击元素: role=button[name='下一步']
DEBUG    pages.base_page:base_page.py:65 元素 role=button[name='下一步'] 已稳定
INFO     pages.base_page:base_page.py:410 等待页面加载状态: networkidle
INFO     SCFRegistrationPage:scf_registration_page.py:235 填写业务合规表单
INFO     SCFRegistrationPage:scf_registration_page.py:120 在区域 bcVele 选择选项: 否
DEBUG    components.retry_util:retry_util.py:87 执行函数 click，尝试 1/3
INFO     pages.base_page:base_page.py:106 点击元素: #bcVele text=否
WARNING  components.retry_util:retry_util.py:97 函数 click 第 1 次尝试失败: Locator.wait_for: Unexpected token "=" while parsing css selector "#bcVele text=否". Did you mean to CSS.escape it?
Call log:
  - waiting for #bcVele text=否 to be visible

DEBUG    components.retry_util:retry_util.py:115 等待 0.47 秒后重试
DEBUG    components.retry_util:retry_util.py:87 执行函数 click，尝试 2/3
INFO     pages.base_page:base_page.py:106 点击元素: #bcVele text=否
WARNING  components.retry_util:retry_util.py:97 函数 click 第 2 次尝试失败: Locator.wait_for: Unexpected token "=" while parsing css selector "#bcVele text=否". Did you mean to CSS.escape it?
Call log:
  - waiting for #bcVele text=否 to be visible

DEBUG    components.retry_util:retry_util.py:115 等待 1.03 秒后重试
DEBUG    components.retry_util:retry_util.py:87 执行函数 click，尝试 3/3
INFO     pages.base_page:base_page.py:106 点击元素: #bcVele text=否
WARNING  components.retry_util:retry_util.py:97 函数 click 第 3 次尝试失败: Locator.wait_for: Unexpected token "=" while parsing css selector "#bcVele text=否". Did you mean to CSS.escape it?
Call log:
  - waiting for #bcVele text=否 to be visible

ERROR    components.retry_util:retry_util.py:124 函数 click 在 3 次尝试后仍然失败