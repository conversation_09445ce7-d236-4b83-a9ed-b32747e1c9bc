{"name": "test_login_failure_scenarios[chromium-empty_credentials-\\u5e94\\u8be5\\u63d0\\u793a\\u8f93\\u5165\\u7528\\u6237\\u540d\\u548c\\u5bc6\\u7801]", "status": "passed", "description": "测试登录失败场景", "steps": [{"name": "使用empty_credentials凭证登录", "status": "passed", "steps": [{"name": "登录系统", "status": "failed", "statusDetails": {"message": "AssertionError: 登录失败，仍在登录页面: http://*************:8080/login\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\workflows\\scf_registration_workflow.py\", line 126, in _login_to_system\n    self.login_page.verify_login_success()\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 366, in verify_login_success\n    assert self.login_url not in current_url, f\"登录失败，仍在登录页面: {current_url}\"\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "steps": [{"name": "导航到登录页面", "status": "passed", "steps": [{"name": "导航到页面: 'http://*************:8080/login'", "status": "passed", "parameters": [{"name": "url", "value": "'http://*************:8080/login'"}, {"name": "wait_until", "value": "'domcontentloaded'"}], "start": 1751853975159, "stop": 1751853976014}], "parameters": [{"name": "base_url", "value": "'http://*************:8080'"}], "start": 1751853975159, "stop": 1751853976014}, {"name": "执行登录操作", "status": "passed", "steps": [{"name": "点击账号登录标签", "status": "passed", "steps": [{"name": "等待元素出现: 'text=账号登录'", "status": "passed", "parameters": [{"name": "locator", "value": "'text=账号登录'"}, {"name": "state", "value": "'visible'"}, {"name": "timeout", "value": "10000"}], "start": 1751853976015, "stop": 1751853976223}, {"name": "智能点击: 'text=账号登录'", "status": "passed", "steps": [{"name": "等待元素稳定: 'text=账号登录'", "status": "passed", "parameters": [{"name": "locator", "value": "'text=账号登录'"}, {"name": "timeout", "value": "None"}], "start": 1751853976224, "stop": 1751853976559}], "parameters": [{"name": "locator", "value": "'text=账号登录'"}, {"name": "force", "value": "False"}, {"name": "timeout", "value": "None"}], "start": 1751853976223, "stop": 1751853976617}], "start": 1751853976015, "stop": 1751853977623}, {"name": "输入用户名: ''", "status": "passed", "steps": [{"name": "智能填充: 'input[placeholder='请输入账号']' = ''", "status": "passed", "steps": [{"name": "等待元素稳定: 'input[placeholder='请输入账号']'", "status": "passed", "parameters": [{"name": "locator", "value": "'input[placeholder='请输入账号']'"}, {"name": "timeout", "value": "None"}], "start": 1751853977624, "stop": 1751853977977}], "parameters": [{"name": "locator", "value": "'input[placeholder='请输入账号']'"}, {"name": "text", "value": "''"}, {"name": "clear_first", "value": "True"}, {"name": "timeout", "value": "None"}], "start": 1751853977624, "stop": 1751853978024}], "parameters": [{"name": "username", "value": "''"}], "start": 1751853977624, "stop": 1751853978024}, {"name": "输入密码", "status": "passed", "steps": [{"name": "智能填充: 'input[type='password']' = ''", "status": "passed", "steps": [{"name": "等待元素稳定: 'input[type='password']'", "status": "passed", "parameters": [{"name": "locator", "value": "'input[type='password']'"}, {"name": "timeout", "value": "None"}], "start": 1751853978024, "stop": 1751853978379}], "parameters": [{"name": "locator", "value": "'input[type='password']'"}, {"name": "text", "value": "''"}, {"name": "clear_first", "value": "True"}, {"name": "timeout", "value": "None"}], "start": 1751853978024, "stop": 1751853978419}], "parameters": [{"name": "password", "value": "''"}], "start": 1751853978024, "stop": 1751853978419}, {"name": "自动识别并输入验证码", "status": "passed", "steps": [{"name": "等待元素出现: '.arco-image img'", "status": "passed", "parameters": [{"name": "locator", "value": "'.arco-image img'"}, {"name": "state", "value": "'visible'"}, {"name": "timeout", "value": "10000"}], "start": 1751853978419, "stop": 1751853978424}, {"name": "输入图形验证码: '2824'", "status": "passed", "steps": [{"name": "智能填充: 'input[placeholder='请输入图形验证码']' = '2824'", "status": "passed", "steps": [{"name": "等待元素稳定: 'input[placeholder='请输入图形验证码']'", "status": "passed", "parameters": [{"name": "locator", "value": "'input[placeholder='请输入图形验证码']'"}, {"name": "timeout", "value": "None"}], "start": 1751853979516, "stop": 1751853979885}], "parameters": [{"name": "locator", "value": "'input[placeholder='请输入图形验证码']'"}, {"name": "text", "value": "'2824'"}, {"name": "clear_first", "value": "True"}, {"name": "timeout", "value": "None"}], "start": 1751853979516, "stop": 1751853979916}], "parameters": [{"name": "<PERSON><PERSON>a", "value": "'2824'"}], "start": 1751853979515, "stop": 1751853979916}], "parameters": [{"name": "max_retries", "value": "3"}], "start": 1751853978419, "stop": 1751853979916}, {"name": "点击登录按钮", "status": "passed", "steps": [{"name": "智能点击: 'button[type='submit']'", "status": "passed", "steps": [{"name": "等待元素稳定: 'button[type='submit']'", "status": "passed", "parameters": [{"name": "locator", "value": "'button[type='submit']'"}, {"name": "timeout", "value": "None"}], "start": 1751853979916, "stop": 1751853980262}], "parameters": [{"name": "locator", "value": "'button[type='submit']'"}, {"name": "force", "value": "False"}, {"name": "timeout", "value": "None"}], "start": 1751853979916, "stop": 1751853980346}], "start": 1751853979916, "stop": 1751853980346}], "parameters": [{"name": "username", "value": "''"}, {"name": "password", "value": "''"}, {"name": "<PERSON><PERSON>a", "value": "None"}, {"name": "auto_captcha", "value": "True"}], "start": 1751853976015, "stop": 1751853980346}, {"name": "验证登录成功", "status": "failed", "statusDetails": {"message": "AssertionError: 登录失败，仍在登录页面: http://*************:8080/login\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 366, in verify_login_success\n    assert self.login_url not in current_url, f\"登录失败，仍在登录页面: {current_url}\"\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "steps": [{"name": "等待页面加载完成", "status": "passed", "parameters": [{"name": "state", "value": "'networkidle'"}, {"name": "timeout", "value": "None"}], "start": 1751853980347, "stop": 1751853980348}], "parameters": [{"name": "expected_url_pattern", "value": "None"}], "start": 1751853980347, "stop": 1751853980348}], "parameters": [{"name": "credentials", "value": "{'username': '', 'password': ''}"}, {"name": "base_url", "value": "'http://*************:8080'"}], "start": 1751853975159, "stop": 1751853980349}], "attachments": [{"name": "登录失败信息", "source": "ace189de-faec-40b9-a233-fd5ba0609c72-attachment.txt", "type": "text/plain"}], "start": 1751853975159, "stop": 1751853980359}, {"name": "验证登录失败行为", "status": "passed", "start": 1751853980359, "stop": 1751853980359}], "attachments": [{"name": "log", "source": "ce668b39-2c2c-421a-bc1e-5741e0b9214c-attachment.txt", "type": "text/plain"}, {"name": "stdout", "source": "16cee056-9ad5-483d-884a-85373813622c-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "browser_name", "value": "'chromium'"}, {"name": "credentials_key", "value": "'empty_credentials'"}, {"name": "expected_behavior", "value": "'应该提示输入用户名和密码'"}], "start": 1751853975119, "stop": 1751853980359, "uuid": "bb20e51e-ea27-4766-b70d-261a0387aa48", "historyId": "d370f9b387261f6b14d16c225a143019", "testCaseId": "a99cc8e5e6e05ba82a9dc1ae7049de1b", "fullName": "tests.ui.smoke.test_scf_login.TestSCFLogin#test_login_failure_scenarios", "labels": [{"name": "feature", "value": "用户登录"}, {"name": "epic", "value": "供应链金融备案系统"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "登录失败场景"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "tests.ui.smoke"}, {"name": "suite", "value": "test_scf_login"}, {"name": "subSuite", "value": "TestSC<PERSON><PERSON>in"}, {"name": "host", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "thread", "value": "14940-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.ui.smoke.test_scf_login"}]}