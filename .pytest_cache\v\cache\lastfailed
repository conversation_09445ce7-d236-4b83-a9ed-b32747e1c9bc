{"tests/ui/smoke/test_login.py::test_login[chromium]": true, "tests/ui/smoke/test_login.py::TestLogin::test_standard_user_login_success[chromium]": true, "tests/ui/smoke/test_login.py::TestLogin::test_admin_user_login_success[chromium]": true, "tests/ui/smoke/test_login.py::TestLogin::test_login_with_invalid_credentials[chromium-invalid_user-invalid_pass-\\u7528\\u6237\\u540d\\u6216\\u5bc6\\u7801\\u9519\\u8bef]": true, "tests/ui/smoke/test_login.py::TestLogin::test_login_page_elements[chromium]": true, "tests/ui/smoke/test_login.py::TestLogin::test_login_with_invalid_credentials[chromium---\\u8bf7\\u8f93\\u5165\\u7528\\u6237\\u540d\\u548c\\u5bc6\\u7801]": true, "tests/ui/smoke/test_login.py::TestLogin::test_login_with_invalid_credentials[chromium-valid_user--\\u8bf7\\u8f93\\u5165\\u5bc6\\u7801]": true, "tests/ui/smoke/test_login.py::TestLogin::test_login_with_invalid_credentials[chromium--valid_pass-\\u8bf7\\u8f93\\u5165\\u7528\\u6237\\u540d]": true, "tests/ui/smoke/test_scf_login.py::TestSCFLogin::test_admin_user_login_success[chromium]": true, "tests/ui/e2e/test_user_journey.py": true, "tests/ui/smoke/test_scf_registration.py::TestSCFRegistration::test_form_filling_functionality[chromium]": true, "tests/ui/smoke/test_scf_registration.py::TestSCFRegistration::test_minimal_registration_process[chromium]": true, "tests/ui/filing/test_scf_filing_exceptions.py::TestSCFFilingExceptions::test_required_fields_validation[chromium]": true, "tests/ui/filing/test_scf_filing_exceptions.py::TestSCFFilingExceptions::test_invalid_file_format[chromium]": true, "tests/ui/filing/test_scf_filing_exceptions.py::TestSCFFilingExceptions::test_file_size_limit[chromium]": true, "tests/ui/filing/test_scf_filing_exceptions.py::TestSCFFilingExceptions::test_network_interruption_handling[chromium]": true, "tests/ui/filing/test_scf_filing_exceptions.py::TestSCFFilingExceptions::test_duplicate_submission_handling[chromium]": true, "tests/ui/filing/test_scf_filing_exceptions.py::TestSCFFilingExceptions::test_session_timeout_handling[chromium]": true, "tests/ui/filing/test_scf_filing_exceptions.py::TestSCFFilingExceptions::test_invalid_data_input[chromium]": true, "tests/ui/filing/test_scf_filing_exceptions.py::TestSCFFilingExceptions::test_permission_validation[chromium]": true, "tests/ui/filing/test_scf_filing_boundary.py::TestSCFFilingBoundary::test_character_length_boundaries[chromium]": true, "tests/ui/filing/test_scf_filing_boundary.py::TestSCFFilingBoundary::test_file_size_boundaries[chromium]": true, "tests/ui/filing/test_scf_filing_boundary.py::TestSCFFilingBoundary::test_numeric_range_boundaries[chromium]": true, "tests/ui/filing/test_scf_filing_boundary.py::TestSCFFilingBoundary::test_concurrent_operations_boundary[chromium]": true, "tests/ui/filing/test_scf_filing_boundary.py::TestSCFFilingBoundary::test_browser_compatibility_boundary[chromium]": true, "tests/ui/filing/test_scf_filing_boundary.py::TestSCFFilingBoundary::test_time_boundary_conditions[chromium]": true, "tests/ui/filing/test_scf_filing_normal.py::TestSCFFilingNormal::test_complete_filing_process_success[chromium]": true, "tests/ui/filing/test_scf_filing_normal.py::TestSCFFilingNormal::test_basic_materials_upload[chromium]": true, "tests/ui/filing/test_scf_filing_normal.py::TestSCFFilingNormal::test_supplementary_materials_upload[chromium]": true, "tests/ui/filing/test_scf_filing_normal.py::TestSCFFilingNormal::test_compliance_check_process[chromium]": true, "tests/ui/filing/test_scf_filing_normal.py::TestSCFFilingNormal::test_institution_selection[chromium]": true, "tests/ui/filing/test_scf_filing_normal.py::TestSCFFilingNormal::test_application_save[chromium]": true, "tests/ui/filing/test_scf_filing_normal.py::TestSCFFilingNormal::test_multiple_file_upload[chromium]": true}