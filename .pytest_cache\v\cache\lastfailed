{"tests/ui/smoke/test_login.py::test_login[chromium]": true, "tests/ui/smoke/test_login.py::TestLogin::test_standard_user_login_success[chromium]": true, "tests/ui/smoke/test_login.py::TestLogin::test_admin_user_login_success[chromium]": true, "tests/ui/smoke/test_login.py::TestLogin::test_login_with_invalid_credentials[chromium-invalid_user-invalid_pass-\\u7528\\u6237\\u540d\\u6216\\u5bc6\\u7801\\u9519\\u8bef]": true, "tests/ui/smoke/test_login.py::TestLogin::test_login_page_elements[chromium]": true, "tests/ui/smoke/test_login.py::TestLogin::test_login_with_invalid_credentials[chromium---\\u8bf7\\u8f93\\u5165\\u7528\\u6237\\u540d\\u548c\\u5bc6\\u7801]": true, "tests/ui/smoke/test_login.py::TestLogin::test_login_with_invalid_credentials[chromium-valid_user--\\u8bf7\\u8f93\\u5165\\u5bc6\\u7801]": true, "tests/ui/smoke/test_login.py::TestLogin::test_login_with_invalid_credentials[chromium--valid_pass-\\u8bf7\\u8f93\\u5165\\u7528\\u6237\\u540d]": true, "tests/ui/smoke/test_scf_login.py::TestSCFLogin::test_standard_user_login_success[chromium]": true, "tests/ui/smoke/test_scf_login.py::TestSCFLogin::test_admin_user_login_success[chromium]": true, "tests/ui/smoke/test_scf_login.py::TestSCFLogin::test_login_failure_scenarios[chromium-empty_credentials-\\u5e94\\u8be5\\u63d0\\u793a\\u8f93\\u5165\\u7528\\u6237\\u540d\\u548c\\u5bc6\\u7801]": true, "tests/ui/smoke/test_scf_login.py::TestSCFLogin::test_captcha_functionality[chromium]": true, "tests/ui/e2e/test_user_journey.py": true}