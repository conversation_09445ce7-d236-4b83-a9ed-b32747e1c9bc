{"name": "test_admin_user_login_success[chromium]", "status": "broken", "statusDetails": {"message": "AttributeError: 'SCFLoginPage' object has no attribute 'take_screenshot_for_report'", "trace": "pages\\scf_login_page.py:110: in auto_input_captcha\n    self.wait_for_element(self.captcha_image)\npages\\base_page.py:192: in wait_for_element\n    element.wait_for(state=state, timeout=timeout or self.default_timeout)\n.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py:17937: in wait_for\n    self._sync(self._impl_obj.wait_for(timeout=timeout, state=state))\n.venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py:693: in wait_for\n    await self._frame.wait_for_selector(\n.venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py:341: in wait_for_selector\n    await self._channel.send(\n.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:69: in send\n    return await self._connection.wrap_api_call(\n.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:558: in wrap_api_call\n    raise rewrite_error(error, f\"{parsed_st['apiName']}: {error}\") from None\nE   playwright._impl._errors.TimeoutError: Locator.wait_for: Timeout 30000ms exceeded.\nE   Call log:\nE     - waiting for locator(\".captcha-image\") to be visible\n\nDuring handling of the above exception, another exception occurred:\ntests\\ui\\smoke\\test_scf_login.py:55: in test_admin_user_login_success\n    workflow._login_to_system(credentials, scf_base_url)\nworkflows\\scf_registration_workflow.py:123: in _login_to_system\n    self.login_page.login(username, password, auto_captcha=True)\npages\\scf_login_page.py:170: in login\n    self.auto_input_captcha()\npages\\scf_login_page.py:133: in auto_input_captcha\n    self.take_screenshot_for_report(\"验证码识别失败\")\n    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nE   AttributeError: 'SCFLoginPage' object has no attribute 'take_screenshot_for_report'"}, "description": "测试管理员用户成功登录", "steps": [{"name": "执行管理员登录", "status": "broken", "statusDetails": {"message": "AttributeError: 'SCFLoginPage' object has no attribute 'take_screenshot_for_report'\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\tests\\ui\\smoke\\test_scf_login.py\", line 55, in test_admin_user_login_success\n    workflow._login_to_system(credentials, scf_base_url)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\workflows\\scf_registration_workflow.py\", line 123, in _login_to_system\n    self.login_page.login(username, password, auto_captcha=True)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 170, in login\n    self.auto_input_captcha()\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 133, in auto_input_captcha\n    self.take_screenshot_for_report(\"验证码识别失败\")\n    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "steps": [{"name": "登录系统", "status": "broken", "statusDetails": {"message": "AttributeError: 'SCFLoginPage' object has no attribute 'take_screenshot_for_report'\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\workflows\\scf_registration_workflow.py\", line 123, in _login_to_system\n    self.login_page.login(username, password, auto_captcha=True)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 170, in login\n    self.auto_input_captcha()\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 133, in auto_input_captcha\n    self.take_screenshot_for_report(\"验证码识别失败\")\n    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "steps": [{"name": "导航到登录页面", "status": "passed", "steps": [{"name": "导航到页面: 'http://*************:8080/login'", "status": "passed", "parameters": [{"name": "url", "value": "'http://*************:8080/login'"}, {"name": "wait_until", "value": "'domcontentloaded'"}], "start": 1751637731934, "stop": 1751637732634}], "parameters": [{"name": "base_url", "value": "'http://*************:8080'"}], "start": 1751637731929, "stop": 1751637732634}, {"name": "执行登录操作", "status": "broken", "statusDetails": {"message": "AttributeError: 'SCFLoginPage' object has no attribute 'take_screenshot_for_report'\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 170, in login\n    self.auto_input_captcha()\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 133, in auto_input_captcha\n    self.take_screenshot_for_report(\"验证码识别失败\")\n    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "steps": [{"name": "点击账号登录标签", "status": "passed", "steps": [{"name": "智能点击: 'text=账号登录'", "status": "passed", "steps": [{"name": "等待元素稳定: 'text=账号登录'", "status": "passed", "parameters": [{"name": "locator", "value": "'text=账号登录'"}, {"name": "timeout", "value": "None"}], "start": 1751637732635, "stop": 1751637733114}], "parameters": [{"name": "locator", "value": "'text=账号登录'"}, {"name": "force", "value": "False"}, {"name": "timeout", "value": "None"}], "start": 1751637732635, "stop": 1751637733209}], "start": 1751637732635, "stop": 1751637733209}, {"name": "输入用户名: 'admin'", "status": "passed", "steps": [{"name": "智能填充: 'role=textbox[name='请输入账号']' = 'admin'", "status": "passed", "steps": [{"name": "等待元素稳定: 'role=textbox[name='请输入账号']'", "status": "passed", "parameters": [{"name": "locator", "value": "'role=textbox[name='请输入账号']'"}, {"name": "timeout", "value": "None"}], "start": 1751637733210, "stop": 1751637733609}], "parameters": [{"name": "locator", "value": "'role=textbox[name='请输入账号']'"}, {"name": "text", "value": "'admin'"}, {"name": "clear_first", "value": "True"}, {"name": "timeout", "value": "None"}], "start": 1751637733210, "stop": 1751637733692}], "parameters": [{"name": "username", "value": "'admin'"}], "start": 1751637733209, "stop": 1751637733692}, {"name": "输入密码", "status": "passed", "steps": [{"name": "智能填充: 'role=textbox[name='请输入密码']' = 'Admin123456.'", "status": "passed", "steps": [{"name": "等待元素稳定: 'role=textbox[name='请输入密码']'", "status": "passed", "parameters": [{"name": "locator", "value": "'role=textbox[name='请输入密码']'"}, {"name": "timeout", "value": "None"}], "start": 1751637733694, "stop": 1751637734070}], "parameters": [{"name": "locator", "value": "'role=textbox[name='请输入密码']'"}, {"name": "text", "value": "'Admin123456.'"}, {"name": "clear_first", "value": "True"}, {"name": "timeout", "value": "None"}], "start": 1751637733693, "stop": 1751637734157}], "parameters": [{"name": "password", "value": "'Admin123456.'"}], "start": 1751637733692, "stop": 1751637734157}, {"name": "自动识别并输入验证码", "status": "broken", "statusDetails": {"message": "AttributeError: 'SCFLoginPage' object has no attribute 'take_screenshot_for_report'\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 133, in auto_input_captcha\n    self.take_screenshot_for_report(\"验证码识别失败\")\n    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "steps": [{"name": "等待元素出现: '.captcha-image'", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.TimeoutError: Locator.wait_for: Timeout 30000ms exceeded.\nCall log:\n  - waiting for locator(\".captcha-image\") to be visible\n\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 192, in wait_for_element\n    element.wait_for(state=state, timeout=timeout or self.default_timeout)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 17937, in wait_for\n    self._sync(self._impl_obj.wait_for(timeout=timeout, state=state))\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 115, in _sync\n    return task.result()\n           ^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py\", line 693, in wait_for\n    await self._frame.wait_for_selector(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py\", line 341, in wait_for_selector\n    await self._channel.send(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 69, in send\n    return await self._connection.wrap_api_call(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 558, in wrap_api_call\n    raise rewrite_error(error, f\"{parsed_st['apiName']}: {error}\") from None\n"}, "parameters": [{"name": "locator", "value": "'.captcha-image'"}, {"name": "state", "value": "'visible'"}, {"name": "timeout", "value": "None"}], "start": 1751637734158, "stop": 1751637764175}], "start": 1751637734157, "stop": 1751637764176}], "parameters": [{"name": "username", "value": "'admin'"}, {"name": "password", "value": "'Admin123456.'"}, {"name": "<PERSON><PERSON>a", "value": "None"}, {"name": "auto_captcha", "value": "True"}], "start": 1751637732634, "stop": 1751637764179}], "parameters": [{"name": "credentials", "value": "{'username': 'admin', 'password': 'Admin123456.', 'description': '管理员用户'}"}, {"name": "base_url", "value": "'http://*************:8080'"}], "start": 1751637731929, "stop": 1751637764185}], "start": 1751637731929, "stop": 1751637764187}], "attachments": [{"name": "失败截图", "source": "1b81f40a-2882-40fd-95d1-63ee59ec3296-attachment.png", "type": "image/png"}, {"name": "页面HTML", "source": "58336bb0-afb1-4bc4-b156-05b09ea9e3cf-attachment.html", "type": "text/html"}, {"name": "当前URL", "source": "4b20897c-354e-4b67-9221-eba1bd0bc310-attachment.txt", "type": "text/plain"}, {"name": "log", "source": "6ff15675-29b2-4ba8-9749-94190833c466-attachment.txt", "type": "text/plain"}, {"name": "stderr", "source": "9fed748c-be47-415d-9ae4-a748752ffb82-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "browser_name", "value": "'chromium'"}], "start": 1751637731787, "stop": 1751637764191, "uuid": "ba5b29fa-d7db-401d-a130-939b95d136f9", "historyId": "cc91e9c679431603a47edb163ac91059", "testCaseId": "33ab5eee7f85e3ff87145ff630fc8c37", "fullName": "tests.ui.smoke.test_scf_login.TestSCFLogin#test_admin_user_login_success", "labels": [{"name": "story", "value": "管理员用户登录"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "用户登录"}, {"name": "epic", "value": "供应链金融备案系统"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "tests.ui.smoke"}, {"name": "suite", "value": "test_scf_login"}, {"name": "subSuite", "value": "TestSC<PERSON><PERSON>in"}, {"name": "host", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "thread", "value": "31596-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.ui.smoke.test_scf_login"}]}