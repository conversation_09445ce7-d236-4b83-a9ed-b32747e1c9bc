{"name": "test_required_fields_validation[chromium]", "status": "broken", "statusDetails": {"message": "UnboundLocalError: cannot access local variable 'random' where it is not associated with a value", "trace": "tests\\ui\\filing\\test_scf_filing_exceptions.py:43: in test_required_fields_validation\n    test_data = self.data_manager.generate_filing_test_data(\"invalid\")\n                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\ncomponents\\filing_data_manager.py:51: in generate_filing_test_data\n    \"user_info\": self._generate_user_info(scenario),\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\ncomponents\\filing_data_manager.py:87: in _generate_user_info\n    \"department\": random.choice([\"技术部\", \"财务部\", \"业务部\", \"风控部\"])\n                  ^^^^^^\nE   UnboundLocalError: cannot access local variable 'random' where it is not associated with a value"}, "description": "\n        测试必填字段验证\n        \n        测试步骤：\n        1. 登录并导航到备案申请页面\n        2. 跳过必填字段直接提交\n        3. 验证显示错误提示\n        4. 验证无法进入下一步\n        ", "attachments": [{"name": "失败截图", "source": "f7bf9c31-23c1-40ea-a58d-a0d3830b2ff4-attachment.png", "type": "image/png"}, {"name": "页面HTML", "source": "6bda69d9-24ed-4aab-92a3-3c55aee814fd-attachment.html", "type": "text/html"}, {"name": "当前URL", "source": "215a0668-d098-48dc-981b-42ccf6889ce4-attachment.txt", "type": "text/plain"}, {"name": "log", "source": "1c9147e2-a384-4aaa-b9f5-f467e6f13d0b-attachment.txt", "type": "text/plain"}, {"name": "stdout", "source": "0a2830eb-8688-4b33-9e9a-************-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "browser_name", "value": "'chromium'"}], "start": 1751858941256, "stop": 1751858941302, "uuid": "c228309a-95b3-4618-9e80-1b76191d0edb", "historyId": "8a70119861b542f592cd6ff6d6026f63", "testCaseId": "7f2e2dffdabb3b76a3959ca1593db18c", "fullName": "tests.ui.filing.test_scf_filing_exceptions.TestSCFFilingExceptions#test_required_fields_validation", "labels": [{"name": "story", "value": "必填字段验证"}, {"name": "epic", "value": "SCF备案系统"}, {"name": "severity", "value": "critical"}, {"name": "feature", "value": "备案申请异常处理"}, {"name": "tag", "value": "regression"}, {"name": "tag", "value": "filing"}, {"name": "tag", "value": "filing_exception"}, {"name": "parentSuite", "value": "tests.ui.filing"}, {"name": "suite", "value": "test_scf_filing_exceptions"}, {"name": "subSuite", "value": "TestSCFFilingExceptions"}, {"name": "host", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "thread", "value": "19296-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.ui.filing.test_scf_filing_exceptions"}]}