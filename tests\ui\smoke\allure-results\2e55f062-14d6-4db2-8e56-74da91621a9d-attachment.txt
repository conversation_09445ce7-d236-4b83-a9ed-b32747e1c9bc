DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.address`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.address` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.automotive`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.automotive` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.bank`.
DEBUG    faker.factory:factory.py:88 Specified locale `en_US` is not available for provider `faker.providers.bank`. Locale reset to `en_GB` for this provider.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.barcode`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.barcode` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.color`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.color` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.company`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.company` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.credit_card`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.credit_card` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.currency`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.currency` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.date_time`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.date_time` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:108 Provider `faker.providers.doi` does not feature localization. Specified locale `en_US` is not utilized for this provider.
DEBUG    faker.factory:factory.py:108 Provider `faker.providers.emoji` does not feature localization. Specified locale `en_US` is not utilized for this provider.
DEBUG    faker.factory:factory.py:108 Provider `faker.providers.file` does not feature localization. Specified locale `en_US` is not utilized for this provider.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.geo`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.geo` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.internet`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.internet` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.isbn`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.isbn` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.job`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.job` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.lorem`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.lorem` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.misc`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.misc` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.passport`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.passport` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.person`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.person` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.phone_number`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.phone_number` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:108 Provider `faker.providers.profile` does not feature localization. Specified locale `en_US` is not utilized for this provider.
DEBUG    faker.factory:factory.py:108 Provider `faker.providers.python` does not feature localization. Specified locale `en_US` is not utilized for this provider.
DEBUG    faker.factory:factory.py:108 Provider `faker.providers.sbn` does not feature localization. Specified locale `en_US` is not utilized for this provider.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.ssn`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.ssn` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:108 Provider `faker.providers.user_agent` does not feature localization. Specified locale `en_US` is not utilized for this provider.
INFO     fixtures.scf_fixtures:scf_fixtures.py:32 加载SCF系统配置成功
INFO     fixtures.scf_fixtures:scf_fixtures.py:52 加载SCF测试数据成功
DEBUG    asyncio:proactor_events.py:634 Using proactor: IocpProactor
INFO     fixtures.scf_fixtures:scf_fixtures.py:79 准备SCF登录凭证
INFO     components.ocr_util:ocr_util.py:60 OCR引擎初始化成功
WARNING  fixtures.scf_fixtures:scf_fixtures.py:170 测试文件不存在: bfmArtAssocId -> G:\nifa\playwright-python-template\data\test_files\异常信息共享系统V1.5需求.docx