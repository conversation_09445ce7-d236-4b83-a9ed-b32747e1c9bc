#!/usr/bin/env python3
"""
备案测试数据管理器
负责生成、管理和清理备案流程的测试数据
"""

import json
import uuid
import random
from pathlib import Path
from datetime import datetime, timedelta
from typing import Optional
from components.faker_util import get_faker_factory
from components.log_manager import get_logger


class FilingDataManager:
    """备案测试数据管理器"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.faker = get_faker_factory()
        
        # 数据存储路径
        self.data_dir = Path(__file__).parent.parent / "test_data"
        self.data_dir.mkdir(exist_ok=True)
        
        # 测试数据文件
        self.test_data_file = self.data_dir / "filing_test_data.json"
        self.session_data_file = self.data_dir / "session_data.json"
        
    def generate_filing_test_data(self, scenario: str = "normal") -> dict:
        """
        生成备案测试数据
        
        Args:
            scenario: 测试场景 ("normal", "boundary", "invalid", "edge_case")
            
        Returns:
            测试数据字典
        """
        self.logger.info(f"生成备案测试数据，场景: {scenario}")
        
        # 生成唯一的测试会话ID
        session_id = str(uuid.uuid4())[:8]
        
        # 基础数据
        base_data = {
            "session_id": session_id,
            "scenario": scenario,
            "created_at": datetime.now().isoformat(),
            "user_info": self._generate_user_info(scenario),
            "company_info": self._generate_company_info(scenario),
            "compliance_data": self._generate_compliance_data(scenario),
            "file_info": self._generate_file_info(scenario),
            "form_data": self._generate_form_data(scenario)
        }
        
        # 保存到会话数据
        self._save_session_data(session_id, base_data)
        
        self.logger.info(f"测试数据生成完成，会话ID: {session_id}")
        return base_data
    
    def _generate_user_info(self, scenario: str) -> dict:
        """生成用户信息"""
        if scenario == "boundary":
            # 边界值测试：最长用户名
            username = "a" * 50  # 假设最大长度50
            company_name = "测试公司" + "有限责任公司" * 10  # 长公司名
        elif scenario == "invalid":
            # 无效数据测试
            username = ""  # 空用户名
            company_name = ""  # 空公司名
        else:
            # 正常数据
            username = f"test_user_{random.randint(1000, 9999)}"
            company_name = self.faker.fake.company()

        return {
            "username": username,
            "password": "Scf123456.",  # 固定测试密码
            "company_name": company_name,
            "contact_person": self.faker.fake.name(),
            "contact_phone": self.faker.fake.phone_number(),
            "contact_email": self.faker.fake.email(),
            "department": random.choice(["技术部", "财务部", "业务部", "风控部"])
        }
    
    def _generate_company_info(self, scenario: str) -> dict:
        """生成公司信息"""

        if scenario == "boundary":
            # 边界值测试
            business_scope = "A" * 1000  # 最大业务范围描述
            registered_capital = "999999999999.99"  # 最大注册资本
        elif scenario == "invalid":
            # 无效数据测试
            business_scope = ""  # 空业务范围
            registered_capital = "-100"  # 负数注册资本
        else:
            # 正常数据
            business_scope = self.faker.fake.text(max_nb_chars=200)
            registered_capital = str(random.randint(1000000, *********))

        return {
            "company_type": random.choice(["有限责任公司", "股份有限公司", "个人独资企业"]),
            "business_scope": business_scope,
            "registered_capital": registered_capital,
            "registration_date": self.faker.fake.date_between(start_date="-10y", end_date="today").isoformat(),
            "legal_representative": self.faker.fake.name(),
            "business_license": f"91{random.randint(*********000000, 999999999999999)}",
            "tax_number": f"{random.randint(*********000000, 999999999999999)}",
            "organization_code": f"{random.randint(10000000, 99999999)}-{random.randint(1, 9)}"
        }
    
    def _generate_compliance_data(self, scenario: str) -> dict:
        """生成合规性检查数据"""
        compliance_fields = [
            "bcVele", "bcVbsaroa", "bcCoo", "bcPcabsmrc", 
            "bcFswscsv", "bcRosctedt", "bcSmflee", "bcCbscud",
            "scoSsdama", "scoSccmfd", "scoSml3", "scoSmdoa",
            "bmBctms", "bmCicrm", "bmNmcsf", "bmNfsadp",
            "bmCamm", "bmCiv", "bmNsuvc", "bmFvrap",
            "bmClba", "bmCfia", "bmAmlct", "bmSscdm",
            "bplDbqav", "bplRuivr", "bplFclb",
            "opUtbdi", "opCceiq", "opReiar",
            "tpCtr", "tpNdpt", "tpErt5l",
            "fpUtbdf", "fpMpft", "fpNffli",
            "ppPrmer", "ppSnidd",
            "fmTfp", "fmNfspd", "fmNufur", "fmSsfcf",
            "fmCfofp", "fmFrsme", "fmDfpt", "fmNuf",
            "irSirr", "irSiid"
        ]
        
        compliance_data = {}
        
        import random

        for field in compliance_fields:
            if scenario == "invalid":
                # 无效数据：不选择任何选项
                compliance_data[f"{field}_option"] = None
                compliance_data[f"{field}_description"] = ""
            elif scenario == "boundary":
                # 边界值：最长描述
                compliance_data[f"{field}_option"] = "是"
                compliance_data[f"{field}_description"] = "测试" * 250  # 1000字符
            else:
                # 正常数据
                compliance_data[f"{field}_option"] = random.choice(["是", "否"])
                compliance_data[f"{field}_description"] = f"测试说明 - {self.faker.fake.sentence()}"
        
        return compliance_data
    
    def _generate_file_info(self, scenario: str) -> dict:
        """生成文件信息"""
        if scenario == "invalid":
            # 无效文件测试
            return {
                "basic_doc": "nonexistent_file.txt",
                "assoc_doc": "invalid_format.xyz",
                "supplement_doc": "too_large_file.pdf"  # 假设超大文件
            }
        else:
            # 正常文件
            return {
                "basic_doc": "异常信息共享系统V1.5需求.docx",
                "assoc_doc": "格式二模版.xlsx",
                "supplement_doc": "供应链金融测试报告V1.6.0.pdf",
                "audit_report": "供应链金融测试报告V1.6.0.pdf",
                "framework_coop": "供应链金融测试报告V1.6.0.pdf"
            }
    
    def _generate_form_data(self, scenario: str) -> dict:
        """生成表单数据"""
        if scenario == "boundary":
            return {
                "institution_name": "A" * 100,  # 最长机构名
                "project_description": "项目描述" * 200,  # 最长项目描述
                "risk_assessment": "风险评估" * 100  # 最长风险评估
            }
        elif scenario == "invalid":
            return {
                "institution_name": "",  # 空机构名
                "project_description": "",  # 空项目描述
                "risk_assessment": ""  # 空风险评估
            }
        else:
            return {
                "institution_name": "天津中互金数据科技有限公司",
                "project_description": self.faker.fake.text(max_nb_chars=500),
                "risk_assessment": self.faker.fake.text(max_nb_chars=300)
            }
    
    def _save_session_data(self, session_id: str, data: dict):
        """保存会话数据"""
        try:
            # 读取现有数据
            if self.session_data_file.exists():
                with open(self.session_data_file, 'r', encoding='utf-8') as f:
                    session_data = json.load(f)
            else:
                session_data = {}
            
            # 添加新会话数据
            session_data[session_id] = data
            
            # 保存数据
            with open(self.session_data_file, 'w', encoding='utf-8') as f:
                json.dump(session_data, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"会话数据已保存: {session_id}")
            
        except Exception as e:
            self.logger.error(f"保存会话数据失败: {e}")
    
    def get_session_data(self, session_id: str) -> Optional[dict]:
        """获取会话数据"""
        try:
            if self.session_data_file.exists():
                with open(self.session_data_file, 'r', encoding='utf-8') as f:
                    session_data = json.load(f)
                return session_data.get(session_id)
        except Exception as e:
            self.logger.error(f"获取会话数据失败: {e}")
        return None
    
    def cleanup_old_sessions(self, days: int = 7):
        """清理旧的会话数据"""
        try:
            if not self.session_data_file.exists():
                return
            
            with open(self.session_data_file, 'r', encoding='utf-8') as f:
                session_data = json.load(f)
            
            cutoff_date = datetime.now() - timedelta(days=days)
            cleaned_data = {}
            
            for session_id, data in session_data.items():
                created_at = datetime.fromisoformat(data.get('created_at', ''))
                if created_at > cutoff_date:
                    cleaned_data[session_id] = data
            
            with open(self.session_data_file, 'w', encoding='utf-8') as f:
                json.dump(cleaned_data, f, ensure_ascii=False, indent=2)
            
            removed_count = len(session_data) - len(cleaned_data)
            self.logger.info(f"清理了 {removed_count} 个旧会话数据")
            
        except Exception as e:
            self.logger.error(f"清理会话数据失败: {e}")
    
    def generate_test_scenarios(self) -> dict:
        """生成所有测试场景的数据"""
        scenarios = {
            "normal": self.generate_filing_test_data("normal"),
            "boundary": self.generate_filing_test_data("boundary"),
            "invalid": self.generate_filing_test_data("invalid"),
            "edge_case": self.generate_filing_test_data("edge_case")
        }
        
        self.logger.info(f"生成了 {len(scenarios)} 个测试场景的数据")
        return scenarios
