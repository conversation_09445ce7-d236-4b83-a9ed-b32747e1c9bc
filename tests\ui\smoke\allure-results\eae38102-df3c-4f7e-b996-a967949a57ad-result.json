{"name": "test_form_filling_functionality[chromium]", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: Locator.wait_for: Unexpected token \"=\" while parsing css selector \"#bcVele text=否\". Did you mean to CSS.escape it?\nCall log:\n  - waiting for #bcVele text=否 to be visible", "trace": "test_scf_registration.py:144: in test_form_filling_functionality\n    workflow.registration_page.fill_business_compliance_form(test_form_data)\n..\\..\\..\\pages\\scf_registration_page.py:249: in fill_business_compliance_form\n    self.select_yes_no_option(field, option, description)\n..\\..\\..\\pages\\scf_registration_page.py:126: in select_yes_no_option\n    self.click(option_selector)\n..\\..\\..\\components\\retry_util.py:135: in wrapper\n    raise last_exception\n..\\..\\..\\components\\retry_util.py:88: in wrapper\n    result = func(*args, **kwargs)\n             ^^^^^^^^^^^^^^^^^^^^^\n..\\..\\..\\pages\\base_page.py:107: in click\n    element = self.wait_for_element_stable(locator)\n              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n..\\..\\..\\pages\\base_page.py:50: in wait_for_element_stable\n    element.wait_for(state=\"visible\", timeout=self.default_timeout)\n..\\..\\..\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py:17937: in wait_for\n    self._sync(self._impl_obj.wait_for(timeout=timeout, state=state))\n..\\..\\..\\.venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py:693: in wait_for\n    await self._frame.wait_for_selector(\n..\\..\\..\\.venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py:341: in wait_for_selector\n    await self._channel.send(\n..\\..\\..\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:69: in send\n    return await self._connection.wrap_api_call(\n..\\..\\..\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:558: in wrap_api_call\n    raise rewrite_error(error, f\"{parsed_st['apiName']}: {error}\") from None\nE   playwright._impl._errors.Error: Locator.wait_for: Unexpected token \"=\" while parsing css selector \"#bcVele text=否\". Did you mean to CSS.escape it?\nE   Call log:\nE     - waiting for #bcVele text=否 to be visible"}, "description": "测试表单填写功能", "steps": [{"name": "登录并导航到申请页面", "status": "passed", "steps": [{"name": "登录系统", "status": "passed", "steps": [{"name": "导航到登录页面", "status": "passed", "steps": [{"name": "导航到页面: 'http://*************:8080/login'", "status": "passed", "parameters": [{"name": "url", "value": "'http://*************:8080/login'"}, {"name": "wait_until", "value": "'domcontentloaded'"}], "start": 1751854947887, "stop": 1751854948764}], "parameters": [{"name": "base_url", "value": "'http://*************:8080'"}], "start": 1751854947887, "stop": 1751854948764}, {"name": "执行登录操作", "status": "passed", "steps": [{"name": "执行登录操作（带重试机制）", "status": "passed", "steps": [{"name": "点击账号登录标签", "status": "passed", "steps": [{"name": "等待元素出现: 'text=账号登录'", "status": "passed", "parameters": [{"name": "locator", "value": "'text=账号登录'"}, {"name": "state", "value": "'visible'"}, {"name": "timeout", "value": "10000"}], "start": 1751854948765, "stop": 1751854948970}, {"name": "智能点击: 'text=账号登录'", "status": "passed", "steps": [{"name": "等待元素稳定: 'text=账号登录'", "status": "passed", "parameters": [{"name": "locator", "value": "'text=账号登录'"}, {"name": "timeout", "value": "None"}], "start": 1751854948971, "stop": 1751854949318}], "parameters": [{"name": "locator", "value": "'text=账号登录'"}, {"name": "force", "value": "False"}, {"name": "timeout", "value": "None"}], "start": 1751854948970, "stop": 1751854949379}], "start": 1751854948764, "stop": 1751854950395}, {"name": "输入用户名: 'scf_4nuioc'", "status": "passed", "steps": [{"name": "智能填充: 'input[placeholder='请输入账号']' = 'scf_4nuioc'", "status": "passed", "steps": [{"name": "等待元素稳定: 'input[placeholder='请输入账号']'", "status": "passed", "parameters": [{"name": "locator", "value": "'input[placeholder='请输入账号']'"}, {"name": "timeout", "value": "None"}], "start": 1751854950396, "stop": 1751854950747}], "parameters": [{"name": "locator", "value": "'input[placeholder='请输入账号']'"}, {"name": "text", "value": "'scf_4nuioc'"}, {"name": "clear_first", "value": "True"}, {"name": "timeout", "value": "None"}], "start": 1751854950396, "stop": 1751854950781}], "parameters": [{"name": "username", "value": "'scf_4nuioc'"}], "start": 1751854950395, "stop": 1751854950781}, {"name": "输入密码", "status": "passed", "steps": [{"name": "智能填充: 'input[type='password']' = 'Scf123456.'", "status": "passed", "steps": [{"name": "等待元素稳定: 'input[type='password']'", "status": "passed", "parameters": [{"name": "locator", "value": "'input[type='password']'"}, {"name": "timeout", "value": "None"}], "start": 1751854950782, "stop": 1751854951123}], "parameters": [{"name": "locator", "value": "'input[type='password']'"}, {"name": "text", "value": "'Scf123456.'"}, {"name": "clear_first", "value": "True"}, {"name": "timeout", "value": "None"}], "start": 1751854950782, "stop": 1751854951165}], "parameters": [{"name": "password", "value": "'Scf123456.'"}], "start": 1751854950781, "stop": 1751854951165}, {"name": "自动识别并输入验证码", "status": "passed", "steps": [{"name": "截取并保存验证码图片", "status": "passed", "steps": [{"name": "等待元素出现: '.arco-image img'", "status": "passed", "parameters": [{"name": "locator", "value": "'.arco-image img'"}, {"name": "state", "value": "'visible'"}, {"name": "timeout", "value": "10000"}], "start": 1751854951165, "stop": 1751854951171}], "attachments": [{"name": "验证码图片_尝试1", "source": "bee6b658-e52f-4a6c-a2cc-ce4e74a95d79-attachment.png", "type": "image/png"}], "parameters": [{"name": "attempt", "value": "1"}], "start": 1751854951165, "stop": 1751854952253}, {"name": "OCR识别验证码", "status": "passed", "parameters": [{"name": "captcha_image", "value": "<class 'bytes'>"}, {"name": "attempt", "value": "1"}], "start": 1751854952253, "stop": 1751854952276}, {"name": "输入图形验证码: '1647'", "status": "passed", "steps": [{"name": "智能填充: 'input[placeholder='请输入图形验证码']' = '1647'", "status": "passed", "steps": [{"name": "等待元素稳定: 'input[placeholder='请输入图形验证码']'", "status": "passed", "parameters": [{"name": "locator", "value": "'input[placeholder='请输入图形验证码']'"}, {"name": "timeout", "value": "None"}], "start": 1751854952276, "stop": 1751854952654}], "parameters": [{"name": "locator", "value": "'input[placeholder='请输入图形验证码']'"}, {"name": "text", "value": "'1647'"}, {"name": "clear_first", "value": "True"}, {"name": "timeout", "value": "None"}], "start": 1751854952276, "stop": 1751854952701}], "parameters": [{"name": "<PERSON><PERSON>a", "value": "'1647'"}], "start": 1751854952276, "stop": 1751854952701}], "parameters": [{"name": "max_retries", "value": "1"}, {"name": "min_confidence", "value": "0.6"}], "start": 1751854951165, "stop": 1751854952701}, {"name": "点击登录按钮", "status": "passed", "steps": [{"name": "智能点击: 'button[type='submit']'", "status": "passed", "steps": [{"name": "等待元素稳定: 'button[type='submit']'", "status": "passed", "parameters": [{"name": "locator", "value": "'button[type='submit']'"}, {"name": "timeout", "value": "None"}], "start": 1751854952701, "stop": 1751854953051}], "parameters": [{"name": "locator", "value": "'button[type='submit']'"}, {"name": "force", "value": "False"}, {"name": "timeout", "value": "None"}], "start": 1751854952701, "stop": 1751854953147}], "start": 1751854952701, "stop": 1751854953147}, {"name": "检查登录状态", "status": "passed", "parameters": [{"name": "timeout", "value": "5000"}], "start": 1751854953148, "stop": 1751854954153}], "parameters": [{"name": "username", "value": "'scf_4nuioc'"}, {"name": "password", "value": "'Scf123456.'"}, {"name": "max_captcha_retries", "value": "5"}], "start": 1751854948764, "stop": 1751854954153}], "parameters": [{"name": "username", "value": "'scf_4nuioc'"}, {"name": "password", "value": "'Scf123456.'"}, {"name": "<PERSON><PERSON>a", "value": "None"}, {"name": "auto_captcha", "value": "True"}], "start": 1751854948764, "stop": 1751854954153}, {"name": "验证登录成功", "status": "passed", "steps": [{"name": "等待页面加载完成", "status": "passed", "parameters": [{"name": "state", "value": "'networkidle'"}, {"name": "timeout", "value": "None"}], "start": 1751854954154, "stop": 1751854954156}], "parameters": [{"name": "expected_url_pattern", "value": "None"}], "start": 1751854954153, "stop": 1751854954156}], "parameters": [{"name": "credentials", "value": "{'username': 'scf_4nuioc', 'password': 'Scf123456.', 'description': '标准测试用户'}"}, {"name": "base_url", "value": "'http://*************:8080'"}], "start": 1751854947887, "stop": 1751854954156}, {"name": "进入备案申请", "status": "passed", "steps": [{"name": "点击备案登记菜单", "status": "passed", "steps": [{"name": "智能点击: 'text=备案登记'", "status": "passed", "steps": [{"name": "等待元素稳定: 'text=备案登记'", "status": "passed", "parameters": [{"name": "locator", "value": "'text=备案登记'"}, {"name": "timeout", "value": "None"}], "start": 1751854954157, "stop": 1751854954506}], "parameters": [{"name": "locator", "value": "'text=备案登记'"}, {"name": "force", "value": "False"}, {"name": "timeout", "value": "None"}], "start": 1751854954157, "stop": 1751854954574}], "start": 1751854954157, "stop": 1751854954574}, {"name": "点击备案申请按钮", "status": "passed", "steps": [{"name": "智能点击: 'role=button[name='备案申请']'", "status": "passed", "steps": [{"name": "等待元素稳定: 'role=button[name='备案申请']'", "status": "passed", "parameters": [{"name": "locator", "value": "'role=button[name='备案申请']'"}, {"name": "timeout", "value": "None"}], "start": 1751854954575, "stop": 1751854955137}], "parameters": [{"name": "locator", "value": "'role=button[name='备案申请']'"}, {"name": "force", "value": "False"}, {"name": "timeout", "value": "None"}], "start": 1751854954574, "stop": 1751854955208}], "start": 1751854954574, "stop": 1751854955208}], "start": 1751854954156, "stop": 1751854955208}], "start": 1751854947886, "stop": 1751854955208}, {"name": "跳过基础材料步骤", "status": "passed", "steps": [{"name": "点击下一步", "status": "passed", "steps": [{"name": "智能点击: 'role=button[name='下一步']'", "status": "passed", "steps": [{"name": "等待元素稳定: 'role=button[name='下一步']'", "status": "passed", "parameters": [{"name": "locator", "value": "'role=button[name='下一步']'"}, {"name": "timeout", "value": "None"}], "start": 1751854955208, "stop": 1751854955807}], "parameters": [{"name": "locator", "value": "'role=button[name='下一步']'"}, {"name": "force", "value": "False"}, {"name": "timeout", "value": "None"}], "start": 1751854955208, "stop": 1751854955896}, {"name": "等待页面加载完成", "status": "passed", "parameters": [{"name": "state", "value": "'networkidle'"}, {"name": "timeout", "value": "None"}], "start": 1751854955896, "stop": 1751854955898}], "start": 1751854955208, "stop": 1751854955898}], "start": 1751854955208, "stop": 1751854955898}, {"name": "测试业务合规表单填写", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: Locator.wait_for: Unexpected token \"=\" while parsing css selector \"#bcVele text=否\". Did you mean to CSS.escape it?\nCall log:\n  - waiting for #bcVele text=否 to be visible\n\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\tests\\ui\\smoke\\test_scf_registration.py\", line 144, in test_form_filling_functionality\n    workflow.registration_page.fill_business_compliance_form(test_form_data)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_registration_page.py\", line 249, in fill_business_compliance_form\n    self.select_yes_no_option(field, option, description)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_registration_page.py\", line 126, in select_yes_no_option\n    self.click(option_selector)\n  File \"G:\\nifa\\playwright-python-template\\components\\retry_util.py\", line 135, in wrapper\n    raise last_exception\n  File \"G:\\nifa\\playwright-python-template\\components\\retry_util.py\", line 88, in wrapper\n    result = func(*args, **kwargs)\n             ^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 107, in click\n    element = self.wait_for_element_stable(locator)\n              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 50, in wait_for_element_stable\n    element.wait_for(state=\"visible\", timeout=self.default_timeout)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 17937, in wait_for\n    self._sync(self._impl_obj.wait_for(timeout=timeout, state=state))\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 115, in _sync\n    return task.result()\n           ^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py\", line 693, in wait_for\n    await self._frame.wait_for_selector(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py\", line 341, in wait_for_selector\n    await self._channel.send(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 69, in send\n    return await self._connection.wrap_api_call(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 558, in wrap_api_call\n    raise rewrite_error(error, f\"{parsed_st['apiName']}: {error}\") from None\n"}, "steps": [{"name": "填写业务合规表单", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: Locator.wait_for: Unexpected token \"=\" while parsing css selector \"#bcVele text=否\". Did you mean to CSS.escape it?\nCall log:\n  - waiting for #bcVele text=否 to be visible\n\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_registration_page.py\", line 249, in fill_business_compliance_form\n    self.select_yes_no_option(field, option, description)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_registration_page.py\", line 126, in select_yes_no_option\n    self.click(option_selector)\n  File \"G:\\nifa\\playwright-python-template\\components\\retry_util.py\", line 135, in wrapper\n    raise last_exception\n  File \"G:\\nifa\\playwright-python-template\\components\\retry_util.py\", line 88, in wrapper\n    result = func(*args, **kwargs)\n             ^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 107, in click\n    element = self.wait_for_element_stable(locator)\n              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 50, in wait_for_element_stable\n    element.wait_for(state=\"visible\", timeout=self.default_timeout)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 17937, in wait_for\n    self._sync(self._impl_obj.wait_for(timeout=timeout, state=state))\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 115, in _sync\n    return task.result()\n           ^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py\", line 693, in wait_for\n    await self._frame.wait_for_selector(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py\", line 341, in wait_for_selector\n    await self._channel.send(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 69, in send\n    return await self._connection.wrap_api_call(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 558, in wrap_api_call\n    raise rewrite_error(error, f\"{parsed_st['apiName']}: {error}\") from None\n"}, "steps": [{"name": "选择是/否选项: '否' 在区域 'bcVele'", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: Locator.wait_for: Unexpected token \"=\" while parsing css selector \"#bcVele text=否\". Did you mean to CSS.escape it?\nCall log:\n  - waiting for #bcVele text=否 to be visible\n\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_registration_page.py\", line 126, in select_yes_no_option\n    self.click(option_selector)\n  File \"G:\\nifa\\playwright-python-template\\components\\retry_util.py\", line 135, in wrapper\n    raise last_exception\n  File \"G:\\nifa\\playwright-python-template\\components\\retry_util.py\", line 88, in wrapper\n    result = func(*args, **kwargs)\n             ^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 107, in click\n    element = self.wait_for_element_stable(locator)\n              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 50, in wait_for_element_stable\n    element.wait_for(state=\"visible\", timeout=self.default_timeout)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 17937, in wait_for\n    self._sync(self._impl_obj.wait_for(timeout=timeout, state=state))\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 115, in _sync\n    return task.result()\n           ^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py\", line 693, in wait_for\n    await self._frame.wait_for_selector(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py\", line 341, in wait_for_selector\n    await self._channel.send(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 69, in send\n    return await self._connection.wrap_api_call(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 558, in wrap_api_call\n    raise rewrite_error(error, f\"{parsed_st['apiName']}: {error}\") from None\n"}, "steps": [{"name": "智能点击: '#bcVele text=否'", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: Locator.wait_for: Unexpected token \"=\" while parsing css selector \"#bcVele text=否\". Did you mean to CSS.escape it?\nCall log:\n  - waiting for #bcVele text=否 to be visible\n\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 107, in click\n    element = self.wait_for_element_stable(locator)\n              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 50, in wait_for_element_stable\n    element.wait_for(state=\"visible\", timeout=self.default_timeout)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 17937, in wait_for\n    self._sync(self._impl_obj.wait_for(timeout=timeout, state=state))\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 115, in _sync\n    return task.result()\n           ^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py\", line 693, in wait_for\n    await self._frame.wait_for_selector(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py\", line 341, in wait_for_selector\n    await self._channel.send(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 69, in send\n    return await self._connection.wrap_api_call(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 558, in wrap_api_call\n    raise rewrite_error(error, f\"{parsed_st['apiName']}: {error}\") from None\n"}, "steps": [{"name": "等待元素稳定: '#bcVele text=否'", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: Locator.wait_for: Unexpected token \"=\" while parsing css selector \"#bcVele text=否\". Did you mean to CSS.escape it?\nCall log:\n  - waiting for #bcVele text=否 to be visible\n\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 50, in wait_for_element_stable\n    element.wait_for(state=\"visible\", timeout=self.default_timeout)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 17937, in wait_for\n    self._sync(self._impl_obj.wait_for(timeout=timeout, state=state))\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 115, in _sync\n    return task.result()\n           ^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py\", line 693, in wait_for\n    await self._frame.wait_for_selector(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py\", line 341, in wait_for_selector\n    await self._channel.send(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 69, in send\n    return await self._connection.wrap_api_call(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 558, in wrap_api_call\n    raise rewrite_error(error, f\"{parsed_st['apiName']}: {error}\") from None\n"}, "parameters": [{"name": "locator", "value": "'#bcVele text=否'"}, {"name": "timeout", "value": "None"}], "start": 1751854955899, "stop": 1751854955901}], "parameters": [{"name": "locator", "value": "'#bcVele text=否'"}, {"name": "force", "value": "False"}, {"name": "timeout", "value": "None"}], "start": 1751854955899, "stop": 1751854955902}, {"name": "智能点击: '#bcVele text=否'", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: Locator.wait_for: Unexpected token \"=\" while parsing css selector \"#bcVele text=否\". Did you mean to CSS.escape it?\nCall log:\n  - waiting for #bcVele text=否 to be visible\n\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 107, in click\n    element = self.wait_for_element_stable(locator)\n              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 50, in wait_for_element_stable\n    element.wait_for(state=\"visible\", timeout=self.default_timeout)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 17937, in wait_for\n    self._sync(self._impl_obj.wait_for(timeout=timeout, state=state))\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 115, in _sync\n    return task.result()\n           ^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py\", line 693, in wait_for\n    await self._frame.wait_for_selector(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py\", line 341, in wait_for_selector\n    await self._channel.send(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 69, in send\n    return await self._connection.wrap_api_call(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 558, in wrap_api_call\n    raise rewrite_error(error, f\"{parsed_st['apiName']}: {error}\") from None\n"}, "steps": [{"name": "等待元素稳定: '#bcVele text=否'", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: Locator.wait_for: Unexpected token \"=\" while parsing css selector \"#bcVele text=否\". Did you mean to CSS.escape it?\nCall log:\n  - waiting for #bcVele text=否 to be visible\n\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 50, in wait_for_element_stable\n    element.wait_for(state=\"visible\", timeout=self.default_timeout)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 17937, in wait_for\n    self._sync(self._impl_obj.wait_for(timeout=timeout, state=state))\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 115, in _sync\n    return task.result()\n           ^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py\", line 693, in wait_for\n    await self._frame.wait_for_selector(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py\", line 341, in wait_for_selector\n    await self._channel.send(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 69, in send\n    return await self._connection.wrap_api_call(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 558, in wrap_api_call\n    raise rewrite_error(error, f\"{parsed_st['apiName']}: {error}\") from None\n"}, "parameters": [{"name": "locator", "value": "'#bcVele text=否'"}, {"name": "timeout", "value": "None"}], "start": 1751854956373, "stop": 1751854956375}], "parameters": [{"name": "locator", "value": "'#bcVele text=否'"}, {"name": "force", "value": "False"}, {"name": "timeout", "value": "None"}], "start": 1751854956373, "stop": 1751854956375}, {"name": "智能点击: '#bcVele text=否'", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: Locator.wait_for: Unexpected token \"=\" while parsing css selector \"#bcVele text=否\". Did you mean to CSS.escape it?\nCall log:\n  - waiting for #bcVele text=否 to be visible\n\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 107, in click\n    element = self.wait_for_element_stable(locator)\n              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 50, in wait_for_element_stable\n    element.wait_for(state=\"visible\", timeout=self.default_timeout)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 17937, in wait_for\n    self._sync(self._impl_obj.wait_for(timeout=timeout, state=state))\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 115, in _sync\n    return task.result()\n           ^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py\", line 693, in wait_for\n    await self._frame.wait_for_selector(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py\", line 341, in wait_for_selector\n    await self._channel.send(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 69, in send\n    return await self._connection.wrap_api_call(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 558, in wrap_api_call\n    raise rewrite_error(error, f\"{parsed_st['apiName']}: {error}\") from None\n"}, "steps": [{"name": "等待元素稳定: '#bcVele text=否'", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: Locator.wait_for: Unexpected token \"=\" while parsing css selector \"#bcVele text=否\". Did you mean to CSS.escape it?\nCall log:\n  - waiting for #bcVele text=否 to be visible\n\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 50, in wait_for_element_stable\n    element.wait_for(state=\"visible\", timeout=self.default_timeout)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 17937, in wait_for\n    self._sync(self._impl_obj.wait_for(timeout=timeout, state=state))\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 115, in _sync\n    return task.result()\n           ^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py\", line 693, in wait_for\n    await self._frame.wait_for_selector(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py\", line 341, in wait_for_selector\n    await self._channel.send(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 69, in send\n    return await self._connection.wrap_api_call(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 558, in wrap_api_call\n    raise rewrite_error(error, f\"{parsed_st['apiName']}: {error}\") from None\n"}, "parameters": [{"name": "locator", "value": "'#bcVele text=否'"}, {"name": "timeout", "value": "None"}], "start": 1751854957407, "stop": 1751854957415}], "parameters": [{"name": "locator", "value": "'#bcVele text=否'"}, {"name": "force", "value": "False"}, {"name": "timeout", "value": "None"}], "start": 1751854957407, "stop": 1751854957415}], "parameters": [{"name": "area_id", "value": "'bc<PERSON><PERSON>'"}, {"name": "option", "value": "'否'"}, {"name": "description", "value": "'测试'"}], "start": 1751854955899, "stop": 1751854957416}], "parameters": [{"name": "form_data", "value": "{'bcVele': {'option': '否', 'description': '测试'}}"}], "start": 1751854955899, "stop": 1751854957416}], "start": 1751854955898, "stop": 1751854957417}], "attachments": [{"name": "失败截图", "source": "1053bd84-9836-4efc-9eee-cdb47545e830-attachment.png", "type": "image/png"}, {"name": "页面HTML", "source": "561a9fda-a8ec-4a1b-995b-1c2c896e72a0-attachment.html", "type": "text/html"}, {"name": "当前URL", "source": "3c1ce2d9-86f1-494c-9866-a45fdfcf18ac-attachment.txt", "type": "text/plain"}, {"name": "log", "source": "5a9b78e9-4fae-4efc-928d-2ed21f1e9eda-attachment.txt", "type": "text/plain"}, {"name": "stdout", "source": "b641bb39-1c4d-4ef8-bf62-1e143c533bef-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "browser_name", "value": "'chromium'"}], "start": 1751854947847, "stop": 1751854957418, "uuid": "3f5b1a70-3866-49e8-b244-589facadc924", "historyId": "1bf38affd12aba37d8ea3162ac28682f", "testCaseId": "43961b128e8469100f2244d080d663cd", "fullName": "tests.ui.smoke.test_scf_registration.TestSCFRegistration#test_form_filling_functionality", "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "表单填写功能"}, {"name": "feature", "value": "备案申请"}, {"name": "epic", "value": "供应链金融备案系统"}, {"name": "tag", "value": "component"}, {"name": "parentSuite", "value": "tests.ui.smoke"}, {"name": "suite", "value": "test_scf_registration"}, {"name": "subSuite", "value": "TestSCFRegistration"}, {"name": "host", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "thread", "value": "24016-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.ui.smoke.test_scf_registration"}]}