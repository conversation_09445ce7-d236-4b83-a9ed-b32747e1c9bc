{"name": "test_admin_user_login_success[chromium]", "status": "failed", "statusDetails": {"message": "AssertionError: 登录失败，仍在登录页面: http://*************:8080/login", "trace": "test_scf_login.py:62: in test_admin_user_login_success\n    workflow._login_to_system(credentials, scf_base_url)\n..\\..\\..\\workflows\\scf_registration_workflow.py:126: in _login_to_system\n    self.login_page.verify_login_success()\n..\\..\\..\\pages\\scf_login_page.py:366: in verify_login_success\n    assert self.login_url not in current_url, f\"登录失败，仍在登录页面: {current_url}\"\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nE   AssertionError: 登录失败，仍在登录页面: http://*************:8080/login"}, "description": "测试管理员用户成功登录", "steps": [{"name": "执行管理员登录", "status": "failed", "statusDetails": {"message": "AssertionError: 登录失败，仍在登录页面: http://*************:8080/login\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\tests\\ui\\smoke\\test_scf_login.py\", line 62, in test_admin_user_login_success\n    workflow._login_to_system(credentials, scf_base_url)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\workflows\\scf_registration_workflow.py\", line 126, in _login_to_system\n    self.login_page.verify_login_success()\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 366, in verify_login_success\n    assert self.login_url not in current_url, f\"登录失败，仍在登录页面: {current_url}\"\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "steps": [{"name": "登录系统", "status": "failed", "statusDetails": {"message": "AssertionError: 登录失败，仍在登录页面: http://*************:8080/login\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\workflows\\scf_registration_workflow.py\", line 126, in _login_to_system\n    self.login_page.verify_login_success()\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 366, in verify_login_success\n    assert self.login_url not in current_url, f\"登录失败，仍在登录页面: {current_url}\"\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "steps": [{"name": "导航到登录页面", "status": "passed", "steps": [{"name": "导航到页面: 'http://*************:8080/login'", "status": "passed", "parameters": [{"name": "url", "value": "'http://*************:8080/login'"}, {"name": "wait_until", "value": "'domcontentloaded'"}], "start": 1751853950890, "stop": 1751853951807}], "parameters": [{"name": "base_url", "value": "'http://*************:8080'"}], "start": 1751853950890, "stop": 1751853951807}, {"name": "执行登录操作", "status": "passed", "steps": [{"name": "点击账号登录标签", "status": "passed", "steps": [{"name": "等待元素出现: 'text=账号登录'", "status": "passed", "parameters": [{"name": "locator", "value": "'text=账号登录'"}, {"name": "state", "value": "'visible'"}, {"name": "timeout", "value": "10000"}], "start": 1751853951808, "stop": 1751853952028}, {"name": "智能点击: 'text=账号登录'", "status": "passed", "steps": [{"name": "等待元素稳定: 'text=账号登录'", "status": "passed", "parameters": [{"name": "locator", "value": "'text=账号登录'"}, {"name": "timeout", "value": "None"}], "start": 1751853952029, "stop": 1751853952383}], "parameters": [{"name": "locator", "value": "'text=账号登录'"}, {"name": "force", "value": "False"}, {"name": "timeout", "value": "None"}], "start": 1751853952029, "stop": 1751853952453}], "start": 1751853951808, "stop": 1751853953458}, {"name": "输入用户名: 'admin'", "status": "passed", "steps": [{"name": "智能填充: 'input[placeholder='请输入账号']' = 'admin'", "status": "passed", "steps": [{"name": "等待元素稳定: 'input[placeholder='请输入账号']'", "status": "passed", "parameters": [{"name": "locator", "value": "'input[placeholder='请输入账号']'"}, {"name": "timeout", "value": "None"}], "start": 1751853953459, "stop": 1751853953813}], "parameters": [{"name": "locator", "value": "'input[placeholder='请输入账号']'"}, {"name": "text", "value": "'admin'"}, {"name": "clear_first", "value": "True"}, {"name": "timeout", "value": "None"}], "start": 1751853953459, "stop": 1751853953853}], "parameters": [{"name": "username", "value": "'admin'"}], "start": 1751853953458, "stop": 1751853953853}, {"name": "输入密码", "status": "passed", "steps": [{"name": "智能填充: 'input[type='password']' = 'Admin123456.'", "status": "passed", "steps": [{"name": "等待元素稳定: 'input[type='password']'", "status": "passed", "parameters": [{"name": "locator", "value": "'input[type='password']'"}, {"name": "timeout", "value": "None"}], "start": 1751853953854, "stop": 1751853954206}], "parameters": [{"name": "locator", "value": "'input[type='password']'"}, {"name": "text", "value": "'Admin123456.'"}, {"name": "clear_first", "value": "True"}, {"name": "timeout", "value": "None"}], "start": 1751853953854, "stop": 1751853954234}], "parameters": [{"name": "password", "value": "'Admin123456.'"}], "start": 1751853953853, "stop": 1751853954234}, {"name": "自动识别并输入验证码", "status": "passed", "steps": [{"name": "等待元素出现: '.arco-image img'", "status": "passed", "parameters": [{"name": "locator", "value": "'.arco-image img'"}, {"name": "state", "value": "'visible'"}, {"name": "timeout", "value": "10000"}], "start": 1751853954234, "stop": 1751853954240}, {"name": "输入图形验证码: '2549'", "status": "passed", "steps": [{"name": "智能填充: 'input[placeholder='请输入图形验证码']' = '2549'", "status": "passed", "steps": [{"name": "等待元素稳定: 'input[placeholder='请输入图形验证码']'", "status": "passed", "parameters": [{"name": "locator", "value": "'input[placeholder='请输入图形验证码']'"}, {"name": "timeout", "value": "None"}], "start": 1751853955362, "stop": 1751853955745}], "parameters": [{"name": "locator", "value": "'input[placeholder='请输入图形验证码']'"}, {"name": "text", "value": "'2549'"}, {"name": "clear_first", "value": "True"}, {"name": "timeout", "value": "None"}], "start": 1751853955362, "stop": 1751853955792}], "parameters": [{"name": "<PERSON><PERSON>a", "value": "'2549'"}], "start": 1751853955362, "stop": 1751853955792}], "parameters": [{"name": "max_retries", "value": "3"}], "start": 1751853954234, "stop": 1751853955792}, {"name": "点击登录按钮", "status": "passed", "steps": [{"name": "智能点击: 'button[type='submit']'", "status": "passed", "steps": [{"name": "等待元素稳定: 'button[type='submit']'", "status": "passed", "parameters": [{"name": "locator", "value": "'button[type='submit']'"}, {"name": "timeout", "value": "None"}], "start": 1751853955793, "stop": 1751853956131}], "parameters": [{"name": "locator", "value": "'button[type='submit']'"}, {"name": "force", "value": "False"}, {"name": "timeout", "value": "None"}], "start": 1751853955793, "stop": 1751853956237}], "start": 1751853955792, "stop": 1751853956237}], "parameters": [{"name": "username", "value": "'admin'"}, {"name": "password", "value": "'Admin123456.'"}, {"name": "<PERSON><PERSON>a", "value": "None"}, {"name": "auto_captcha", "value": "True"}], "start": 1751853951807, "stop": 1751853956237}, {"name": "验证登录成功", "status": "failed", "statusDetails": {"message": "AssertionError: 登录失败，仍在登录页面: http://*************:8080/login\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 366, in verify_login_success\n    assert self.login_url not in current_url, f\"登录失败，仍在登录页面: {current_url}\"\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "steps": [{"name": "等待页面加载完成", "status": "passed", "parameters": [{"name": "state", "value": "'networkidle'"}, {"name": "timeout", "value": "None"}], "start": 1751853956237, "stop": 1751853956239}], "parameters": [{"name": "expected_url_pattern", "value": "None"}], "start": 1751853956237, "stop": 1751853956239}], "parameters": [{"name": "credentials", "value": "{'username': 'admin', 'password': 'Admin123456.', 'description': '管理员用户'}"}, {"name": "base_url", "value": "'http://*************:8080'"}], "start": 1751853950889, "stop": 1751853956239}], "start": 1751853950889, "stop": 1751853956239}], "attachments": [{"name": "失败截图", "source": "873a081c-4063-406b-967c-d009a7a71108-attachment.png", "type": "image/png"}, {"name": "页面HTML", "source": "95a8ec45-9d95-4020-8080-5f41196c2140-attachment.html", "type": "text/html"}, {"name": "当前URL", "source": "b0251383-2b7a-48f0-9d53-661930738f71-attachment.txt", "type": "text/plain"}, {"name": "log", "source": "bceed52c-0b2f-418c-acaf-2bdb3c3857df-attachment.txt", "type": "text/plain"}, {"name": "stdout", "source": "641afedd-7a7a-4ca7-af58-39e67771f40d-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "browser_name", "value": "'chromium'"}], "start": 1751853950841, "stop": 1751853956239, "uuid": "157c2389-d1ff-43d7-9c1d-28d2aad2010e", "historyId": "cc91e9c679431603a47edb163ac91059", "testCaseId": "33ab5eee7f85e3ff87145ff630fc8c37", "fullName": "tests.ui.smoke.test_scf_login.TestSCFLogin#test_admin_user_login_success", "labels": [{"name": "story", "value": "管理员用户登录"}, {"name": "feature", "value": "用户登录"}, {"name": "epic", "value": "供应链金融备案系统"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "tests.ui.smoke"}, {"name": "suite", "value": "test_scf_login"}, {"name": "subSuite", "value": "TestSC<PERSON><PERSON>in"}, {"name": "host", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "thread", "value": "14940-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.ui.smoke.test_scf_login"}]}