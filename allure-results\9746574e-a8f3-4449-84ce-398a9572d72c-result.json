{"name": "test_login_failure_scenarios[chromium-invalid_user-\\u5e94\\u8be5\\u663e\\u793a\\u9519\\u8bef\\u4fe1\\u606f]", "status": "passed", "description": "测试登录失败场景", "steps": [{"name": "使用invalid_user凭证登录", "status": "passed", "steps": [{"name": "登录系统", "status": "broken", "statusDetails": {"message": "AttributeError: 'SCFLoginPage' object has no attribute 'take_screenshot_for_report'\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\workflows\\scf_registration_workflow.py\", line 123, in _login_to_system\n    self.login_page.login(username, password, auto_captcha=True)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 170, in login\n    self.auto_input_captcha()\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 133, in auto_input_captcha\n    self.take_screenshot_for_report(\"验证码识别失败\")\n    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "steps": [{"name": "导航到登录页面", "status": "passed", "steps": [{"name": "导航到页面: 'http://172.18.12.128:8080/login'", "status": "passed", "parameters": [{"name": "url", "value": "'http://172.18.12.128:8080/login'"}, {"name": "wait_until", "value": "'domcontentloaded'"}], "start": 1751637765088, "stop": 1751637765810}], "parameters": [{"name": "base_url", "value": "'http://172.18.12.128:8080'"}], "start": 1751637765088, "stop": 1751637765811}, {"name": "执行登录操作", "status": "broken", "statusDetails": {"message": "AttributeError: 'SCFLoginPage' object has no attribute 'take_screenshot_for_report'\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 170, in login\n    self.auto_input_captcha()\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 133, in auto_input_captcha\n    self.take_screenshot_for_report(\"验证码识别失败\")\n    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "steps": [{"name": "点击账号登录标签", "status": "passed", "steps": [{"name": "智能点击: 'text=账号登录'", "status": "passed", "steps": [{"name": "等待元素稳定: 'text=账号登录'", "status": "passed", "parameters": [{"name": "locator", "value": "'text=账号登录'"}, {"name": "timeout", "value": "None"}], "start": 1751637765814, "stop": 1751637766433}], "parameters": [{"name": "locator", "value": "'text=账号登录'"}, {"name": "force", "value": "False"}, {"name": "timeout", "value": "None"}], "start": 1751637765813, "stop": 1751637766521}], "start": 1751637765812, "stop": 1751637766521}, {"name": "输入用户名: 'invalid_user'", "status": "passed", "steps": [{"name": "智能填充: 'role=textbox[name='请输入账号']' = 'invalid_user'", "status": "passed", "steps": [{"name": "等待元素稳定: 'role=textbox[name='请输入账号']'", "status": "passed", "parameters": [{"name": "locator", "value": "'role=textbox[name='请输入账号']'"}, {"name": "timeout", "value": "None"}], "start": 1751637766523, "stop": 1751637766946}], "parameters": [{"name": "locator", "value": "'role=textbox[name='请输入账号']'"}, {"name": "text", "value": "'invalid_user'"}, {"name": "clear_first", "value": "True"}, {"name": "timeout", "value": "None"}], "start": 1751637766522, "stop": 1751637767023}], "parameters": [{"name": "username", "value": "'invalid_user'"}], "start": 1751637766522, "stop": 1751637767023}, {"name": "输入密码", "status": "passed", "steps": [{"name": "智能填充: 'role=textbox[name='请输入密码']' = 'invalid_password'", "status": "passed", "steps": [{"name": "等待元素稳定: 'role=textbox[name='请输入密码']'", "status": "passed", "parameters": [{"name": "locator", "value": "'role=textbox[name='请输入密码']'"}, {"name": "timeout", "value": "None"}], "start": 1751637767025, "stop": 1751637767414}], "parameters": [{"name": "locator", "value": "'role=textbox[name='请输入密码']'"}, {"name": "text", "value": "'invalid_password'"}, {"name": "clear_first", "value": "True"}, {"name": "timeout", "value": "None"}], "start": 1751637767025, "stop": 1751637767496}], "parameters": [{"name": "password", "value": "'invalid_password'"}], "start": 1751637767023, "stop": 1751637767496}, {"name": "自动识别并输入验证码", "status": "broken", "statusDetails": {"message": "AttributeError: 'SCFLoginPage' object has no attribute 'take_screenshot_for_report'\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 133, in auto_input_captcha\n    self.take_screenshot_for_report(\"验证码识别失败\")\n    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "steps": [{"name": "等待元素出现: '.captcha-image'", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.TimeoutError: Locator.wait_for: Timeout 30000ms exceeded.\nCall log:\n  - waiting for locator(\".captcha-image\") to be visible\n\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 192, in wait_for_element\n    element.wait_for(state=state, timeout=timeout or self.default_timeout)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 17937, in wait_for\n    self._sync(self._impl_obj.wait_for(timeout=timeout, state=state))\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 115, in _sync\n    return task.result()\n           ^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py\", line 693, in wait_for\n    await self._frame.wait_for_selector(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py\", line 341, in wait_for_selector\n    await self._channel.send(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 69, in send\n    return await self._connection.wrap_api_call(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 558, in wrap_api_call\n    raise rewrite_error(error, f\"{parsed_st['apiName']}: {error}\") from None\n"}, "parameters": [{"name": "locator", "value": "'.captcha-image'"}, {"name": "state", "value": "'visible'"}, {"name": "timeout", "value": "None"}], "start": 1751637767496, "stop": 1751637797522}], "start": 1751637767496, "stop": 1751637797523}], "parameters": [{"name": "username", "value": "'invalid_user'"}, {"name": "password", "value": "'invalid_password'"}, {"name": "<PERSON><PERSON>a", "value": "None"}, {"name": "auto_captcha", "value": "True"}], "start": 1751637765811, "stop": 1751637797529}], "parameters": [{"name": "credentials", "value": "{'username': 'invalid_user', 'password': 'invalid_password'}"}, {"name": "base_url", "value": "'http://172.18.12.128:8080'"}], "start": 1751637765088, "stop": 1751637797533}], "attachments": [{"name": "登录失败信息", "source": "c526973f-890d-49c3-877d-d0f9cfd7d0ba-attachment.txt", "type": "text/plain"}], "start": 1751637765088, "stop": 1751637797539}, {"name": "验证登录失败行为", "status": "passed", "start": 1751637797539, "stop": 1751637797539}], "attachments": [{"name": "log", "source": "f0e29b64-d5f0-4bee-8fc0-a9af92268083-attachment.txt", "type": "text/plain"}, {"name": "stderr", "source": "04b0f405-83e0-4e5a-9b4f-9f6b2696f478-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "browser_name", "value": "'chromium'"}, {"name": "credentials_key", "value": "'invalid_user'"}, {"name": "expected_behavior", "value": "'应该显示错误信息'"}], "start": 1751637764918, "stop": 1751637797540, "uuid": "73f75ee5-74b4-4b25-91c0-5ba83d43a321", "historyId": "14f7173cd53e4fc1432a01c8e67b0401", "testCaseId": "a99cc8e5e6e05ba82a9dc1ae7049de1b", "fullName": "tests.ui.smoke.test_scf_login.TestSCFLogin#test_login_failure_scenarios", "labels": [{"name": "epic", "value": "供应链金融备案系统"}, {"name": "feature", "value": "用户登录"}, {"name": "story", "value": "登录失败场景"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "tests.ui.smoke"}, {"name": "suite", "value": "test_scf_login"}, {"name": "subSuite", "value": "TestSC<PERSON><PERSON>in"}, {"name": "host", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "thread", "value": "31596-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.ui.smoke.test_scf_login"}]}