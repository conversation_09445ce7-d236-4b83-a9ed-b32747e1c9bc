name: Quality Checks

on:
  push:
    branches: [ main, develop, feature/* ]
  pull_request:
    branches: [ main, develop ]

env:
  PYTHON_VERSION: '3.12'

jobs:
  lint-and-format:
    name: 代码检查和格式化
    runs-on: ubuntu-latest
    steps:
      - name: Checkout代码
        uses: actions/checkout@v4

      - name: 安装uv
        uses: astral-sh/setup-uv@v3
        with:
          version: "latest"

      - name: 设置Python
        run: uv python install ${{ env.PYTHON_VERSION }}

      - name: 创建虚拟环境并安装依赖
        run: |
          uv venv
          uv pip install -e ".[dev]"

      - name: 运行Ruff检查
        run: |
          echo "🔍 运行Ruff代码检查..."
          uv run ruff check . --output-format=github

      - name: 运行Ruff格式检查
        run: |
          echo "📝 检查代码格式..."
          uv run ruff format --check .

      - name: 运行Black格式检查
        run: |
          echo "🎨 运行Black格式检查..."
          uv run black --check --diff .

  type-check:
    name: 类型检查
    runs-on: ubuntu-latest
    steps:
      - name: Checkout代码
        uses: actions/checkout@v4

      - name: 安装uv
        uses: astral-sh/setup-uv@v3

      - name: 设置Python
        run: uv python install ${{ env.PYTHON_VERSION }}

      - name: 安装依赖
        run: |
          uv venv
          uv pip install -e ".[dev]"

      - name: 运行MyPy类型检查
        run: |
          echo "🔍 运行MyPy类型检查..."
          uv run mypy . --ignore-missing-imports --show-error-codes

  pre-commit-check:
    name: Pre-commit检查
    runs-on: ubuntu-latest
    steps:
      - name: Checkout代码
        uses: actions/checkout@v4

      - name: 安装uv
        uses: astral-sh/setup-uv@v3

      - name: 设置Python
        run: uv python install ${{ env.PYTHON_VERSION }}

      - name: 安装依赖
        run: |
          uv venv
          uv pip install -e ".[dev]"

      - name: 运行pre-commit检查
        run: |
          echo "🔧 运行pre-commit检查..."
          uv run pre-commit run --all-files

  dependency-check:
    name: 依赖安全检查
    runs-on: ubuntu-latest
    steps:
      - name: Checkout代码
        uses: actions/checkout@v4

      - name: 安装uv
        uses: astral-sh/setup-uv@v3

      - name: 设置Python
        run: uv python install ${{ env.PYTHON_VERSION }}

      - name: 检查依赖漏洞
        run: |
          echo "🔒 检查依赖安全漏洞..."
          uv pip install safety
          uv run safety check

  code-complexity:
    name: 代码复杂度检查
    runs-on: ubuntu-latest
    steps:
      - name: Checkout代码
        uses: actions/checkout@v4

      - name: 安装uv
        uses: astral-sh/setup-uv@v3

      - name: 设置Python
        run: uv python install ${{ env.PYTHON_VERSION }}

      - name: 安装依赖
        run: |
          uv venv
          uv pip install -e ".[dev]"
          uv pip install radon xenon

      - name: 检查代码复杂度
        run: |
          echo "📊 检查代码复杂度..."
          uv run radon cc . -a -nb
          uv run radon mi . -nb
          uv run xenon . --max-absolute B --max-modules A --max-average A

  documentation-check:
    name: 文档检查
    runs-on: ubuntu-latest
    steps:
      - name: Checkout代码
        uses: actions/checkout@v4

      - name: 检查README文件
        run: |
          echo "📚 检查文档完整性..."
          if [ ! -f README.md ]; then
            echo "❌ README.md文件缺失"
            exit 1
          fi
          
          # 检查README内容
          if ! grep -q "快速启动" README.md; then
            echo "⚠️ README.md缺少快速启动说明"
          fi
          
          if ! grep -q "安装" README.md; then
            echo "⚠️ README.md缺少安装说明"
          fi

      - name: 检查配置文件
        run: |
          echo "⚙️ 检查配置文件..."
          required_files=(
            "pyproject.toml"
            ".pre-commit-config.yaml"
            "Dockerfile"
            "docker-compose.yml"
          )
          
          for file in "${required_files[@]}"; do
            if [ ! -f "$file" ]; then
              echo "❌ 必需文件缺失: $file"
              exit 1
            else
              echo "✅ $file 存在"
            fi
          done

  quality-gate:
    name: 质量门禁
    runs-on: ubuntu-latest
    needs: [lint-and-format, type-check, pre-commit-check, dependency-check, code-complexity, documentation-check]
    if: always()
    steps:
      - name: 检查质量门禁状态
        run: |
          echo "🚪 检查质量门禁状态..."
          
          # 检查所有前置任务的状态
          lint_status="${{ needs.lint-and-format.result }}"
          type_status="${{ needs.type-check.result }}"
          precommit_status="${{ needs.pre-commit-check.result }}"
          dependency_status="${{ needs.dependency-check.result }}"
          complexity_status="${{ needs.code-complexity.result }}"
          doc_status="${{ needs.documentation-check.result }}"
          
          echo "📊 质量检查结果:"
          echo "- 代码检查和格式化: $lint_status"
          echo "- 类型检查: $type_status"
          echo "- Pre-commit检查: $precommit_status"
          echo "- 依赖安全检查: $dependency_status"
          echo "- 代码复杂度检查: $complexity_status"
          echo "- 文档检查: $doc_status"
          
          # 如果任何检查失败，则质量门禁失败
          if [[ "$lint_status" != "success" || "$type_status" != "success" || "$precommit_status" != "success" || "$dependency_status" != "success" || "$complexity_status" != "success" || "$doc_status" != "success" ]]; then
            echo "❌ 质量门禁失败！请修复上述问题后重新提交。"
            exit 1
          else
            echo "✅ 质量门禁通过！代码质量符合要求。"
          fi

      - name: 添加PR评论
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v7
        with:
          script: |
            const { needs } = context.payload.workflow_run || { needs: ${{ toJson(needs) }} };
            
            let status = '✅ 质量检查通过';
            let details = '';
            
            const checks = [
              { name: '代码检查和格式化', status: '${{ needs.lint-and-format.result }}' },
              { name: '类型检查', status: '${{ needs.type-check.result }}' },
              { name: 'Pre-commit检查', status: '${{ needs.pre-commit-check.result }}' },
              { name: '依赖安全检查', status: '${{ needs.dependency-check.result }}' },
              { name: '代码复杂度检查', status: '${{ needs.code-complexity.result }}' },
              { name: '文档检查', status: '${{ needs.documentation-check.result }}' }
            ];
            
            const failed = checks.filter(check => check.status !== 'success');
            
            if (failed.length > 0) {
              status = '❌ 质量检查失败';
              details = '\n\n**失败的检查项:**\n' + 
                failed.map(check => `- ${check.name}: ${check.status}`).join('\n');
            }
            
            const body = `## 🔍 代码质量检查结果\n\n${status}${details}\n\n**所有检查项:**\n` +
              checks.map(check => `- ${check.status === 'success' ? '✅' : '❌'} ${check.name}`).join('\n');
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: body
            });
