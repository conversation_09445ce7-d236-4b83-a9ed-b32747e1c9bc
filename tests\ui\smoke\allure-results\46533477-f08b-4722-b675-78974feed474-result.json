{"name": "test_institution_selection[chromium]", "status": "passed", "description": "测试专业机构选择功能", "steps": [{"name": "登录系统", "status": "passed", "steps": [{"name": "登录系统", "status": "passed", "steps": [{"name": "导航到登录页面", "status": "passed", "steps": [{"name": "导航到页面: 'http://*************:8080/login'", "status": "passed", "parameters": [{"name": "url", "value": "'http://*************:8080/login'"}, {"name": "wait_until", "value": "'domcontentloaded'"}], "start": 1751854958067, "stop": 1751854959034}], "parameters": [{"name": "base_url", "value": "'http://*************:8080'"}], "start": 1751854958067, "stop": 1751854959034}, {"name": "执行登录操作", "status": "passed", "steps": [{"name": "执行登录操作（带重试机制）", "status": "passed", "steps": [{"name": "点击账号登录标签", "status": "passed", "steps": [{"name": "等待元素出现: 'text=账号登录'", "status": "passed", "parameters": [{"name": "locator", "value": "'text=账号登录'"}, {"name": "state", "value": "'visible'"}, {"name": "timeout", "value": "10000"}], "start": 1751854959035, "stop": 1751854959269}, {"name": "智能点击: 'text=账号登录'", "status": "passed", "steps": [{"name": "等待元素稳定: 'text=账号登录'", "status": "passed", "parameters": [{"name": "locator", "value": "'text=账号登录'"}, {"name": "timeout", "value": "None"}], "start": 1751854959269, "stop": 1751854959616}], "parameters": [{"name": "locator", "value": "'text=账号登录'"}, {"name": "force", "value": "False"}, {"name": "timeout", "value": "None"}], "start": 1751854959269, "stop": 1751854959699}], "start": 1751854959035, "stop": 1751854960705}, {"name": "输入用户名: 'scf_4nuioc'", "status": "passed", "steps": [{"name": "智能填充: 'input[placeholder='请输入账号']' = 'scf_4nuioc'", "status": "passed", "steps": [{"name": "等待元素稳定: 'input[placeholder='请输入账号']'", "status": "passed", "parameters": [{"name": "locator", "value": "'input[placeholder='请输入账号']'"}, {"name": "timeout", "value": "None"}], "start": 1751854960706, "stop": 1751854961056}], "parameters": [{"name": "locator", "value": "'input[placeholder='请输入账号']'"}, {"name": "text", "value": "'scf_4nuioc'"}, {"name": "clear_first", "value": "True"}, {"name": "timeout", "value": "None"}], "start": 1751854960706, "stop": 1751854961098}], "parameters": [{"name": "username", "value": "'scf_4nuioc'"}], "start": 1751854960706, "stop": 1751854961098}, {"name": "输入密码", "status": "passed", "steps": [{"name": "智能填充: 'input[type='password']' = 'Scf123456.'", "status": "passed", "steps": [{"name": "等待元素稳定: 'input[type='password']'", "status": "passed", "parameters": [{"name": "locator", "value": "'input[type='password']'"}, {"name": "timeout", "value": "None"}], "start": 1751854961098, "stop": 1751854961446}], "parameters": [{"name": "locator", "value": "'input[type='password']'"}, {"name": "text", "value": "'Scf123456.'"}, {"name": "clear_first", "value": "True"}, {"name": "timeout", "value": "None"}], "start": 1751854961098, "stop": 1751854961484}], "parameters": [{"name": "password", "value": "'Scf123456.'"}], "start": 1751854961098, "stop": 1751854961484}, {"name": "自动识别并输入验证码", "status": "passed", "steps": [{"name": "截取并保存验证码图片", "status": "passed", "steps": [{"name": "等待元素出现: '.arco-image img'", "status": "passed", "parameters": [{"name": "locator", "value": "'.arco-image img'"}, {"name": "state", "value": "'visible'"}, {"name": "timeout", "value": "10000"}], "start": 1751854961485, "stop": 1751854961505}], "attachments": [{"name": "验证码图片_尝试1", "source": "ec8e96a0-6526-47fd-86ef-2e0994b2dc9b-attachment.png", "type": "image/png"}], "parameters": [{"name": "attempt", "value": "1"}], "start": 1751854961485, "stop": 1751854962602}, {"name": "OCR识别验证码", "status": "passed", "parameters": [{"name": "captcha_image", "value": "<class 'bytes'>"}, {"name": "attempt", "value": "1"}], "start": 1751854962602, "stop": 1751854962648}, {"name": "输入图形验证码: '8683'", "status": "passed", "steps": [{"name": "智能填充: 'input[placeholder='请输入图形验证码']' = '8683'", "status": "passed", "steps": [{"name": "等待元素稳定: 'input[placeholder='请输入图形验证码']'", "status": "passed", "parameters": [{"name": "locator", "value": "'input[placeholder='请输入图形验证码']'"}, {"name": "timeout", "value": "None"}], "start": 1751854962649, "stop": 1751854963032}], "parameters": [{"name": "locator", "value": "'input[placeholder='请输入图形验证码']'"}, {"name": "text", "value": "'8683'"}, {"name": "clear_first", "value": "True"}, {"name": "timeout", "value": "None"}], "start": 1751854962649, "stop": 1751854963087}], "parameters": [{"name": "<PERSON><PERSON>a", "value": "'8683'"}], "start": 1751854962648, "stop": 1751854963087}], "parameters": [{"name": "max_retries", "value": "1"}, {"name": "min_confidence", "value": "0.6"}], "start": 1751854961485, "stop": 1751854963087}, {"name": "点击登录按钮", "status": "passed", "steps": [{"name": "智能点击: 'button[type='submit']'", "status": "passed", "steps": [{"name": "等待元素稳定: 'button[type='submit']'", "status": "passed", "parameters": [{"name": "locator", "value": "'button[type='submit']'"}, {"name": "timeout", "value": "None"}], "start": 1751854963088, "stop": 1751854963435}], "parameters": [{"name": "locator", "value": "'button[type='submit']'"}, {"name": "force", "value": "False"}, {"name": "timeout", "value": "None"}], "start": 1751854963088, "stop": 1751854963559}], "start": 1751854963087, "stop": 1751854963559}, {"name": "检查登录状态", "status": "passed", "parameters": [{"name": "timeout", "value": "5000"}], "start": 1751854963559, "stop": 1751854964565}], "parameters": [{"name": "username", "value": "'scf_4nuioc'"}, {"name": "password", "value": "'Scf123456.'"}, {"name": "max_captcha_retries", "value": "5"}], "start": 1751854959034, "stop": 1751854964565}], "parameters": [{"name": "username", "value": "'scf_4nuioc'"}, {"name": "password", "value": "'Scf123456.'"}, {"name": "<PERSON><PERSON>a", "value": "None"}, {"name": "auto_captcha", "value": "True"}], "start": 1751854959034, "stop": 1751854964565}, {"name": "验证登录成功", "status": "passed", "steps": [{"name": "等待页面加载完成", "status": "passed", "parameters": [{"name": "state", "value": "'networkidle'"}, {"name": "timeout", "value": "None"}], "start": 1751854964566, "stop": 1751854964567}], "parameters": [{"name": "expected_url_pattern", "value": "None"}], "start": 1751854964565, "stop": 1751854964567}], "parameters": [{"name": "credentials", "value": "{'username': 'scf_4nuioc', 'password': 'Scf123456.', 'description': '标准测试用户'}"}, {"name": "base_url", "value": "'http://*************:8080'"}], "start": 1751854958066, "stop": 1751854964567}], "start": 1751854958066, "stop": 1751854964567}, {"name": "快速导航到机构选择步骤", "status": "passed", "steps": [{"name": "进入备案申请", "status": "passed", "steps": [{"name": "点击备案登记菜单", "status": "passed", "steps": [{"name": "智能点击: 'text=备案登记'", "status": "passed", "steps": [{"name": "等待元素稳定: 'text=备案登记'", "status": "passed", "parameters": [{"name": "locator", "value": "'text=备案登记'"}, {"name": "timeout", "value": "None"}], "start": 1751854964568, "stop": 1751854964919}], "parameters": [{"name": "locator", "value": "'text=备案登记'"}, {"name": "force", "value": "False"}, {"name": "timeout", "value": "None"}], "start": 1751854964567, "stop": 1751854964992}], "start": 1751854964567, "stop": 1751854964993}, {"name": "点击备案申请按钮", "status": "passed", "steps": [{"name": "智能点击: 'role=button[name='备案申请']'", "status": "passed", "steps": [{"name": "等待元素稳定: 'role=button[name='备案申请']'", "status": "passed", "parameters": [{"name": "locator", "value": "'role=button[name='备案申请']'"}, {"name": "timeout", "value": "None"}], "start": 1751854964993, "stop": 1751854965544}], "parameters": [{"name": "locator", "value": "'role=button[name='备案申请']'"}, {"name": "force", "value": "False"}, {"name": "timeout", "value": "None"}], "start": 1751854964993, "stop": 1751854965608}], "start": 1751854964993, "stop": 1751854965608}], "start": 1751854964567, "stop": 1751854965608}, {"name": "点击下一步", "status": "passed", "steps": [{"name": "智能点击: 'role=button[name='下一步']'", "status": "passed", "steps": [{"name": "等待元素稳定: 'role=button[name='下一步']'", "status": "passed", "parameters": [{"name": "locator", "value": "'role=button[name='下一步']'"}, {"name": "timeout", "value": "None"}], "start": 1751854965609, "stop": 1751854966329}], "parameters": [{"name": "locator", "value": "'role=button[name='下一步']'"}, {"name": "force", "value": "False"}, {"name": "timeout", "value": "None"}], "start": 1751854965609, "stop": 1751854966411}, {"name": "等待页面加载完成", "status": "passed", "parameters": [{"name": "state", "value": "'networkidle'"}, {"name": "timeout", "value": "None"}], "start": 1751854966411, "stop": 1751854966412}], "start": 1751854965609, "stop": 1751854966412}, {"name": "点击下一步", "status": "passed", "steps": [{"name": "智能点击: 'role=button[name='下一步']'", "status": "passed", "steps": [{"name": "等待元素稳定: 'role=button[name='下一步']'", "status": "passed", "parameters": [{"name": "locator", "value": "'role=button[name='下一步']'"}, {"name": "timeout", "value": "None"}], "start": 1751854967421, "stop": 1751854967822}], "parameters": [{"name": "locator", "value": "'role=button[name='下一步']'"}, {"name": "force", "value": "False"}, {"name": "timeout", "value": "None"}], "start": 1751854967421, "stop": 1751854967934}, {"name": "等待页面加载完成", "status": "passed", "parameters": [{"name": "state", "value": "'networkidle'"}, {"name": "timeout", "value": "None"}], "start": 1751854967935, "stop": 1751854967938}], "start": 1751854967420, "stop": 1751854967938}, {"name": "点击下一步", "status": "passed", "steps": [{"name": "智能点击: 'role=button[name='下一步']'", "status": "passed", "steps": [{"name": "等待元素稳定: 'role=button[name='下一步']'", "status": "passed", "parameters": [{"name": "locator", "value": "'role=button[name='下一步']'"}, {"name": "timeout", "value": "None"}], "start": 1751854968944, "stop": 1751854969345}], "parameters": [{"name": "locator", "value": "'role=button[name='下一步']'"}, {"name": "force", "value": "False"}, {"name": "timeout", "value": "None"}], "start": 1751854968944, "stop": 1751854969460}, {"name": "等待页面加载完成", "status": "passed", "parameters": [{"name": "state", "value": "'networkidle'"}, {"name": "timeout", "value": "None"}], "start": 1751854969460, "stop": 1751854969464}], "start": 1751854968943, "stop": 1751854969464}, {"name": "点击下一步", "status": "passed", "steps": [{"name": "智能点击: 'role=button[name='下一步']'", "status": "passed", "steps": [{"name": "等待元素稳定: 'role=button[name='下一步']'", "status": "passed", "parameters": [{"name": "locator", "value": "'role=button[name='下一步']'"}, {"name": "timeout", "value": "None"}], "start": 1751854970480, "stop": 1751854970882}], "parameters": [{"name": "locator", "value": "'role=button[name='下一步']'"}, {"name": "force", "value": "False"}, {"name": "timeout", "value": "None"}], "start": 1751854970480, "stop": 1751854971011}, {"name": "等待页面加载完成", "status": "passed", "parameters": [{"name": "state", "value": "'networkidle'"}, {"name": "timeout", "value": "None"}], "start": 1751854971011, "stop": 1751854971014}], "start": 1751854970479, "stop": 1751854971014}, {"name": "点击下一步", "status": "passed", "steps": [{"name": "智能点击: 'role=button[name='下一步']'", "status": "passed", "steps": [{"name": "等待元素稳定: 'role=button[name='下一步']'", "status": "passed", "parameters": [{"name": "locator", "value": "'role=button[name='下一步']'"}, {"name": "timeout", "value": "None"}], "start": 1751854972031, "stop": 1751854972458}], "parameters": [{"name": "locator", "value": "'role=button[name='下一步']'"}, {"name": "force", "value": "False"}, {"name": "timeout", "value": "None"}], "start": 1751854972031, "stop": 1751854972599}, {"name": "等待页面加载完成", "status": "passed", "parameters": [{"name": "state", "value": "'networkidle'"}, {"name": "timeout", "value": "None"}], "start": 1751854972599, "stop": 1751854972602}], "start": 1751854972030, "stop": 1751854972602}, {"name": "点击下一步", "status": "passed", "steps": [{"name": "智能点击: 'role=button[name='下一步']'", "status": "passed", "steps": [{"name": "等待元素稳定: 'role=button[name='下一步']'", "status": "passed", "parameters": [{"name": "locator", "value": "'role=button[name='下一步']'"}, {"name": "timeout", "value": "None"}], "start": 1751854973605, "stop": 1751854974001}], "parameters": [{"name": "locator", "value": "'role=button[name='下一步']'"}, {"name": "force", "value": "False"}, {"name": "timeout", "value": "None"}], "start": 1751854973605, "stop": 1751854974094}, {"name": "等待页面加载完成", "status": "passed", "parameters": [{"name": "state", "value": "'networkidle'"}, {"name": "timeout", "value": "None"}], "start": 1751854974095, "stop": 1751854974100}], "start": 1751854973604, "stop": 1751854974100}, {"name": "点击下一步", "status": "passed", "steps": [{"name": "智能点击: 'role=button[name='下一步']'", "status": "passed", "steps": [{"name": "等待元素稳定: 'role=button[name='下一步']'", "status": "passed", "parameters": [{"name": "locator", "value": "'role=button[name='下一步']'"}, {"name": "timeout", "value": "None"}], "start": 1751854975119, "stop": 1751854975530}], "parameters": [{"name": "locator", "value": "'role=button[name='下一步']'"}, {"name": "force", "value": "False"}, {"name": "timeout", "value": "None"}], "start": 1751854975118, "stop": 1751854975669}, {"name": "等待页面加载完成", "status": "passed", "parameters": [{"name": "state", "value": "'networkidle'"}, {"name": "timeout", "value": "None"}], "start": 1751854975669, "stop": 1751854975673}], "start": 1751854975118, "stop": 1751854975674}, {"name": "点击下一步", "status": "passed", "steps": [{"name": "智能点击: 'role=button[name='下一步']'", "status": "passed", "steps": [{"name": "等待元素稳定: 'role=button[name='下一步']'", "status": "passed", "parameters": [{"name": "locator", "value": "'role=button[name='下一步']'"}, {"name": "timeout", "value": "None"}], "start": 1751854976681, "stop": 1751854977074}], "parameters": [{"name": "locator", "value": "'role=button[name='下一步']'"}, {"name": "force", "value": "False"}, {"name": "timeout", "value": "None"}], "start": 1751854976681, "stop": 1751854977178}, {"name": "等待页面加载完成", "status": "passed", "parameters": [{"name": "state", "value": "'networkidle'"}, {"name": "timeout", "value": "None"}], "start": 1751854977178, "stop": 1751854977182}], "start": 1751854976680, "stop": 1751854977182}, {"name": "点击下一步", "status": "passed", "steps": [{"name": "智能点击: 'role=button[name='下一步']'", "status": "passed", "steps": [{"name": "等待元素稳定: 'role=button[name='下一步']'", "status": "passed", "parameters": [{"name": "locator", "value": "'role=button[name='下一步']'"}, {"name": "timeout", "value": "None"}], "start": 1751854978235, "stop": 1751854979640}], "parameters": [{"name": "locator", "value": "'role=button[name='下一步']'"}, {"name": "force", "value": "False"}, {"name": "timeout", "value": "None"}], "start": 1751854978235, "stop": 1751854979995}, {"name": "等待页面加载完成", "status": "passed", "parameters": [{"name": "state", "value": "'networkidle'"}, {"name": "timeout", "value": "None"}], "start": 1751854979995, "stop": 1751854979997}], "start": 1751854978234, "stop": 1751854979998}, {"name": "点击下一步", "status": "passed", "steps": [{"name": "智能点击: 'role=button[name='下一步']'", "status": "passed", "steps": [{"name": "等待元素稳定: 'role=button[name='下一步']'", "status": "passed", "parameters": [{"name": "locator", "value": "'role=button[name='下一步']'"}, {"name": "timeout", "value": "None"}], "start": 1751854981006, "stop": 1751854981448}], "parameters": [{"name": "locator", "value": "'role=button[name='下一步']'"}, {"name": "force", "value": "False"}, {"name": "timeout", "value": "None"}], "start": 1751854981006, "stop": 1751854981561}, {"name": "等待页面加载完成", "status": "passed", "parameters": [{"name": "state", "value": "'networkidle'"}, {"name": "timeout", "value": "None"}], "start": 1751854981561, "stop": 1751854981563}], "start": 1751854981005, "stop": 1751854981565}], "start": 1751854964567, "stop": 1751854982581}, {"name": "选择专业机构: 天津中互金数据科技有限公司", "status": "broken", "statusDetails": {"message": "KeyboardInterrupt\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\tests\\ui\\smoke\\test_scf_registration.py\", line 184, in test_institution_selection\n    workflow.registration_page.select_institution(default_institution)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_registration_page.py\", line 185, in select_institution\n    self.click(self.institution_dropdown)\n  File \"G:\\nifa\\playwright-python-template\\components\\retry_util.py\", line 88, in wrapper\n    result = func(*args, **kwargs)\n             ^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 107, in click\n    element = self.wait_for_element_stable(locator)\n              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 50, in wait_for_element_stable\n    element.wait_for(state=\"visible\", timeout=self.default_timeout)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 17937, in wait_for\n    self._sync(self._impl_obj.wait_for(timeout=timeout, state=state))\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 113, in _sync\n    self._dispatcher_fiber.switch()\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_context_manager.py\", line 56, in greenlet_main\n    self._loop.run_until_complete(self._connection.run_as_sync())\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.11-windows-x86_64-none\\Lib\\asyncio\\base_events.py\", line 678, in run_until_complete\n    self.run_forever()\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.11-windows-x86_64-none\\Lib\\asyncio\\windows_events.py\", line 322, in run_forever\n    super().run_forever()\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.11-windows-x86_64-none\\Lib\\asyncio\\base_events.py\", line 645, in run_forever\n    self._run_once()\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.11-windows-x86_64-none\\Lib\\asyncio\\base_events.py\", line 1961, in _run_once\n    event_list = self._selector.select(timeout)\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.11-windows-x86_64-none\\Lib\\asyncio\\windows_events.py\", line 445, in select\n    self._poll(timeout)\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.11-windows-x86_64-none\\Lib\\asyncio\\windows_events.py\", line 774, in _poll\n    status = _overlapped.GetQueuedCompletionStatus(self._iocp, ms)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "steps": [{"name": "选择专业机构", "status": "broken", "statusDetails": {"message": "KeyboardInterrupt\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_registration_page.py\", line 185, in select_institution\n    self.click(self.institution_dropdown)\n  File \"G:\\nifa\\playwright-python-template\\components\\retry_util.py\", line 88, in wrapper\n    result = func(*args, **kwargs)\n             ^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 107, in click\n    element = self.wait_for_element_stable(locator)\n              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 50, in wait_for_element_stable\n    element.wait_for(state=\"visible\", timeout=self.default_timeout)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 17937, in wait_for\n    self._sync(self._impl_obj.wait_for(timeout=timeout, state=state))\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 113, in _sync\n    self._dispatcher_fiber.switch()\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_context_manager.py\", line 56, in greenlet_main\n    self._loop.run_until_complete(self._connection.run_as_sync())\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.11-windows-x86_64-none\\Lib\\asyncio\\base_events.py\", line 678, in run_until_complete\n    self.run_forever()\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.11-windows-x86_64-none\\Lib\\asyncio\\windows_events.py\", line 322, in run_forever\n    super().run_forever()\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.11-windows-x86_64-none\\Lib\\asyncio\\base_events.py\", line 645, in run_forever\n    self._run_once()\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.11-windows-x86_64-none\\Lib\\asyncio\\base_events.py\", line 1961, in _run_once\n    event_list = self._selector.select(timeout)\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.11-windows-x86_64-none\\Lib\\asyncio\\windows_events.py\", line 445, in select\n    self._poll(timeout)\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.11-windows-x86_64-none\\Lib\\asyncio\\windows_events.py\", line 774, in _poll\n    status = _overlapped.GetQueuedCompletionStatus(self._iocp, ms)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "steps": [{"name": "智能点击: 'role=textbox[name='请选择专业机构']'", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.TimeoutError: Locator.wait_for: Timeout 30000ms exceeded.\nCall log:\n  - waiting for locator(\"role=textbox[name='请选择专业机构']\") to be visible\n\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 107, in click\n    element = self.wait_for_element_stable(locator)\n              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 50, in wait_for_element_stable\n    element.wait_for(state=\"visible\", timeout=self.default_timeout)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 17937, in wait_for\n    self._sync(self._impl_obj.wait_for(timeout=timeout, state=state))\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 115, in _sync\n    return task.result()\n           ^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py\", line 693, in wait_for\n    await self._frame.wait_for_selector(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py\", line 341, in wait_for_selector\n    await self._channel.send(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 69, in send\n    return await self._connection.wrap_api_call(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 558, in wrap_api_call\n    raise rewrite_error(error, f\"{parsed_st['apiName']}: {error}\") from None\n"}, "steps": [{"name": "等待元素稳定: 'role=textbox[name='请选择专业机构']'", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.TimeoutError: Locator.wait_for: Timeout 30000ms exceeded.\nCall log:\n  - waiting for locator(\"role=textbox[name='请选择专业机构']\") to be visible\n\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 50, in wait_for_element_stable\n    element.wait_for(state=\"visible\", timeout=self.default_timeout)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 17937, in wait_for\n    self._sync(self._impl_obj.wait_for(timeout=timeout, state=state))\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 115, in _sync\n    return task.result()\n           ^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py\", line 693, in wait_for\n    await self._frame.wait_for_selector(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py\", line 341, in wait_for_selector\n    await self._channel.send(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 69, in send\n    return await self._connection.wrap_api_call(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 558, in wrap_api_call\n    raise rewrite_error(error, f\"{parsed_st['apiName']}: {error}\") from None\n"}, "parameters": [{"name": "locator", "value": "'role=textbox[name='请选择专业机构']'"}, {"name": "timeout", "value": "None"}], "start": 1751854982582, "stop": 1751855012588}], "parameters": [{"name": "locator", "value": "'role=textbox[name='请选择专业机构']'"}, {"name": "force", "value": "False"}, {"name": "timeout", "value": "None"}], "start": 1751854982582, "stop": 1751855012588}, {"name": "智能点击: 'role=textbox[name='请选择专业机构']'", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.TimeoutError: Locator.wait_for: Timeout 30000ms exceeded.\nCall log:\n  - waiting for locator(\"role=textbox[name='请选择专业机构']\") to be visible\n\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 107, in click\n    element = self.wait_for_element_stable(locator)\n              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 50, in wait_for_element_stable\n    element.wait_for(state=\"visible\", timeout=self.default_timeout)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 17937, in wait_for\n    self._sync(self._impl_obj.wait_for(timeout=timeout, state=state))\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 115, in _sync\n    return task.result()\n           ^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py\", line 693, in wait_for\n    await self._frame.wait_for_selector(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py\", line 341, in wait_for_selector\n    await self._channel.send(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 69, in send\n    return await self._connection.wrap_api_call(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 558, in wrap_api_call\n    raise rewrite_error(error, f\"{parsed_st['apiName']}: {error}\") from None\n"}, "steps": [{"name": "等待元素稳定: 'role=textbox[name='请选择专业机构']'", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.TimeoutError: Locator.wait_for: Timeout 30000ms exceeded.\nCall log:\n  - waiting for locator(\"role=textbox[name='请选择专业机构']\") to be visible\n\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 50, in wait_for_element_stable\n    element.wait_for(state=\"visible\", timeout=self.default_timeout)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 17937, in wait_for\n    self._sync(self._impl_obj.wait_for(timeout=timeout, state=state))\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 115, in _sync\n    return task.result()\n           ^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py\", line 693, in wait_for\n    await self._frame.wait_for_selector(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py\", line 341, in wait_for_selector\n    await self._channel.send(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 69, in send\n    return await self._connection.wrap_api_call(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 558, in wrap_api_call\n    raise rewrite_error(error, f\"{parsed_st['apiName']}: {error}\") from None\n"}, "parameters": [{"name": "locator", "value": "'role=textbox[name='请选择专业机构']'"}, {"name": "timeout", "value": "None"}], "start": 1751855013056, "stop": 1751855043064}], "parameters": [{"name": "locator", "value": "'role=textbox[name='请选择专业机构']'"}, {"name": "force", "value": "False"}, {"name": "timeout", "value": "None"}], "start": 1751855013056, "stop": 1751855043064}, {"name": "智能点击: 'role=textbox[name='请选择专业机构']'", "status": "broken", "statusDetails": {"message": "KeyboardInterrupt\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 107, in click\n    element = self.wait_for_element_stable(locator)\n              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 50, in wait_for_element_stable\n    element.wait_for(state=\"visible\", timeout=self.default_timeout)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 17937, in wait_for\n    self._sync(self._impl_obj.wait_for(timeout=timeout, state=state))\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 113, in _sync\n    self._dispatcher_fiber.switch()\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_context_manager.py\", line 56, in greenlet_main\n    self._loop.run_until_complete(self._connection.run_as_sync())\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.11-windows-x86_64-none\\Lib\\asyncio\\base_events.py\", line 678, in run_until_complete\n    self.run_forever()\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.11-windows-x86_64-none\\Lib\\asyncio\\windows_events.py\", line 322, in run_forever\n    super().run_forever()\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.11-windows-x86_64-none\\Lib\\asyncio\\base_events.py\", line 645, in run_forever\n    self._run_once()\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.11-windows-x86_64-none\\Lib\\asyncio\\base_events.py\", line 1961, in _run_once\n    event_list = self._selector.select(timeout)\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.11-windows-x86_64-none\\Lib\\asyncio\\windows_events.py\", line 445, in select\n    self._poll(timeout)\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.11-windows-x86_64-none\\Lib\\asyncio\\windows_events.py\", line 774, in _poll\n    status = _overlapped.GetQueuedCompletionStatus(self._iocp, ms)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "steps": [{"name": "等待元素稳定: 'role=textbox[name='请选择专业机构']'", "status": "broken", "statusDetails": {"message": "KeyboardInterrupt\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 50, in wait_for_element_stable\n    element.wait_for(state=\"visible\", timeout=self.default_timeout)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 17937, in wait_for\n    self._sync(self._impl_obj.wait_for(timeout=timeout, state=state))\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 113, in _sync\n    self._dispatcher_fiber.switch()\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_context_manager.py\", line 56, in greenlet_main\n    self._loop.run_until_complete(self._connection.run_as_sync())\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.11-windows-x86_64-none\\Lib\\asyncio\\base_events.py\", line 678, in run_until_complete\n    self.run_forever()\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.11-windows-x86_64-none\\Lib\\asyncio\\windows_events.py\", line 322, in run_forever\n    super().run_forever()\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.11-windows-x86_64-none\\Lib\\asyncio\\base_events.py\", line 645, in run_forever\n    self._run_once()\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.11-windows-x86_64-none\\Lib\\asyncio\\base_events.py\", line 1961, in _run_once\n    event_list = self._selector.select(timeout)\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.11-windows-x86_64-none\\Lib\\asyncio\\windows_events.py\", line 445, in select\n    self._poll(timeout)\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.11-windows-x86_64-none\\Lib\\asyncio\\windows_events.py\", line 774, in _poll\n    status = _overlapped.GetQueuedCompletionStatus(self._iocp, ms)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "parameters": [{"name": "locator", "value": "'role=textbox[name='请选择专业机构']'"}, {"name": "timeout", "value": "None"}], "start": 1751855044150, "stop": 1751855066627}], "parameters": [{"name": "locator", "value": "'role=textbox[name='请选择专业机构']'"}, {"name": "force", "value": "False"}, {"name": "timeout", "value": "None"}], "start": 1751855044150, "stop": 1751855066628}], "parameters": [{"name": "institution_name", "value": "'天津中互金数据科技有限公司'"}], "start": 1751854982581, "stop": 1751855066628}], "start": 1751854982581, "stop": 1751855066629}], "parameters": [{"name": "browser_name", "value": "'chromium'"}], "start": 1751854958012, "stop": 1751855066630, "uuid": "05822d25-ce24-4d65-86f6-73a7545b8b26", "testCaseId": "0d0e7372cbb448f338a63d75bd6b928a", "fullName": "tests.ui.smoke.test_scf_registration.TestSCFRegistration#test_institution_selection"}