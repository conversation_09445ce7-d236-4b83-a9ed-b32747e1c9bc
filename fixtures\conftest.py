"""
全局核心 Fixtures
提供环境配置、浏览器、日志等核心功能
"""

import pytest
import allure
from playwright.sync_api import Page, <PERSON><PERSON><PERSON>, BrowserContext
from pathlib import Path
import os
from components.log_manager import get_logger
from components.faker_util import get_faker_factory
from collections.abc import Generator

logger = get_logger(__name__)


@pytest.fixture(scope="session")
def test_logger():
    """测试日志器 fixture"""
    return get_logger("test")


@pytest.fixture(scope="session")
def faker_factory():
    """Faker 工厂 fixture"""
    return get_faker_factory()


@pytest.fixture(scope="function")
def test_data_cleanup():
    """测试数据清理 fixture"""
    cleanup_tasks = []

    def add_cleanup_task(task_func, *args, **kwargs):
        """添加清理任务"""
        cleanup_tasks.append((task_func, args, kwargs))

    yield add_cleanup_task

    # 执行清理任务
    for task_func, args, kwargs in cleanup_tasks:
        try:
            task_func(*args, **kwargs)
            logger.info(f"清理任务执行成功: {task_func.__name__}")
        except Exception as e:
            logger.error(f"清理任务执行失败: {task_func.__name__}, 错误: {e}")


@pytest.fixture(scope="function")
def page_with_logging(page: Page, test_logger) -> Generator[Page, None, None]:
    """
    带日志记录的页面 fixture
    自动记录页面事件和错误
    """
    # 设置页面事件监听
    def on_console(msg):
        test_logger.info(f"浏览器控制台: [{msg.type}] {msg.text}")

    def on_page_error(error):
        test_logger.error(f"页面错误: {error}")

    def on_request_failed(request):
        test_logger.warning(f"请求失败: {request.method} {request.url}")

    # 绑定事件监听器
    page.on("console", on_console)
    page.on("pageerror", on_page_error)
    page.on("requestfailed", on_request_failed)

    yield page

    # 清理事件监听器
    page.remove_listener("console", on_console)
    page.remove_listener("pageerror", on_page_error)
    page.remove_listener("requestfailed", on_request_failed)


@pytest.fixture(scope="function")
def browser_context_with_trace(browser: Browser) -> Generator[BrowserContext, None, None]:
    """
    带 Trace 记录的浏览器上下文 fixture
    """
    # 创建上下文并启用 trace
    context = browser.new_context(
        viewport={"width": 1920, "height": 1080},
        locale="zh-CN",
        timezone_id="Asia/Shanghai"
    )

    # 启动 trace 记录
    trace_dir = Path("logs/traces")
    trace_dir.mkdir(parents=True, exist_ok=True)

    context.tracing.start(screenshots=True, snapshots=True, sources=True)

    yield context

    # 停止 trace 记录并保存
    try:
        trace_path = trace_dir / f"trace_{pytest.current_test_name}.zip"
        context.tracing.stop(path=str(trace_path))

        # 将 trace 附加到 Allure 报告
        if trace_path.exists():
            allure.attach.file(
                str(trace_path),
                name="Playwright Trace",
                attachment_type=allure.attachment_type.ZIP
            )
    except Exception as e:
        logger.error(f"保存 trace 失败: {e}")
    finally:
        context.close()


@pytest.fixture(scope="function")
def page_with_trace(browser_context_with_trace: BrowserContext) -> Page:
    """带 Trace 的页面 fixture"""
    page = browser_context_with_trace.new_page()
    return page


@pytest.hookimpl(hookwrapper=True)
def pytest_runtest_makereport(item, call):
    """
    测试报告钩子
    在测试失败时附加截图、日志等调试信息
    """
    # 获取测试结果
    outcome = yield
    rep = outcome.get_result()

    # 如果测试失败，附加调试信息
    if call.when == "call" and rep.failed:
        # 尝试获取页面对象并截图
        if hasattr(item, "funcargs"):
            page = None
            for fixture_name in ["page", "page_with_logging", "page_with_trace"]:
                if fixture_name in item.funcargs:
                    page = item.funcargs[fixture_name]
                    break

            if page:
                try:
                    # 截图
                    screenshot = page.screenshot()
                    allure.attach(
                        screenshot,
                        name="失败截图",
                        attachment_type=allure.attachment_type.PNG
                    )

                    # 页面HTML
                    html_content = page.content()
                    allure.attach(
                        html_content,
                        name="页面HTML",
                        attachment_type=allure.attachment_type.HTML
                    )

                    # 当前URL
                    allure.attach(
                        page.url,
                        name="当前URL",
                        attachment_type=allure.attachment_type.TEXT
                    )

                    logger.error(f"测试失败，已保存调试信息: {item.name}")

                except Exception as e:
                    logger.error(f"保存失败调试信息时出错: {e}")


def pytest_configure(config):
    """
    Pytest 配置钩子
    设置 Allure 环境信息
    """
    # 创建 allure-results 目录
    allure_dir = Path("allure-results")
    allure_dir.mkdir(exist_ok=True)

    # 生成环境信息文件
    env_properties = allure_dir / "environment.properties"

    env_info = {
        "Python版本": f"{os.sys.version}",
        "操作系统": f"{os.name}",
        "测试环境": config.getoption("env", default="test"),
        "浏览器": "Chromium",  # 可以根据实际配置动态获取
        "分辨率": "1920x1080",
        "时区": "Asia/Shanghai"
    }

    with open(env_properties, "w", encoding="utf-8") as f:
        for key, value in env_info.items():
            f.write(f"{key}={value}\n")

    logger.info("Allure 环境信息已生成")


# 存储当前测试名称的全局变量
pytest.current_test_name = ""


def pytest_runtest_setup(item):
    """测试设置钩子，记录当前测试名称"""
    pytest.current_test_name = item.name.replace("[", "_").replace("]", "_").replace(" ", "_")