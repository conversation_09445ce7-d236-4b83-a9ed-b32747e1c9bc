# 日志管理最佳实践指南

## Windows 环境下的日志配置

### 问题背景

在 Windows 环境下，由于文件锁定机制更严格，日志轮转可能遇到以下问题：
- `PermissionError: [WinError 32] 另一个程序正在使用此文件`
- 多进程同时访问同一日志文件导致的冲突
- TimedRotatingFileHandler 在文件重命名时的权限问题

### 解决方案

#### 1. 使用 SafeRotatingFileHandler

项目已实现 `SafeRotatingFileHandler` 类，具有以下特性：
- 禁用延迟写入，确保文件句柄正确管理
- 增加重试机制处理文件锁定
- 优雅的错误处理，避免程序中断

#### 2. 线程安全的日志配置

- 使用全局锁 `_logger_lock` 确保配置的线程安全
- 缓存已配置的日志器，避免重复创建处理器
- 统一的根日志器配置，避免处理器冲突

#### 3. 文件轮转策略

```python
# 推荐配置
SafeRotatingFileHandler(
    log_dir / 'all.log',
    maxBytes=10*1024*1024,  # 10MB
    backupCount=5,
    encoding='utf-8'
)
```

### 使用指南

#### 获取日志器

```python
from components.log_manager import get_logger

# 正确方式
logger = get_logger(__name__)
logger.info("日志消息")
```

#### 避免的做法

```python
# 错误：直接使用 logging.getLogger
logger = logging.getLogger(__name__)

# 错误：重复配置处理器
logger.addHandler(handler)
```

### 性能优化

1. **日志级别控制**：
   - 生产环境使用 INFO 级别
   - 调试环境使用 DEBUG 级别

2. **文件大小管理**：
   - 主日志文件：10MB，保留5个备份
   - 错误日志文件：5MB，保留3个备份

3. **自动清理**：
   ```python
   from components.log_manager import cleanup_old_logs
   cleanup_old_logs(days=7)  # 清理7天前的日志
   ```

### 故障排除

#### 常见问题

1. **文件被占用**：
   - 检查是否有多个进程同时运行
   - 确保测试结束后正确关闭浏览器

2. **权限不足**：
   - 确保 logs 目录有写权限
   - 以管理员身份运行（如必要）

3. **磁盘空间不足**：
   - 定期清理旧日志文件
   - 调整轮转策略

#### 调试命令

```powershell
# 检查文件占用
Get-Process | Where-Object {$_.ProcessName -like "*python*"}

# 检查日志文件状态
Get-ChildItem logs -Force | Format-Table Name, Length, LastWriteTime

# 清理进程
taskkill /f /im python.exe
```

### 监控和维护

#### 日志监控

1. **文件大小监控**：定期检查日志文件大小
2. **错误率监控**：关注错误日志的增长趋势
3. **性能监控**：监控日志写入性能

#### 定期维护

1. **清理旧文件**：每周清理超过7天的日志
2. **检查配置**：定期验证日志配置是否正确
3. **性能测试**：定期测试日志系统在高负载下的表现

### 企业级规范

#### 日志格式标准

```
%(asctime)s - %(name)s - %(levelname)s - %(message)s
```

#### 日志级别使用

- **DEBUG**：详细的调试信息
- **INFO**：一般信息，如测试步骤
- **WARNING**：警告信息，如重试操作
- **ERROR**：错误信息，如测试失败
- **CRITICAL**：严重错误，如系统崩溃

#### 安全考虑

1. **敏感信息**：不在日志中记录密码、Token等敏感信息
2. **访问控制**：限制日志文件的访问权限
3. **数据保护**：定期备份重要日志文件

### 集成指南

#### CI/CD 集成

```yaml
# 示例 GitHub Actions 配置
- name: Setup Logging
  run: |
    mkdir -p logs
    chmod 755 logs

- name: Run Tests with Logging
  run: |
    uv run pytest --log-cli-level=INFO
```

#### Docker 集成

```dockerfile
# 创建日志目录
RUN mkdir -p /app/logs && chmod 755 /app/logs

# 设置日志卷
VOLUME ["/app/logs"]
```

### 总结

通过实施这些最佳实践，可以确保：
- 在 Windows 环境下稳定的日志记录
- 高性能的日志处理
- 企业级的日志管理规范
- 便于故障排除和监控

定期回顾和更新这些实践，确保日志系统始终满足项目需求。
