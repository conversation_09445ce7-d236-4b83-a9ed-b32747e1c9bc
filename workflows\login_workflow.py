import allure
from pages.login_page import LoginPage
from components.log_manager import get_logger

logger = get_logger(__name__)


class LoginWorkflow:
    """登录业务流程"""

    def __init__(self, page):
        self.page = page
        self.login_page = LoginPage(page)

    @allure.step("执行用户登录流程")
    def login(self, username: str, password: str):
        """
        执行登录流程

        Args:
            username: 用户名
            password: 密码
        """
        logger.info(f"开始登录流程，用户名: {username}")

        with allure.step(f"使用用户名 '{username}' 登录"):
            self.login_page.login(username, password)
            logger.info("登录操作完成")

    @allure.step("验证登录成功")
    def verify_login_success(self, expected_url_pattern: str = None, expected_element: str = None):
        """
        验证登录是否成功

        Args:
            expected_url_pattern: 期望的URL模式
            expected_element: 期望出现的元素定位符
        """
        logger.info("验证登录成功状态")

        if expected_url_pattern:
            self.login_page.expect_page_to_have_url(expected_url_pattern)
            logger.info(f"URL验证通过: {expected_url_pattern}")

        if expected_element:
            self.login_page.expect_element_to_be_visible(expected_element)
            logger.info(f"元素验证通过: {expected_element}")

    @allure.step("验证登录失败")
    def verify_login_failure(self, error_message_locator: str, expected_error: str = None):
        """
        验证登录失败

        Args:
            error_message_locator: 错误消息元素定位符
            expected_error: 期望的错误消息
        """
        logger.info("验证登录失败状态")

        self.login_page.expect_element_to_be_visible(error_message_locator)

        if expected_error:
            self.login_page.expect_element_to_have_text(error_message_locator, expected_error)
            logger.info(f"错误消息验证通过: {expected_error}")

    @allure.step("执行登出流程")
    def logout(self, logout_button_locator: str = "#logout"):
        """
        执行登出流程

        Args:
            logout_button_locator: 登出按钮定位符
        """
        logger.info("开始登出流程")

        self.login_page.click(logout_button_locator)
        logger.info("登出操作完成")
