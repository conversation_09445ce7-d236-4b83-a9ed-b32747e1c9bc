{"name": "test_captcha_functionality[chromium]", "status": "passed", "description": "测试验证码功能", "steps": [{"name": "导航到登录页面", "status": "passed", "steps": [{"name": "导航到登录页面", "status": "passed", "steps": [{"name": "导航到页面: 'http://*************:8080/login'", "status": "passed", "parameters": [{"name": "url", "value": "'http://*************:8080/login'"}, {"name": "wait_until", "value": "'domcontentloaded'"}], "start": 1751853964627, "stop": 1751853965552}], "parameters": [{"name": "base_url", "value": "'http://*************:8080'"}], "start": 1751853964627, "stop": 1751853965552}, {"name": "点击账号登录标签", "status": "passed", "steps": [{"name": "等待元素出现: 'text=账号登录'", "status": "passed", "parameters": [{"name": "locator", "value": "'text=账号登录'"}, {"name": "state", "value": "'visible'"}, {"name": "timeout", "value": "10000"}], "start": 1751853965552, "stop": 1751853965780}, {"name": "智能点击: 'text=账号登录'", "status": "passed", "steps": [{"name": "等待元素稳定: 'text=账号登录'", "status": "passed", "parameters": [{"name": "locator", "value": "'text=账号登录'"}, {"name": "timeout", "value": "None"}], "start": 1751853965780, "stop": 1751853966121}], "parameters": [{"name": "locator", "value": "'text=账号登录'"}, {"name": "force", "value": "False"}, {"name": "timeout", "value": "None"}], "start": 1751853965780, "stop": 1751853966185}], "start": 1751853965552, "stop": 1751853967201}], "start": 1751853964626, "stop": 1751853967201}, {"name": "测试验证码刷新功能", "status": "passed", "steps": [{"name": "检查元素是否可见: '.arco-image img'", "status": "passed", "parameters": [{"name": "locator", "value": "'.arco-image img'"}, {"name": "timeout", "value": "None"}], "start": 1751853967201, "stop": 1751853967212}, {"name": "刷新验证码", "status": "passed", "steps": [{"name": "检查元素是否可见: '.captcha-refresh'", "status": "passed", "parameters": [{"name": "locator", "value": "'.captcha-refresh'"}, {"name": "timeout", "value": "None"}], "start": 1751853967213, "stop": 1751853972218}, {"name": "检查元素是否可见: '.arco-image'", "status": "passed", "parameters": [{"name": "locator", "value": "'.arco-image'"}, {"name": "timeout", "value": "None"}], "start": 1751853972218, "stop": 1751853972228}, {"name": "智能点击: '.arco-image'", "status": "passed", "steps": [{"name": "等待元素稳定: '.arco-image'", "status": "passed", "parameters": [{"name": "locator", "value": "'.arco-image'"}, {"name": "timeout", "value": "None"}], "start": 1751853972229, "stop": 1751853972576}], "parameters": [{"name": "locator", "value": "'.arco-image'"}, {"name": "force", "value": "False"}, {"name": "timeout", "value": "None"}], "start": 1751853972228, "stop": 1751853972627}], "start": 1751853967213, "stop": 1751853974642}, {"name": "检查元素是否可见: 'input[placeholder='请输入图形验证码']'", "status": "passed", "parameters": [{"name": "locator", "value": "'input[placeholder='请输入图形验证码']'"}, {"name": "timeout", "value": "None"}], "start": 1751853974642, "stop": 1751853974650}], "start": 1751853967201, "stop": 1751853974650}, {"name": "测试验证码输入", "status": "passed", "steps": [{"name": "输入图形验证码: '1234'", "status": "passed", "steps": [{"name": "智能填充: 'input[placeholder='请输入图形验证码']' = '1234'", "status": "passed", "steps": [{"name": "等待元素稳定: 'input[placeholder='请输入图形验证码']'", "status": "passed", "parameters": [{"name": "locator", "value": "'input[placeholder='请输入图形验证码']'"}, {"name": "timeout", "value": "None"}], "start": 1751853974651, "stop": 1751853975014}], "parameters": [{"name": "locator", "value": "'input[placeholder='请输入图形验证码']'"}, {"name": "text", "value": "'1234'"}, {"name": "clear_first", "value": "True"}, {"name": "timeout", "value": "None"}], "start": 1751853974651, "stop": 1751853975054}], "parameters": [{"name": "<PERSON><PERSON>a", "value": "'1234'"}], "start": 1751853974650, "stop": 1751853975054}], "start": 1751853974650, "stop": 1751853975057}], "attachments": [{"name": "log", "source": "128998e9-bf8d-42a6-8309-c7a90ea2c340-attachment.txt", "type": "text/plain"}, {"name": "stdout", "source": "8ee60243-e366-421b-be5d-64d532383557-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "browser_name", "value": "'chromium'"}], "start": 1751853964586, "stop": 1751853975057, "uuid": "5989f729-92cb-4d36-addb-c00dd62cbf9b", "historyId": "9c48d705d01d7be2c7e42debe8eca4c9", "testCaseId": "c5ee54436ca1b45fcb049d6e49e06abd", "fullName": "tests.ui.smoke.test_scf_login.TestSCFLogin#test_captcha_functionality", "labels": [{"name": "feature", "value": "用户登录"}, {"name": "epic", "value": "供应链金融备案系统"}, {"name": "story", "value": "验证码功能测试"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "component"}, {"name": "parentSuite", "value": "tests.ui.smoke"}, {"name": "suite", "value": "test_scf_login"}, {"name": "subSuite", "value": "TestSC<PERSON><PERSON>in"}, {"name": "host", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "thread", "value": "14940-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.ui.smoke.test_scf_login"}]}