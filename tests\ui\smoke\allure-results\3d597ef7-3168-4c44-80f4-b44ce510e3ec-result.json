{"name": "test_registration_page_navigation[chromium]", "status": "passed", "description": "测试备案申请页面导航", "steps": [{"name": "登录系统", "status": "passed", "steps": [{"name": "登录系统", "status": "passed", "steps": [{"name": "导航到登录页面", "status": "passed", "steps": [{"name": "导航到页面: 'http://*************:8080/login'", "status": "passed", "parameters": [{"name": "url", "value": "'http://*************:8080/login'"}, {"name": "wait_until", "value": "'domcontentloaded'"}], "start": 1751854940148, "stop": 1751854941003}], "parameters": [{"name": "base_url", "value": "'http://*************:8080'"}], "start": 1751854940148, "stop": 1751854941003}, {"name": "执行登录操作", "status": "passed", "steps": [{"name": "执行登录操作（带重试机制）", "status": "passed", "steps": [{"name": "点击账号登录标签", "status": "passed", "steps": [{"name": "等待元素出现: 'text=账号登录'", "status": "passed", "parameters": [{"name": "locator", "value": "'text=账号登录'"}, {"name": "state", "value": "'visible'"}, {"name": "timeout", "value": "10000"}], "start": 1751854941014, "stop": 1751854941225}, {"name": "智能点击: 'text=账号登录'", "status": "passed", "steps": [{"name": "等待元素稳定: 'text=账号登录'", "status": "passed", "parameters": [{"name": "locator", "value": "'text=账号登录'"}, {"name": "timeout", "value": "None"}], "start": 1751854941226, "stop": 1751854941575}], "parameters": [{"name": "locator", "value": "'text=账号登录'"}, {"name": "force", "value": "False"}, {"name": "timeout", "value": "None"}], "start": 1751854941226, "stop": 1751854941647}], "start": 1751854941014, "stop": 1751854942654}, {"name": "输入用户名: 'scf_4nuioc'", "status": "passed", "steps": [{"name": "智能填充: 'input[placeholder='请输入账号']' = 'scf_4nuioc'", "status": "passed", "steps": [{"name": "等待元素稳定: 'input[placeholder='请输入账号']'", "status": "passed", "parameters": [{"name": "locator", "value": "'input[placeholder='请输入账号']'"}, {"name": "timeout", "value": "None"}], "start": 1751854942654, "stop": 1751854943008}], "parameters": [{"name": "locator", "value": "'input[placeholder='请输入账号']'"}, {"name": "text", "value": "'scf_4nuioc'"}, {"name": "clear_first", "value": "True"}, {"name": "timeout", "value": "None"}], "start": 1751854942654, "stop": 1751854943047}], "parameters": [{"name": "username", "value": "'scf_4nuioc'"}], "start": 1751854942654, "stop": 1751854943047}, {"name": "输入密码", "status": "passed", "steps": [{"name": "智能填充: 'input[type='password']' = 'Scf123456.'", "status": "passed", "steps": [{"name": "等待元素稳定: 'input[type='password']'", "status": "passed", "parameters": [{"name": "locator", "value": "'input[type='password']'"}, {"name": "timeout", "value": "None"}], "start": 1751854943048, "stop": 1751854943384}], "parameters": [{"name": "locator", "value": "'input[type='password']'"}, {"name": "text", "value": "'Scf123456.'"}, {"name": "clear_first", "value": "True"}, {"name": "timeout", "value": "None"}], "start": 1751854943048, "stop": 1751854943410}], "parameters": [{"name": "password", "value": "'Scf123456.'"}], "start": 1751854943047, "stop": 1751854943410}, {"name": "自动识别并输入验证码", "status": "passed", "steps": [{"name": "截取并保存验证码图片", "status": "passed", "steps": [{"name": "等待元素出现: '.arco-image img'", "status": "passed", "parameters": [{"name": "locator", "value": "'.arco-image img'"}, {"name": "state", "value": "'visible'"}, {"name": "timeout", "value": "10000"}], "start": 1751854943411, "stop": 1751854943416}], "attachments": [{"name": "验证码图片_尝试1", "source": "36bee02d-94a3-4d6f-b52c-fa4e14a5067d-attachment.png", "type": "image/png"}], "parameters": [{"name": "attempt", "value": "1"}], "start": 1751854943411, "stop": 1751854944503}, {"name": "OCR识别验证码", "status": "passed", "parameters": [{"name": "captcha_image", "value": "<class 'bytes'>"}, {"name": "attempt", "value": "1"}], "start": 1751854944503, "stop": 1751854944551}, {"name": "输入图形验证码: '3801'", "status": "passed", "steps": [{"name": "智能填充: 'input[placeholder='请输入图形验证码']' = '3801'", "status": "passed", "steps": [{"name": "等待元素稳定: 'input[placeholder='请输入图形验证码']'", "status": "passed", "parameters": [{"name": "locator", "value": "'input[placeholder='请输入图形验证码']'"}, {"name": "timeout", "value": "None"}], "start": 1751854944552, "stop": 1751854944905}], "parameters": [{"name": "locator", "value": "'input[placeholder='请输入图形验证码']'"}, {"name": "text", "value": "'3801'"}, {"name": "clear_first", "value": "True"}, {"name": "timeout", "value": "None"}], "start": 1751854944551, "stop": 1751854944968}], "parameters": [{"name": "<PERSON><PERSON>a", "value": "'3801'"}], "start": 1751854944551, "stop": 1751854944969}], "parameters": [{"name": "max_retries", "value": "1"}, {"name": "min_confidence", "value": "0.6"}], "start": 1751854943411, "stop": 1751854944969}, {"name": "点击登录按钮", "status": "passed", "steps": [{"name": "智能点击: 'button[type='submit']'", "status": "passed", "steps": [{"name": "等待元素稳定: 'button[type='submit']'", "status": "passed", "parameters": [{"name": "locator", "value": "'button[type='submit']'"}, {"name": "timeout", "value": "None"}], "start": 1751854944969, "stop": 1751854945329}], "parameters": [{"name": "locator", "value": "'button[type='submit']'"}, {"name": "force", "value": "False"}, {"name": "timeout", "value": "None"}], "start": 1751854944969, "stop": 1751854945452}], "start": 1751854944969, "stop": 1751854945452}, {"name": "检查登录状态", "status": "passed", "parameters": [{"name": "timeout", "value": "5000"}], "start": 1751854945452, "stop": 1751854946460}], "parameters": [{"name": "username", "value": "'scf_4nuioc'"}, {"name": "password", "value": "'Scf123456.'"}, {"name": "max_captcha_retries", "value": "5"}], "start": 1751854941013, "stop": 1751854946460}], "parameters": [{"name": "username", "value": "'scf_4nuioc'"}, {"name": "password", "value": "'Scf123456.'"}, {"name": "<PERSON><PERSON>a", "value": "None"}, {"name": "auto_captcha", "value": "True"}], "start": 1751854941003, "stop": 1751854946460}, {"name": "验证登录成功", "status": "passed", "steps": [{"name": "等待页面加载完成", "status": "passed", "parameters": [{"name": "state", "value": "'networkidle'"}, {"name": "timeout", "value": "None"}], "start": 1751854946461, "stop": 1751854946464}], "parameters": [{"name": "expected_url_pattern", "value": "None"}], "start": 1751854946460, "stop": 1751854946464}], "parameters": [{"name": "credentials", "value": "{'username': 'scf_4nuioc', 'password': 'Scf123456.', 'description': '标准测试用户'}"}, {"name": "base_url", "value": "'http://*************:8080'"}], "start": 1751854940148, "stop": 1751854946464}], "start": 1751854940148, "stop": 1751854946464}, {"name": "导航到备案申请页面", "status": "passed", "steps": [{"name": "进入备案申请", "status": "passed", "steps": [{"name": "点击备案登记菜单", "status": "passed", "steps": [{"name": "智能点击: 'text=备案登记'", "status": "passed", "steps": [{"name": "等待元素稳定: 'text=备案登记'", "status": "passed", "parameters": [{"name": "locator", "value": "'text=备案登记'"}, {"name": "timeout", "value": "None"}], "start": 1751854946465, "stop": 1751854946817}], "parameters": [{"name": "locator", "value": "'text=备案登记'"}, {"name": "force", "value": "False"}, {"name": "timeout", "value": "None"}], "start": 1751854946465, "stop": 1751854946879}], "start": 1751854946465, "stop": 1751854946879}, {"name": "点击备案申请按钮", "status": "passed", "steps": [{"name": "智能点击: 'role=button[name='备案申请']'", "status": "passed", "steps": [{"name": "等待元素稳定: 'role=button[name='备案申请']'", "status": "passed", "parameters": [{"name": "locator", "value": "'role=button[name='备案申请']'"}, {"name": "timeout", "value": "None"}], "start": 1751854946880, "stop": 1751854947425}], "parameters": [{"name": "locator", "value": "'role=button[name='备案申请']'"}, {"name": "force", "value": "False"}, {"name": "timeout", "value": "None"}], "start": 1751854946880, "stop": 1751854947489}], "start": 1751854946879, "stop": 1751854947489}], "start": 1751854946464, "stop": 1751854947489}], "start": 1751854946464, "stop": 1751854947489}, {"name": "验证页面导航成功", "status": "passed", "steps": [{"name": "检查元素是否可见: 'role=button[name='下一步']'", "status": "passed", "parameters": [{"name": "locator", "value": "'role=button[name='下一步']'"}, {"name": "timeout", "value": "None"}], "start": 1751854947489, "stop": 1751854947713}], "start": 1751854947489, "stop": 1751854947713}], "attachments": [{"name": "log", "source": "6b2d4c01-2f91-4ffb-b297-0e5be952f4c8-attachment.txt", "type": "text/plain"}, {"name": "stdout", "source": "21f997e0-76d0-434c-b67f-abd7645f3d02-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "browser_name", "value": "'chromium'"}], "start": 1751854940106, "stop": 1751854947714, "uuid": "9ab3ec5b-92d0-4eca-a4ae-62a5ce6e95c2", "historyId": "83f101a5ef5067531aeb708683b0609c", "testCaseId": "ef8d1e6eab25364cda6c886672bc4b37", "fullName": "tests.ui.smoke.test_scf_registration.TestSCFRegistration#test_registration_page_navigation", "labels": [{"name": "story", "value": "备案申请页面导航"}, {"name": "feature", "value": "备案申请"}, {"name": "epic", "value": "供应链金融备案系统"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "tests.ui.smoke"}, {"name": "suite", "value": "test_scf_registration"}, {"name": "subSuite", "value": "TestSCFRegistration"}, {"name": "host", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "thread", "value": "24016-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.ui.smoke.test_scf_registration"}]}