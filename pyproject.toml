[project]
name = "playwright-python-template"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "pytest",
    "pytest-playwright",
    "allure-pytest",
    "playwright",
    "faker",
    "ddddocr",
    "pyyaml",
]

[project.optional-dependencies]
dev = [
    "ruff",
    "black",
    "mypy",
    "pre-commit",
]

[tool.ruff]
line-length = 88
select = ["E", "F", "W", "I"]

[tool.black]
line-length = 88

[tool.pytest.ini_options]
pythonpath = [".", "fixtures"]
addopts = [
    "--alluredir=allure-results",
    "--clean-alluredir",
    "--tb=short",
    "--strict-markers",
    "--disable-warnings",
    # 取消下面的注释来默认显示浏览器界面
    # "--headed",
    # "--slowmo=500"
]
markers = [
    "smoke: 冒烟测试",
    "regression: 回归测试",
    "e2e: 端到端测试",
    "component: 组件测试",
    "api: API测试",
    "slow: 慢速测试",
    "skip_ci: 跳过CI执行的测试"
]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
show_error_codes = true

# 忽略第三方库的类型检查
[[tool.mypy.overrides]]
module = [
    "ddddocr.*",
    "faker.*",
    "allure.*",
    "pytest.*"
]
ignore_missing_imports = true

