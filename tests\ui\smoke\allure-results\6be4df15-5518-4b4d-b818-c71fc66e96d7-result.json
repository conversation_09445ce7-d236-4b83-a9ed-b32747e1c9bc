{"name": "test_login_failure_scenarios[chromium-empty_credentials-\\u5e94\\u8be5\\u63d0\\u793a\\u8f93\\u5165\\u7528\\u6237\\u540d\\u548c\\u5bc6\\u7801]", "status": "passed", "description": "测试登录失败场景", "steps": [{"name": "使用empty_credentials凭证登录", "status": "passed", "steps": [{"name": "登录系统", "status": "broken", "statusDetails": {"message": "KeyError: 'locator'\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\workflows\\scf_registration_workflow.py\", line 123, in _login_to_system\n    self.login_page.login(username, password, auto_captcha=True)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 157, in login\n    self.click_account_login_tab()\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 58, in click_account_login_tab\n    self.click(self.account_tab)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 201, in impl\n    with StepContext(self.title.format(*args, **params), params):\n                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "steps": [{"name": "导航到登录页面", "status": "passed", "steps": [{"name": "导航到页面: 'http://172.18.12.128:8080/login'", "status": "passed", "parameters": [{"name": "url", "value": "'http://172.18.12.128:8080/login'"}, {"name": "wait_until", "value": "'domcontentloaded'"}], "start": *********5117, "stop": *********5731}], "parameters": [{"name": "base_url", "value": "'http://172.18.12.128:8080'"}], "start": *********5117, "stop": *********5731}, {"name": "执行登录操作", "status": "broken", "statusDetails": {"message": "KeyError: 'locator'\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 157, in login\n    self.click_account_login_tab()\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 58, in click_account_login_tab\n    self.click(self.account_tab)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 201, in impl\n    with StepContext(self.title.format(*args, **params), params):\n                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "steps": [{"name": "点击账号登录标签", "status": "broken", "statusDetails": {"message": "KeyError: 'locator'\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 58, in click_account_login_tab\n    self.click(self.account_tab)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 201, in impl\n    with StepContext(self.title.format(*args, **params), params):\n                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "start": *********5731, "stop": *********5732}], "parameters": [{"name": "username", "value": "''"}, {"name": "password", "value": "''"}, {"name": "<PERSON><PERSON>a", "value": "None"}, {"name": "auto_captcha", "value": "True"}], "start": *********5731, "stop": *********5732}], "parameters": [{"name": "credentials", "value": "{'username': '', 'password': ''}"}, {"name": "base_url", "value": "'http://172.18.12.128:8080'"}], "start": *********5117, "stop": *********5732}], "attachments": [{"name": "登录失败信息", "source": "7a5a9012-88c9-4808-82c4-059ed84e5fa8-attachment.txt", "type": "text/plain"}], "start": *********5117, "stop": *********5734}, {"name": "验证登录失败行为", "status": "passed", "start": *********5734, "stop": *********5734}], "attachments": [{"name": "log", "source": "18746c4a-dd7d-4fd6-840a-fbcdf07f57a2-attachment.txt", "type": "text/plain"}, {"name": "stderr", "source": "4ffab6e2-1c88-47fb-affb-dddfef8618dc-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "browser_name", "value": "'chromium'"}, {"name": "credentials_key", "value": "'empty_credentials'"}, {"name": "expected_behavior", "value": "'应该提示输入用户名和密码'"}], "start": *********5077, "stop": *********5734, "uuid": "273a4865-caa7-4647-b6a6-d718a0015e4b", "historyId": "d370f9b387261f6b14d16c225a143019", "testCaseId": "a99cc8e5e6e05ba82a9dc1ae7049de1b", "fullName": "tests.ui.smoke.test_scf_login.TestSCFLogin#test_login_failure_scenarios", "labels": [{"name": "feature", "value": "用户登录"}, {"name": "epic", "value": "供应链金融备案系统"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "登录失败场景"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "tests.ui.smoke"}, {"name": "suite", "value": "test_scf_login"}, {"name": "subSuite", "value": "TestSC<PERSON><PERSON>in"}, {"name": "host", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "thread", "value": "21436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.ui.smoke.test_scf_login"}]}