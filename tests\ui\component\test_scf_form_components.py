"""
供应链金融备案系统表单组件测试
SCF Form Components Tests
"""

import pytest
import allure
from workflows.scf_registration_workflow import SCFRegistrationWorkflow


@allure.epic("供应链金融备案系统")
@allure.feature("表单组件")
class TestSCFFormComponents:
    """供应链金融表单组件测试类"""
    
    @allure.story("业务合规表单组件")
    @allure.severity(allure.severity_level.NORMAL)
    @pytest.mark.component
    def test_business_compliance_form_component(self, 
                                              page, 
                                              scf_base_url, 
                                              scf_login_credentials,
                                              scf_form_defaults):
        """测试业务合规表单组件"""
        # 准备测试数据
        workflow = SCFRegistrationWorkflow(page)
        credentials = scf_login_credentials["standard_user"]
        
        # 登录并导航到业务合规表单
        with allure.step("导航到业务合规表单"):
            workflow._login_to_system(credentials, scf_base_url)
            workflow._navigate_to_registration()
            workflow.registration_page.click_next_step()  # 跳过基础材料
        
        # 测试表单组件功能
        with allure.step("测试是/否选择组件"):
            test_data = {
                "bcVele": {
                    "option": "是",
                    "description": scf_form_defaults.get("description_text", "测试")
                }
            }
            workflow.registration_page.fill_business_compliance_form(test_data)
        
        with allure.step("验证选择结果"):
            # 验证可以继续到下一步
            workflow.registration_page.click_next_step()
        
        with allure.step("测试表单重置"):
            # 返回上一步测试表单状态
            workflow.page.go_back()
            
            # 重新填写不同的选项
            test_data_2 = {
                "bcVele": {
                    "option": "否",
                    "description": "重新测试"
                }
            }
            workflow.registration_page.fill_business_compliance_form(test_data_2)
    
    @allure.story("文件上传组件")
    @allure.severity(allure.severity_level.NORMAL)
    @pytest.mark.component
    def test_file_upload_component(self, 
                                 page, 
                                 scf_base_url, 
                                 scf_login_credentials,
                                 scf_file_paths,
                                 scf_test_files_validator):
        """测试文件上传组件"""
        # 验证测试文件
        valid_files = scf_file_paths.get("valid_files", {})
        if not valid_files:
            pytest.skip("没有可用的测试文件")
        
        validation_results = scf_test_files_validator(valid_files)
        available_files = {k: v for k, v in valid_files.items() if validation_results.get(k, False)}
        
        if not available_files:
            pytest.skip("所有测试文件都不存在")
        
        # 准备测试数据
        workflow = SCFRegistrationWorkflow(page)
        credentials = scf_login_credentials["standard_user"]
        
        # 登录并导航到文件上传页面
        with allure.step("导航到文件上传页面"):
            workflow._login_to_system(credentials, scf_base_url)
            workflow._navigate_to_registration()
        
        # 测试单个文件上传
        with allure.step("测试单个文件上传"):
            test_file = list(available_files.values())[0]
            workflow.registration_page.upload_file(test_file)
        
        # 测试多个文件上传
        with allure.step("测试多个文件上传"):
            if len(available_files) > 1:
                file_uploads = {
                    "bfmArtAssocId": list(available_files.values())[0],
                    "bfmAuditRptId": list(available_files.values())[1] if len(available_files) > 1 else list(available_files.values())[0]
                }
                workflow.registration_page.upload_files_to_areas(file_uploads)
        
        with allure.step("验证文件上传功能"):
            # 验证可以继续到下一步
            workflow.registration_page.click_next_step()
    
    @allure.story("情况说明文本框组件")
    @allure.severity(allure.severity_level.NORMAL)
    @pytest.mark.component
    def test_description_textarea_component(self, 
                                          page, 
                                          scf_base_url, 
                                          scf_login_credentials,
                                          scf_form_defaults):
        """测试情况说明文本框组件"""
        # 准备测试数据
        workflow = SCFRegistrationWorkflow(page)
        credentials = scf_login_credentials["standard_user"]
        max_length = scf_form_defaults.get("max_description_length", 1000)
        
        # 登录并导航到表单页面
        with allure.step("导航到表单页面"):
            workflow._login_to_system(credentials, scf_base_url)
            workflow._navigate_to_registration()
            workflow.registration_page.click_next_step()  # 跳过基础材料
        
        # 测试正常文本输入
        with allure.step("测试正常文本输入"):
            normal_text = "这是一个正常的情况说明文本"
            test_data = {
                "bcVele": {
                    "option": "否",
                    "description": normal_text
                }
            }
            workflow.registration_page.fill_business_compliance_form(test_data)
        
        # 测试长文本输入
        with allure.step("测试长文本输入"):
            long_text = "测试" * (max_length // 2)  # 创建一个长文本
            test_data_long = {
                "bcVbsaroa": {
                    "option": "否",
                    "description": long_text
                }
            }
            workflow.registration_page.fill_business_compliance_form(test_data_long)
        
        # 测试特殊字符输入
        with allure.step("测试特殊字符输入"):
            special_text = "测试特殊字符：!@#$%^&*()_+-=[]{}|;':\",./<>?"
            test_data_special = {
                "bcCoo": {
                    "option": "否",
                    "description": special_text
                }
            }
            workflow.registration_page.fill_business_compliance_form(test_data_special)
        
        with allure.step("验证文本输入功能"):
            # 验证可以继续到下一步
            workflow.registration_page.click_next_step()
    
    @allure.story("专业机构下拉选择组件")
    @allure.severity(allure.severity_level.NORMAL)
    @pytest.mark.component
    def test_institution_dropdown_component(self, 
                                          page, 
                                          scf_base_url, 
                                          scf_login_credentials,
                                          scf_institutions_list):
        """测试专业机构下拉选择组件"""
        # 准备测试数据
        workflow = SCFRegistrationWorkflow(page)
        credentials = scf_login_credentials["standard_user"]
        default_institution = scf_institutions_list.get("default")
        
        # 登录系统
        with allure.step("登录系统"):
            workflow._login_to_system(credentials, scf_base_url)
        
        # 快速导航到机构选择步骤
        with allure.step("导航到机构选择步骤"):
            workflow._navigate_to_registration()
            
            # 快速跳过前面的步骤
            for _ in range(10):
                try:
                    workflow.registration_page.click_next_step()
                    workflow.page.wait_for_timeout(500)
                except Exception:
                    break
        
        # 测试下拉选择功能
        with allure.step("测试下拉选择功能"):
            try:
                # 测试点击下拉框
                if workflow.registration_page.is_visible(workflow.registration_page.institution_dropdown):
                    workflow.registration_page.click(workflow.registration_page.institution_dropdown)
                    
                    # 测试选择选项
                    if default_institution:
                        workflow.registration_page.select_institution(default_institution)
                else:
                    pytest.skip("机构下拉框不在当前页面")
            except Exception as e:
                allure.attach(str(e), "下拉选择异常", allure.attachment_type.TEXT)
                pytest.skip("机构选择组件测试跳过")
        
        with allure.step("验证选择结果"):
            # 这里可以添加具体的验证逻辑
            pass
    
    @allure.story("表单验证组件")
    @allure.severity(allure.severity_level.NORMAL)
    @pytest.mark.component
    def test_form_validation_component(self, 
                                     page, 
                                     scf_base_url, 
                                     scf_login_credentials):
        """测试表单验证组件"""
        # 准备测试数据
        workflow = SCFRegistrationWorkflow(page)
        credentials = scf_login_credentials["standard_user"]
        
        # 登录并导航到表单页面
        with allure.step("导航到表单页面"):
            workflow._login_to_system(credentials, scf_base_url)
            workflow._navigate_to_registration()
            workflow.registration_page.click_next_step()  # 跳过基础材料
        
        # 测试空表单提交
        with allure.step("测试空表单提交"):
            try:
                workflow.registration_page.click_next_step()
                # 如果能继续，说明没有必填验证或验证通过
            except Exception as e:
                # 如果有验证错误，记录但不失败
                allure.attach(str(e), "表单验证信息", allure.attachment_type.TEXT)
        
        # 测试部分填写表单
        with allure.step("测试部分填写表单"):
            partial_data = {
                "bcVele": {
                    "option": "否",
                    "description": ""  # 空描述
                }
            }
            workflow.registration_page.fill_business_compliance_form(partial_data)
            
            try:
                workflow.registration_page.click_next_step()
            except Exception as e:
                allure.attach(str(e), "部分填写验证信息", allure.attachment_type.TEXT)
        
        with allure.step("测试完整填写表单"):
            complete_data = {
                "bcVbsaroa": {
                    "option": "否",
                    "description": "完整的情况说明"
                }
            }
            workflow.registration_page.fill_business_compliance_form(complete_data)
            
            # 完整填写应该能够继续
            workflow.registration_page.click_next_step()
    
    @allure.story("表单导航组件")
    @allure.severity(allure.severity_level.MINOR)
    @pytest.mark.component
    def test_form_navigation_component(self, 
                                     page, 
                                     scf_base_url, 
                                     scf_login_credentials):
        """测试表单导航组件"""
        # 准备测试数据
        workflow = SCFRegistrationWorkflow(page)
        credentials = scf_login_credentials["standard_user"]
        
        # 登录并导航到表单
        with allure.step("导航到表单"):
            workflow._login_to_system(credentials, scf_base_url)
            workflow._navigate_to_registration()
        
        # 测试前进导航
        with allure.step("测试前进导航"):
            initial_url = workflow.get_current_url()
            workflow.registration_page.click_next_step()
            
            # 验证URL发生变化
            new_url = workflow.get_current_url()
            # 注意：URL可能不会变化，这取决于应用的实现方式
        
        # 测试后退导航
        with allure.step("测试后退导航"):
            workflow.page.go_back()
            
            # 验证能够返回
            current_url = workflow.get_current_url()
        
        # 测试重新前进
        with allure.step("测试重新前进"):
            workflow.registration_page.click_next_step()
        
        with allure.step("验证导航功能"):
            # 验证导航按钮仍然可用
            assert workflow.registration_page.is_visible(workflow.registration_page.next_step_button), "导航按钮应该可用"
