"""
自定义重试装饰器
提供灵活的重试机制，支持多种异常类型和重试策略
"""

import time
import functools
from typing import Union, Tuple, Callable, Any, Optional, Type, List
import random
from components.log_manager import get_logger

logger = get_logger(__name__)


class RetryConfig:
    """重试配置类"""
    
    def __init__(self,
                 max_attempts: int = 3,
                 delay: float = 1.0,
                 backoff_factor: float = 2.0,
                 max_delay: float = 60.0,
                 jitter: bool = True,
                 exceptions: Union[Type[Exception], Tuple[Type[Exception], ...]] = Exception):
        """
        初始化重试配置
        
        Args:
            max_attempts: 最大尝试次数
            delay: 初始延迟时间（秒）
            backoff_factor: 退避因子，每次重试延迟时间的倍数
            max_delay: 最大延迟时间（秒）
            jitter: 是否添加随机抖动
            exceptions: 需要重试的异常类型
        """
        self.max_attempts = max_attempts
        self.delay = delay
        self.backoff_factor = backoff_factor
        self.max_delay = max_delay
        self.jitter = jitter
        self.exceptions = exceptions if isinstance(exceptions, tuple) else (exceptions,)


def retry_on_exception(config: Optional[RetryConfig] = None,
                      max_attempts: int = 3,
                      delay: float = 1.0,
                      backoff_factor: float = 2.0,
                      max_delay: float = 60.0,
                      jitter: bool = True,
                      exceptions: Union[Type[Exception], Tuple[Type[Exception], ...]] = Exception,
                      on_retry: Optional[Callable[[int, Exception], None]] = None,
                      on_failure: Optional[Callable[[Exception], None]] = None):
    """
    重试装饰器
    
    Args:
        config: 重试配置对象，如果提供则忽略其他参数
        max_attempts: 最大尝试次数
        delay: 初始延迟时间（秒）
        backoff_factor: 退避因子
        max_delay: 最大延迟时间（秒）
        jitter: 是否添加随机抖动
        exceptions: 需要重试的异常类型
        on_retry: 重试时的回调函数，接收参数(attempt, exception)
        on_failure: 最终失败时的回调函数，接收参数(exception)
    
    Returns:
        装饰器函数
    """
    if config is None:
        config = RetryConfig(
            max_attempts=max_attempts,
            delay=delay,
            backoff_factor=backoff_factor,
            max_delay=max_delay,
            jitter=jitter,
            exceptions=exceptions
        )
    
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(config.max_attempts):
                try:
                    logger.debug(f"执行函数 {func.__name__}，尝试 {attempt + 1}/{config.max_attempts}")
                    result = func(*args, **kwargs)
                    
                    if attempt > 0:
                        logger.info(f"函数 {func.__name__} 在第 {attempt + 1} 次尝试后成功")
                    
                    return result
                    
                except config.exceptions as e:
                    last_exception = e
                    logger.warning(f"函数 {func.__name__} 第 {attempt + 1} 次尝试失败: {e}")
                    
                    # 调用重试回调
                    if on_retry:
                        try:
                            on_retry(attempt + 1, e)
                        except Exception as callback_error:
                            logger.error(f"重试回调函数执行失败: {callback_error}")
                    
                    # 如果不是最后一次尝试，则等待后重试
                    if attempt < config.max_attempts - 1:
                        wait_time = _calculate_wait_time(
                            attempt, 
                            config.delay, 
                            config.backoff_factor, 
                            config.max_delay, 
                            config.jitter
                        )
                        logger.debug(f"等待 {wait_time:.2f} 秒后重试")
                        time.sleep(wait_time)
                
                except Exception as e:
                    # 不在重试异常列表中的异常直接抛出
                    logger.error(f"函数 {func.__name__} 遇到不可重试的异常: {e}")
                    raise
            
            # 所有尝试都失败了
            logger.error(f"函数 {func.__name__} 在 {config.max_attempts} 次尝试后仍然失败")
            
            # 调用失败回调
            if on_failure and last_exception:
                try:
                    on_failure(last_exception)
                except Exception as callback_error:
                    logger.error(f"失败回调函数执行失败: {callback_error}")
            
            # 抛出最后一个异常
            if last_exception:
                raise last_exception
            else:
                raise RuntimeError(f"函数 {func.__name__} 执行失败，但没有捕获到异常")
        
        return wrapper
    return decorator


def _calculate_wait_time(attempt: int, 
                        base_delay: float, 
                        backoff_factor: float, 
                        max_delay: float, 
                        jitter: bool) -> float:
    """
    计算等待时间
    
    Args:
        attempt: 当前尝试次数（从0开始）
        base_delay: 基础延迟时间
        backoff_factor: 退避因子
        max_delay: 最大延迟时间
        jitter: 是否添加抖动
    
    Returns:
        等待时间（秒）
    """
    # 指数退避
    wait_time = base_delay * (backoff_factor ** attempt)
    
    # 限制最大延迟
    wait_time = min(wait_time, max_delay)
    
    # 添加随机抖动
    if jitter:
        jitter_range = wait_time * 0.1  # 10%的抖动
        wait_time += random.uniform(-jitter_range, jitter_range)
    
    return max(0, wait_time)


def retry_with_condition(condition: Callable[[Any], bool],
                        max_attempts: int = 3,
                        delay: float = 1.0,
                        backoff_factor: float = 2.0,
                        max_delay: float = 60.0,
                        jitter: bool = True):
    """
    基于条件的重试装饰器
    
    Args:
        condition: 判断是否需要重试的函数，接收函数返回值，返回True表示需要重试
        max_attempts: 最大尝试次数
        delay: 初始延迟时间
        backoff_factor: 退避因子
        max_delay: 最大延迟时间
        jitter: 是否添加抖动
    
    Returns:
        装饰器函数
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            for attempt in range(max_attempts):
                logger.debug(f"执行函数 {func.__name__}，尝试 {attempt + 1}/{max_attempts}")
                
                try:
                    result = func(*args, **kwargs)
                    
                    # 检查是否需要重试
                    if not condition(result):
                        if attempt > 0:
                            logger.info(f"函数 {func.__name__} 在第 {attempt + 1} 次尝试后成功")
                        return result
                    else:
                        logger.warning(f"函数 {func.__name__} 第 {attempt + 1} 次尝试结果不满足条件")
                        
                        if attempt < max_attempts - 1:
                            wait_time = _calculate_wait_time(
                                attempt, delay, backoff_factor, max_delay, jitter
                            )
                            logger.debug(f"等待 {wait_time:.2f} 秒后重试")
                            time.sleep(wait_time)
                        else:
                            logger.error(f"函数 {func.__name__} 在 {max_attempts} 次尝试后仍不满足条件")
                            return result
                
                except Exception as e:
                    logger.error(f"函数 {func.__name__} 执行出错: {e}")
                    raise
            
            # 这里不应该到达，但为了安全起见
            return func(*args, **kwargs)
        
        return wrapper
    return decorator


class RetryableOperation:
    """可重试操作类"""
    
    def __init__(self, config: Optional[RetryConfig] = None):
        """
        初始化可重试操作
        
        Args:
            config: 重试配置
        """
        self.config = config or RetryConfig()
    
    def execute(self, 
                operation: Callable,
                *args,
                on_retry: Optional[Callable[[int, Exception], None]] = None,
                on_failure: Optional[Callable[[Exception], None]] = None,
                **kwargs) -> Any:
        """
        执行可重试操作
        
        Args:
            operation: 要执行的操作函数
            *args: 操作函数的位置参数
            on_retry: 重试回调函数
            on_failure: 失败回调函数
            **kwargs: 操作函数的关键字参数
        
        Returns:
            操作函数的返回值
        """
        last_exception = None
        
        for attempt in range(self.config.max_attempts):
            try:
                logger.debug(f"执行操作，尝试 {attempt + 1}/{self.config.max_attempts}")
                result = operation(*args, **kwargs)
                
                if attempt > 0:
                    logger.info(f"操作在第 {attempt + 1} 次尝试后成功")
                
                return result
                
            except self.config.exceptions as e:
                last_exception = e
                logger.warning(f"操作第 {attempt + 1} 次尝试失败: {e}")
                
                if on_retry:
                    try:
                        on_retry(attempt + 1, e)
                    except Exception as callback_error:
                        logger.error(f"重试回调函数执行失败: {callback_error}")
                
                if attempt < self.config.max_attempts - 1:
                    wait_time = _calculate_wait_time(
                        attempt,
                        self.config.delay,
                        self.config.backoff_factor,
                        self.config.max_delay,
                        self.config.jitter
                    )
                    logger.debug(f"等待 {wait_time:.2f} 秒后重试")
                    time.sleep(wait_time)
            
            except Exception as e:
                logger.error(f"操作遇到不可重试的异常: {e}")
                raise
        
        # 所有尝试都失败了
        logger.error(f"操作在 {self.config.max_attempts} 次尝试后仍然失败")
        
        if on_failure and last_exception:
            try:
                on_failure(last_exception)
            except Exception as callback_error:
                logger.error(f"失败回调函数执行失败: {callback_error}")
        
        if last_exception:
            raise last_exception
        else:
            raise RuntimeError("操作执行失败，但没有捕获到异常")


# 预定义的重试配置
QUICK_RETRY = RetryConfig(max_attempts=3, delay=0.5, backoff_factor=1.5)
STANDARD_RETRY = RetryConfig(max_attempts=5, delay=1.0, backoff_factor=2.0)
PATIENT_RETRY = RetryConfig(max_attempts=10, delay=2.0, backoff_factor=1.5, max_delay=30.0)


# 便捷函数
def quick_retry(func: Callable) -> Callable:
    """快速重试装饰器（3次尝试，0.5秒延迟）"""
    return retry_on_exception(config=QUICK_RETRY)(func)


def standard_retry(func: Callable) -> Callable:
    """标准重试装饰器（5次尝试，1秒延迟）"""
    return retry_on_exception(config=STANDARD_RETRY)(func)


def patient_retry(func: Callable) -> Callable:
    """耐心重试装饰器（10次尝试，2秒延迟）"""
    return retry_on_exception(config=PATIENT_RETRY)(func)
