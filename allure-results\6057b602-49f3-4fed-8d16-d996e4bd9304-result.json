{"name": "test_captcha_functionality[chromium]", "status": "passed", "description": "测试验证码功能", "steps": [{"name": "导航到登录页面", "status": "passed", "steps": [{"name": "导航到登录页面", "status": "passed", "steps": [{"name": "导航到页面: 'http://*************:8080/login'", "status": "passed", "parameters": [{"name": "url", "value": "'http://*************:8080/login'"}, {"name": "wait_until", "value": "'domcontentloaded'"}], "start": 1751853712009, "stop": 1751853712902}], "parameters": [{"name": "base_url", "value": "'http://*************:8080'"}], "start": 1751853712009, "stop": 1751853712902}, {"name": "点击账号登录标签", "status": "passed", "steps": [{"name": "等待元素出现: 'text=账号登录'", "status": "passed", "parameters": [{"name": "locator", "value": "'text=账号登录'"}, {"name": "state", "value": "'visible'"}, {"name": "timeout", "value": "10000"}], "start": 1751853712902, "stop": 1751853713120}, {"name": "智能点击: 'text=账号登录'", "status": "passed", "steps": [{"name": "等待元素稳定: 'text=账号登录'", "status": "passed", "parameters": [{"name": "locator", "value": "'text=账号登录'"}, {"name": "timeout", "value": "None"}], "start": 1751853713121, "stop": 1751853713469}], "parameters": [{"name": "locator", "value": "'text=账号登录'"}, {"name": "force", "value": "False"}, {"name": "timeout", "value": "None"}], "start": 1751853713120, "stop": 1751853713554}], "start": 1751853712902, "stop": 1751853714556}], "start": 1751853712008, "stop": 1751853714556}, {"name": "测试验证码刷新功能", "status": "passed", "steps": [{"name": "检查元素是否可见: '.arco-image img'", "status": "passed", "parameters": [{"name": "locator", "value": "'.arco-image img'"}, {"name": "timeout", "value": "None"}], "start": 1751853714556, "stop": 1751853714561}, {"name": "刷新验证码", "status": "passed", "steps": [{"name": "检查元素是否可见: '.captcha-refresh'", "status": "passed", "parameters": [{"name": "locator", "value": "'.captcha-refresh'"}, {"name": "timeout", "value": "None"}], "start": 1751853714561, "stop": 1751853719578}, {"name": "检查元素是否可见: '.arco-image'", "status": "passed", "parameters": [{"name": "locator", "value": "'.arco-image'"}, {"name": "timeout", "value": "None"}], "start": 1751853719578, "stop": 1751853719580}, {"name": "智能点击: '.arco-image'", "status": "passed", "steps": [{"name": "等待元素稳定: '.arco-image'", "status": "passed", "parameters": [{"name": "locator", "value": "'.arco-image'"}, {"name": "timeout", "value": "None"}], "start": 1751853719580, "stop": 1751853719911}], "parameters": [{"name": "locator", "value": "'.arco-image'"}, {"name": "force", "value": "False"}, {"name": "timeout", "value": "None"}], "start": 1751853719580, "stop": 1751853719969}], "start": 1751853714561, "stop": 1751853721982}, {"name": "检查元素是否可见: 'input[placeholder='请输入图形验证码']'", "status": "passed", "parameters": [{"name": "locator", "value": "'input[placeholder='请输入图形验证码']'"}, {"name": "timeout", "value": "None"}], "start": 1751853721982, "stop": 1751853721991}], "start": 1751853714556, "stop": 1751853721991}, {"name": "测试验证码输入", "status": "passed", "steps": [{"name": "输入图形验证码: '1234'", "status": "passed", "steps": [{"name": "智能填充: 'input[placeholder='请输入图形验证码']' = '1234'", "status": "passed", "steps": [{"name": "等待元素稳定: 'input[placeholder='请输入图形验证码']'", "status": "passed", "parameters": [{"name": "locator", "value": "'input[placeholder='请输入图形验证码']'"}, {"name": "timeout", "value": "None"}], "start": 1751853721992, "stop": 1751853722343}], "parameters": [{"name": "locator", "value": "'input[placeholder='请输入图形验证码']'"}, {"name": "text", "value": "'1234'"}, {"name": "clear_first", "value": "True"}, {"name": "timeout", "value": "None"}], "start": 1751853721991, "stop": 1751853722379}], "parameters": [{"name": "<PERSON><PERSON>a", "value": "'1234'"}], "start": 1751853721991, "stop": 1751853722379}], "start": 1751853721991, "stop": 1751853722382}], "attachments": [{"name": "log", "source": "c9da1abd-8eda-432d-9486-499cbd000ad5-attachment.txt", "type": "text/plain"}, {"name": "stdout", "source": "3cc912dd-8f83-4c42-bd24-54a35094dce5-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "browser_name", "value": "'chromium'"}], "start": 1751853711967, "stop": 1751853722384, "uuid": "ee077b18-f418-47b7-9c6f-2278be171c5e", "historyId": "9c48d705d01d7be2c7e42debe8eca4c9", "testCaseId": "c5ee54436ca1b45fcb049d6e49e06abd", "fullName": "tests.ui.smoke.test_scf_login.TestSCFLogin#test_captcha_functionality", "labels": [{"name": "feature", "value": "用户登录"}, {"name": "story", "value": "验证码功能测试"}, {"name": "epic", "value": "供应链金融备案系统"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "component"}, {"name": "parentSuite", "value": "tests.ui.smoke"}, {"name": "suite", "value": "test_scf_login"}, {"name": "subSuite", "value": "TestSC<PERSON><PERSON>in"}, {"name": "host", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "thread", "value": "23568-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.ui.smoke.test_scf_login"}]}