#!/usr/bin/env python3
"""
SCF 备案流程正常场景测试
测试完整的备案申请流程
"""

import pytest
import allure
from playwright.sync_api import Page
from workflows.scf_filing_workflow import SCFFilingWorkflow
from components.filing_data_manager import FilingDataManager
from components.log_manager import get_logger


@allure.epic("SCF备案系统")
@allure.feature("备案申请流程")
class TestSCFFilingNormal:
    """SCF 备案流程正常场景测试类"""
    
    def setup_method(self):
        """测试前置条件"""
        self.logger = get_logger(__name__)
        self.data_manager = FilingDataManager()
        
    def teardown_method(self):
        """测试后置清理"""
        # 清理测试数据
        self.data_manager.cleanup_old_sessions(days=1)
    
    @allure.story("完整备案流程")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def test_complete_filing_process_success(self, page: Page):
        """
        测试完整备案流程成功场景
        
        测试步骤：
        1. 登录系统
        2. 导航到备案申请页面
        3. 填写基础材料
        4. 上传补充材料
        5. 完成合规性检查
        6. 提交申请
        7. 验证申请成功
        """
        # 初始化工作流
        filing_workflow = SCFFilingWorkflow(page)
        
        # 生成测试数据
        test_data = self.data_manager.generate_filing_test_data("normal")
        
        # 执行完整备案流程
        filing_workflow.complete_filing_process(
            username=test_data["user_info"]["username"],
            password=test_data["user_info"]["password"],
            filing_data=test_data
        )
        
        # 验证申请结果
        filing_workflow.verify_filing_result("已提交")
    
    @allure.story("基础材料上传")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.regression
    def test_basic_materials_upload(self, page: Page):
        """
        测试基础材料上传功能
        
        测试步骤：
        1. 登录并导航到备案申请页面
        2. 上传基础文档
        3. 上传关联文档
        4. 验证上传成功
        5. 进入下一步
        """
        filing_workflow = SCFFilingWorkflow(page)
        test_data = self.data_manager.generate_filing_test_data("normal")
        
        # 登录并导航
        filing_workflow.login_to_system(
            test_data["user_info"]["username"],
            test_data["user_info"]["password"]
        )
        filing_workflow.navigate_to_filing_application()
        
        # 完成第一步：基础材料上传
        filing_workflow.filing_page.complete_step1_basic_materials(test_data["file_info"])
        
        # 验证进入下一步
        filing_workflow.filing_page.verify_current_step()
    
    @allure.story("补充材料上传")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.regression
    def test_supplementary_materials_upload(self, page: Page):
        """
        测试补充材料上传功能
        
        测试步骤：
        1. 完成基础材料上传
        2. 上传各类补充材料
        3. 验证上传成功
        4. 进入下一步
        """
        filing_workflow = SCFFilingWorkflow(page)
        test_data = self.data_manager.generate_filing_test_data("normal")
        
        # 登录并导航
        filing_workflow.login_to_system(
            test_data["user_info"]["username"],
            test_data["user_info"]["password"]
        )
        filing_workflow.navigate_to_filing_application()
        
        # 完成前两步
        filing_workflow.filing_page.complete_step1_basic_materials(test_data["file_info"])
        filing_workflow.filing_page.complete_step2_supplementary_materials(test_data["file_info"])
        
        # 验证进入下一步
        filing_workflow.filing_page.verify_current_step()
    
    @allure.story("合规性检查")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.regression
    def test_compliance_check_process(self, page: Page):
        """
        测试合规性检查流程
        
        测试步骤：
        1. 完成前置步骤
        2. 填写合规性检查表单
        3. 选择是/否选项
        4. 填写情况说明
        5. 上传相关文档
        6. 验证检查完成
        """
        filing_workflow = SCFFilingWorkflow(page)
        test_data = self.data_manager.generate_filing_test_data("normal")
        
        # 登录并导航
        filing_workflow.login_to_system(
            test_data["user_info"]["username"],
            test_data["user_info"]["password"]
        )
        filing_workflow.navigate_to_filing_application()
        
        # 完成前置步骤
        filing_workflow.filing_page.complete_step1_basic_materials(test_data["file_info"])
        filing_workflow.filing_page.complete_step2_supplementary_materials(test_data["file_info"])
        
        # 执行合规性检查步骤
        filing_workflow.execute_compliance_steps(test_data)
        
        # 验证检查完成
        filing_workflow.filing_page.verify_current_step()
    
    @allure.story("专业机构选择")
    @allure.severity(allure.severity_level.NORMAL)
    @pytest.mark.regression
    def test_institution_selection(self, page: Page):
        """
        测试专业机构选择功能
        
        测试步骤：
        1. 导航到最后一步
        2. 选择专业机构
        3. 验证选择成功
        """
        filing_workflow = SCFFilingWorkflow(page)
        test_data = self.data_manager.generate_filing_test_data("normal")
        
        # 登录并导航
        filing_workflow.login_to_system(
            test_data["user_info"]["username"],
            test_data["user_info"]["password"]
        )
        filing_workflow.navigate_to_filing_application()
        
        # 快速完成前置步骤（简化版）
        # 这里可以添加快速完成前置步骤的逻辑
        
        # 选择专业机构
        filing_workflow.filing_page.select_institution(
            test_data["form_data"]["institution_name"]
        )
        
        # 验证选择成功
        # 可以添加验证逻辑
    
    @allure.story("申请保存")
    @allure.severity(allure.severity_level.NORMAL)
    @pytest.mark.regression
    def test_application_save(self, page: Page):
        """
        测试申请保存功能
        
        测试步骤：
        1. 完成部分申请流程
        2. 点击保存按钮
        3. 验证保存成功
        4. 验证可以继续编辑
        """
        filing_workflow = SCFFilingWorkflow(page)
        test_data = self.data_manager.generate_filing_test_data("normal")
        
        # 登录并导航
        filing_workflow.login_to_system(
            test_data["user_info"]["username"],
            test_data["user_info"]["password"]
        )
        filing_workflow.navigate_to_filing_application()
        
        # 完成部分步骤
        filing_workflow.filing_page.complete_step1_basic_materials(test_data["file_info"])
        
        # 保存申请
        filing_workflow.filing_page.click_save_button()
        
        # 验证保存成功
        # 可以添加保存成功的验证逻辑
    
    @allure.story("多文件上传")
    @allure.severity(allure.severity_level.NORMAL)
    @pytest.mark.regression
    def test_multiple_file_upload(self, page: Page):
        """
        测试多文件上传功能
        
        测试步骤：
        1. 导航到文件上传页面
        2. 上传多个不同类型的文件
        3. 验证所有文件上传成功
        4. 验证文件显示正确
        """
        filing_workflow = SCFFilingWorkflow(page)
        test_data = self.data_manager.generate_filing_test_data("normal")
        
        # 登录并导航
        filing_workflow.login_to_system(
            test_data["user_info"]["username"],
            test_data["user_info"]["password"]
        )
        filing_workflow.navigate_to_filing_application()
        
        # 测试多文件上传
        file_fields = ["bfmArtAssocId", "bfmAuditRptId", "bfmFrameCoopId"]
        for field in file_fields:
            filing_workflow.filing_page.upload_attachment_to_field(
                field, test_data["file_info"]["supplement_doc"]
            )
        
        # 验证文件上传成功
        # 可以添加文件上传验证逻辑
