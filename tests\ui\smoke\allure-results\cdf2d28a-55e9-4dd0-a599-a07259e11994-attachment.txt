fixtures.scf_fixtures - INFO - 加载SCF系统配置成功
fixtures.scf_fixtures - INFO - 加载SCF测试数据成功
fixtures.scf_fixtures - INFO - 准备SCF登录凭证
components.ocr_util - INFO - OCR引擎初始化成功
SCFRegistrationWorkflow - INFO - 开始执行完整的备案申请流程
SCFRegistrationWorkflow - INFO - 开始登录系统
SCFLoginPage - INFO - 导航到登录页面: http://172.18.12.128:8080/login
pages.base_page - INFO - 导航到页面: http://172.18.12.128:8080/login
SCFLoginPage - INFO - 开始登录流程，用户名: scf_4nuioc
SCFLoginPage - INFO - 开始登录流程（带重试），用户名: scf_4nuioc, 最大重试次数: 5
SCFLoginPage - INFO - 点击账号登录标签
pages.base_page - INFO - 等待元素 text=账号登录 状态: visible
pages.base_page - INFO - 点击元素: text=账号登录
SCFLoginPage - INFO - 输入用户名: scf_4nuioc
pages.base_page - INFO - 填充文本到元素 input[placeholder='请输入账号']: scf_4nuioc
SCFLoginPage - INFO - 输入密码
pages.base_page - INFO - 填充文本到元素 input[type='password']: Scf123456.
SCFLoginPage - INFO - 登录尝试 1/5
SCFLoginPage - INFO - 开始自动识别验证码，最大重试次数: 1, 最小置信度: 0.6
SCFLoginPage - INFO - 验证码识别尝试 1/1
pages.base_page - INFO - 等待元素 .arco-image img 状态: visible
SCFLoginPage - INFO - 找到验证码元素，使用选择器: .arco-image img
SCFLoginPage - INFO - 验证码图片已保存到: logs/captcha_debug_attempt_1.png
components.ocr_util - WARNING - 图像复杂度分析失败: name 'ImageFilter' is not defined
components.ocr_util - INFO - OCR识别结果: 3055, 置信度: 0.8
SCFLoginPage - INFO - 尝试 1 - OCR识别结果: '3055', 置信度: 0.800
SCFLoginPage - INFO - 验证码识别成功: '3055', 置信度: 0.800
SCFLoginPage - INFO - 输入图形验证码: 3055
pages.base_page - INFO - 填充文本到元素 input[placeholder='请输入图形验证码']: 3055
SCFLoginPage - INFO - 尝试 1 - 使用验证码: 3055
SCFLoginPage - INFO - 点击登录按钮
pages.base_page - INFO - 点击元素: button[type='submit']
SCFLoginPage - INFO - 检查登录状态...
SCFLoginPage - INFO - 已离开登录页面，当前URL: http://172.18.12.128:8080/system-register/system-register
SCFLoginPage - INFO - 登录成功！尝试次数: 1
SCFLoginPage - INFO - 登录操作完成
SCFLoginPage - INFO - 验证登录成功状态
pages.base_page - INFO - 等待页面加载状态: networkidle
SCFLoginPage - INFO - 登录成功验证通过
SCFRegistrationWorkflow - INFO - 系统登录成功
SCFRegistrationWorkflow - INFO - 进入备案申请
SCFRegistrationPage - INFO - 点击备案登记菜单
pages.base_page - INFO - 点击元素: text=备案登记
SCFRegistrationPage - INFO - 点击备案申请按钮
pages.base_page - INFO - 点击元素: role=button[name='备案申请']
SCFRegistrationWorkflow - INFO - 填写基础材料
SCFRegistrationPage - INFO - 批量上传文件，共 1 个文件
SCFRegistrationPage - WARNING - 上传区域不可见: bfmArtAssocId
SCFRegistrationPage - INFO - 点击下一步按钮
pages.base_page - INFO - 点击元素: role=button[name='下一步']
pages.base_page - INFO - 等待页面加载状态: networkidle
SCFRegistrationWorkflow - INFO - 填写业务合规信息
SCFRegistrationPage - INFO - 填写业务合规表单
SCFRegistrationPage - INFO - 在区域 bcVele 选择选项: 否
pages.base_page - INFO - 点击元素: #bcVele text=否
components.retry_util - WARNING - 函数 click 第 1 次尝试失败: Locator.wait_for: Unexpected token "=" while parsing css selector "#bcVele text=否". Did you mean to CSS.escape it?
Call log:
  - waiting for #bcVele text=否 to be visible

pages.base_page - INFO - 点击元素: #bcVele text=否
components.retry_util - WARNING - 函数 click 第 2 次尝试失败: Locator.wait_for: Unexpected token "=" while parsing css selector "#bcVele text=否". Did you mean to CSS.escape it?
Call log:
  - waiting for #bcVele text=否 to be visible

pages.base_page - INFO - 点击元素: #bcVele text=否
components.retry_util - WARNING - 函数 click 第 3 次尝试失败: Locator.wait_for: Unexpected token "=" while parsing css selector "#bcVele text=否". Did you mean to CSS.escape it?
Call log:
  - waiting for #bcVele text=否 to be visible

components.retry_util - ERROR - 函数 click 在 3 次尝试后仍然失败
SCFRegistrationWorkflow - ERROR - 备案申请流程执行失败: Locator.wait_for: Unexpected token "=" while parsing css selector "#bcVele text=否". Did you mean to CSS.escape it?
Call log:
  - waiting for #bcVele text=否 to be visible

SCFRegistrationWorkflow - INFO - 截图: 备案申请流程失败
fixtures.conftest - ERROR - 测试失败，已保存调试信息: test_minimal_registration_process[chromium]
