INFO     components.ocr_util:ocr_util.py:60 OCR引擎初始化成功
INFO     SCFLoginPage:scf_login_page.py:55 导航到登录页面: http://172.18.12.128:8080/login
INFO     pages.base_page:base_page.py:30 导航到页面: http://172.18.12.128:8080/login
INFO     SCFLoginPage:scf_login_page.py:62 点击账号登录标签
INFO     pages.base_page:base_page.py:190 等待元素 text=账号登录 状态: visible
DEBUG    components.retry_util:retry_util.py:87 执行函数 click，尝试 1/3
INFO     pages.base_page:base_page.py:106 点击元素: text=账号登录
DEBUG    pages.base_page:base_page.py:65 元素 text=账号登录 已稳定
INFO     SCFLoginPage:scf_login_page.py:397 刷新验证码
DEBUG    SCFLoginPage:scf_login_page.py:411 尝试点击刷新验证码: .arco-image
DEBUG    components.retry_util:retry_util.py:87 执行函数 click，尝试 1/3
INFO     pages.base_page:base_page.py:106 点击元素: .arco-image
DEBUG    pages.base_page:base_page.py:65 元素 .arco-image 已稳定
INFO     SCFLoginPage:scf_login_page.py:428 验证码刷新完成
INFO     SCFLoginPage:scf_login_page.py:132 输入图形验证码: 1234
DEBUG    components.retry_util:retry_util.py:87 执行函数 fill，尝试 1/3
INFO     pages.base_page:base_page.py:130 填充文本到元素 input[placeholder='请输入图形验证码']: 1234
DEBUG    pages.base_page:base_page.py:65 元素 input[placeholder='请输入图形验证码'] 已稳定