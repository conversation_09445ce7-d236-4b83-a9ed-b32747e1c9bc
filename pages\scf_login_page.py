"""
供应链金融备案系统登录页面对象
Supply Chain Finance (SCF) Login Page Object
"""

import allure
from typing import Optional
from pages.base_page import BasePage
from components.log_manager import get_logger
from components.ocr_util import get_ocr_handler

logger = get_logger(__name__)


class SCFLoginPage(BasePage):
    """供应链金融备案系统登录页面"""
    
    def __init__(self, page):
        """
        初始化登录页面
        
        Args:
            page: Playwright页面对象
        """
        super().__init__(page)
        self.logger = get_logger(self.__class__.__name__)
        self.ocr_handler = get_ocr_handler()
        
        # 页面元素定位符
        self.account_tab = "text=账号登录"
        self.username_input = "role=textbox[name='请输入账号']"
        self.password_input = "role=textbox[name='请输入密码']"
        self.captcha_input = "role=textbox[name='请输入图形验证码']"
        self.captcha_image = ".captcha-image"  # 需要根据实际页面调整
        self.login_button = "role=button[name='登录']"
        self.error_message = ".error-message"  # 需要根据实际页面调整
        
        # 页面URL
        self.login_url = "/login"
    
    @allure.step("导航到登录页面")
    def navigate_to_login(self, base_url: str):
        """
        导航到登录页面
        
        Args:
            base_url: 基础URL
        """
        full_url = f"{base_url.rstrip('/')}{self.login_url}"
        self.logger.info(f"导航到登录页面: {full_url}")
        self.navigate(full_url)
        return self
    
    @allure.step("点击账号登录标签")
    def click_account_login_tab(self):
        """点击账号登录标签"""
        self.logger.info("点击账号登录标签")
        self.click(self.account_tab)
        return self
    
    @allure.step("输入用户名: {username}")
    def input_username(self, username: str):
        """
        输入用户名
        
        Args:
            username: 用户名
        """
        self.logger.info(f"输入用户名: {username}")
        self.fill(self.username_input, username)
        return self
    
    @allure.step("输入密码")
    def input_password(self, password: str):
        """
        输入密码
        
        Args:
            password: 密码
        """
        self.logger.info("输入密码")
        # 密码不记录到日志中，保护敏感信息
        self.fill(self.password_input, password)
        return self
    
    @allure.step("输入验证码: {captcha}")
    def input_captcha(self, captcha: str):
        """
        输入验证码
        
        Args:
            captcha: 验证码
        """
        self.logger.info(f"输入验证码: {captcha}")
        self.fill(self.captcha_input, captcha)
        return self
    
    @allure.step("自动识别并输入验证码")
    def auto_input_captcha(self) -> str:
        """
        自动识别验证码并输入
        
        Returns:
            识别出的验证码文本
        """
        self.logger.info("开始自动识别验证码")
        
        try:
            # 等待验证码图片加载
            self.wait_for_element(self.captcha_image)
            
            # 截取验证码图片
            captcha_element = self.page.locator(self.captcha_image)
            captcha_screenshot = captcha_element.screenshot()
            
            # 使用OCR识别验证码
            ocr_result = self.ocr_handler.recognize_from_bytes(captcha_screenshot)
            
            if ocr_result.success and ocr_result.confidence >= 0.8:
                captcha_text = ocr_result.text.strip()
                self.logger.info(f"验证码识别成功: {captcha_text}, 置信度: {ocr_result.confidence}")
                
                # 输入识别的验证码
                self.input_captcha(captcha_text)
                return captcha_text
            else:
                self.logger.warning(f"验证码识别失败或置信度不足: {ocr_result}")
                raise Exception(f"验证码识别失败: {ocr_result.error_msg}")
                
        except Exception as e:
            self.logger.error(f"自动识别验证码失败: {e}")
            # 附加失败截图到报告
            self.take_screenshot_for_report("验证码识别失败")
            raise
    
    @allure.step("点击登录按钮")
    def click_login_button(self):
        """点击登录按钮"""
        self.logger.info("点击登录按钮")
        self.click(self.login_button)
        return self
    
    @allure.step("执行登录操作")
    def login(self, username: str, password: str, captcha: Optional[str] = None, auto_captcha: bool = True):
        """
        执行完整的登录操作
        
        Args:
            username: 用户名
            password: 密码
            captcha: 验证码（可选，如果不提供且auto_captcha为True则自动识别）
            auto_captcha: 是否自动识别验证码
        """
        self.logger.info(f"开始登录流程，用户名: {username}")
        
        # 点击账号登录标签
        self.click_account_login_tab()
        
        # 输入用户名
        self.input_username(username)
        
        # 输入密码
        self.input_password(password)
        
        # 处理验证码
        if captcha:
            self.input_captcha(captcha)
        elif auto_captcha:
            try:
                self.auto_input_captcha()
            except Exception as e:
                self.logger.warning(f"自动识别验证码失败，需要手动处理: {e}")
                # 可以在这里添加手动处理逻辑或抛出异常
                raise
        
        # 点击登录按钮
        self.click_login_button()
        
        self.logger.info("登录操作完成")
        return self
    
    @allure.step("验证登录成功")
    def verify_login_success(self, expected_url_pattern: str = None):
        """
        验证登录是否成功
        
        Args:
            expected_url_pattern: 期望的URL模式
        """
        self.logger.info("验证登录成功状态")
        
        # 等待页面跳转
        self.wait_for_load_state("networkidle")
        
        # 验证URL变化
        if expected_url_pattern:
            current_url = self.get_current_url()
            assert expected_url_pattern in current_url, f"登录后URL不符合预期: 期望包含 {expected_url_pattern}, 实际 {current_url}"
            self.logger.info(f"URL验证通过: {current_url}")
        
        # 验证是否还在登录页面（如果还在登录页面说明登录失败）
        current_url = self.get_current_url()
        assert self.login_url not in current_url, f"登录失败，仍在登录页面: {current_url}"
        
        self.logger.info("登录成功验证通过")
        return self
    
    @allure.step("验证登录失败")
    def verify_login_failure(self, expected_error: str = None):
        """
        验证登录失败
        
        Args:
            expected_error: 期望的错误消息
        """
        self.logger.info("验证登录失败状态")
        
        # 验证仍在登录页面
        current_url = self.get_current_url()
        assert self.login_url in current_url, f"应该仍在登录页面，但当前URL: {current_url}"
        
        # 验证错误消息（如果页面有错误消息显示）
        if expected_error and self.is_visible(self.error_message):
            error_text = self.get_text(self.error_message)
            assert expected_error in error_text, f"错误消息不符合预期: 期望包含 {expected_error}, 实际 {error_text}"
            self.logger.info(f"错误消息验证通过: {error_text}")
        
        self.logger.info("登录失败验证通过")
        return self
    
    @allure.step("刷新验证码")
    def refresh_captcha(self):
        """刷新验证码"""
        self.logger.info("刷新验证码")
        
        # 如果页面有刷新验证码的按钮，点击它
        refresh_button = ".captcha-refresh"  # 需要根据实际页面调整
        if self.is_visible(refresh_button):
            self.click(refresh_button)
        else:
            # 如果没有刷新按钮，可以点击验证码图片本身
            if self.is_visible(self.captcha_image):
                self.click(self.captcha_image)
        
        # 等待新验证码加载
        self.page.wait_for_timeout(1000)  # 等待1秒
        return self
    
    @allure.step("清空表单")
    def clear_form(self):
        """清空登录表单"""
        self.logger.info("清空登录表单")
        
        # 清空用户名
        if self.is_visible(self.username_input):
            self.page.locator(self.username_input).clear()
        
        # 清空密码
        if self.is_visible(self.password_input):
            self.page.locator(self.password_input).clear()
        
        # 清空验证码
        if self.is_visible(self.captcha_input):
            self.page.locator(self.captcha_input).clear()
        
        return self
    
    def is_login_page(self) -> bool:
        """
        判断当前是否在登录页面
        
        Returns:
            是否在登录页面
        """
        current_url = self.get_current_url()
        return self.login_url in current_url and self.is_visible(self.login_button)
