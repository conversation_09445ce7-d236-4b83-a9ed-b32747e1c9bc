"""
供应链金融备案系统登录页面对象
Supply Chain Finance (SCF) Login Page Object
"""

import allure
# 移除未使用的导入
from pages.base_page import BasePage
from components.log_manager import get_logger
from components.ocr_util import get_ocr_handler

logger = get_logger(__name__)


class SCFLoginPage(BasePage):
    """供应链金融备案系统登录页面"""
    
    def __init__(self, page):
        """
        初始化登录页面
        
        Args:
            page: Playwright页面对象
        """
        super().__init__(page)
        self.logger = get_logger(self.__class__.__name__)
        self.ocr_handler = get_ocr_handler()
        
        # 页面元素定位符 - 根据实际DOM结构更新
        self.account_tab = "text=账号登录"
        self.phone_tab = "text=手机号登录"  # 默认显示的标签
        self.username_input = "input[placeholder='请输入账号']"  # 账号登录模式
        self.phone_input = "input[placeholder='请输入手机号']"  # 手机号登录模式
        self.password_input = "input[type='password']"  # 密码输入框
        self.sms_code_input = "input[placeholder='请输入短信验证码']"  # 短信验证码
        self.captcha_input = "input[placeholder='请输入图形验证码']"  # 图形验证码输入框
        self.captcha_image = ".arco-image img"  # 验证码图片 - 更新为实际选择器
        self.get_sms_button = "button:has-text('获取验证码')"  # 获取短信验证码按钮
        self.login_button = "button[type='submit']"  # 登录按钮
        self.register_link = "text=立即注册"  # 注册链接
        self.error_message = ".arco-message"  # 错误消息容器
        
        # 页面URL
        self.login_url = "/login"
    
    @allure.step("导航到登录页面")
    def navigate_to_login(self, base_url: str):
        """
        导航到登录页面
        
        Args:
            base_url: 基础URL
        """
        full_url = f"{base_url.rstrip('/')}{self.login_url}"
        self.logger.info(f"导航到登录页面: {full_url}")
        self.navigate(full_url)
        return self
    
    @allure.step("点击账号登录标签")
    def click_account_login_tab(self):
        """点击账号登录标签"""
        self.logger.info("点击账号登录标签")
        try:
            # 等待标签可见并点击
            self.wait_for_element(self.account_tab, timeout=10000)
            self.click(self.account_tab)
            # 等待切换完成
            self.page.wait_for_timeout(1000)
        except Exception as e:
            self.logger.warning(f"点击账号登录标签失败: {e}")
            # 如果默认就是账号登录模式，可能不需要点击
        return self

    @allure.step("点击手机号登录标签")
    def click_phone_login_tab(self):
        """点击手机号登录标签"""
        self.logger.info("点击手机号登录标签")
        try:
            self.wait_for_element(self.phone_tab, timeout=10000)
            self.click(self.phone_tab)
            # 等待切换完成
            self.page.wait_for_timeout(1000)
        except Exception as e:
            self.logger.warning(f"点击手机号登录标签失败: {e}")
        return self
    
    @allure.step("输入用户名: {username}")
    def input_username(self, username: str):
        """
        输入用户名

        Args:
            username: 用户名
        """
        self.logger.info(f"输入用户名: {username}")
        self.fill(self.username_input, username)
        return self

    @allure.step("输入手机号: {phone}")
    def input_phone(self, phone: str):
        """
        输入手机号

        Args:
            phone: 手机号
        """
        self.logger.info(f"输入手机号: {phone}")
        self.fill(self.phone_input, phone)
        return self
    
    @allure.step("输入密码")
    def input_password(self, password: str):
        """
        输入密码
        
        Args:
            password: 密码
        """
        self.logger.info("输入密码")
        # 密码不记录到日志中，保护敏感信息
        self.fill(self.password_input, password)
        return self
    
    @allure.step("输入图形验证码: {captcha}")
    def input_captcha(self, captcha: str):
        """
        输入图形验证码

        Args:
            captcha: 图形验证码
        """
        self.logger.info(f"输入图形验证码: {captcha}")
        self.fill(self.captcha_input, captcha)
        return self

    @allure.step("输入短信验证码: {sms_code}")
    def input_sms_code(self, sms_code: str):
        """
        输入短信验证码

        Args:
            sms_code: 短信验证码
        """
        self.logger.info(f"输入短信验证码: {sms_code}")
        self.fill(self.sms_code_input, sms_code)
        return self

    @allure.step("点击获取短信验证码")
    def click_get_sms_code(self):
        """点击获取短信验证码按钮"""
        self.logger.info("点击获取短信验证码")
        self.click(self.get_sms_button)
        return self
    
    @allure.step("截取并保存验证码图片")
    def capture_captcha_image(self, attempt: int) -> bytes:
        """
        截取验证码图片并保存到调试目录

        Args:
            attempt: 当前尝试次数

        Returns:
            验证码图片的字节数据
        """
        # 等待验证码图片加载，使用更灵活的选择器
        captcha_selectors = [
            self.captcha_image,  # 主要选择器
            ".arco-image-img",   # 备用选择器1
            "img[src*='data:image']",  # 备用选择器2 - base64图片
            ".arco-image img"    # 备用选择器3
        ]

        captcha_element = None
        for selector in captcha_selectors:
            try:
                self.logger.debug(f"尝试选择器: {selector}")
                self.wait_for_element(selector, timeout=10000)
                captcha_element = self.page.locator(selector).first
                if captcha_element.is_visible():
                    self.logger.info(f"找到验证码元素，使用选择器: {selector}")
                    break
            except Exception as selector_error:
                self.logger.debug(f"选择器 {selector} 失败: {selector_error}")
                continue

        if not captcha_element:
            raise Exception("无法找到验证码图片元素")

        # 等待图片完全加载
        self.page.wait_for_timeout(1000)

        # 截取验证码图片
        captcha_screenshot = captcha_element.screenshot()

        # 保存验证码图片用于调试
        debug_path = f"logs/captcha_debug_attempt_{attempt}.png"
        with open(debug_path, "wb") as f:
            f.write(captcha_screenshot)
        self.logger.info(f"验证码图片已保存到: {debug_path}")

        # 同时附加到 Allure 报告
        allure.attach(captcha_screenshot, name=f"验证码图片_尝试{attempt}", attachment_type=allure.attachment_type.PNG)

        return captcha_screenshot

    @allure.step("OCR识别验证码")
    def recognize_captcha(self, captcha_image: bytes, attempt: int) -> tuple[str, float]:
        """
        使用 ddddocr 库进行 OCR 识别

        Args:
            captcha_image: 验证码图片字节数据
            attempt: 当前尝试次数

        Returns:
            tuple: (识别文本, 置信度)
        """
        try:
            # 使用OCR识别验证码
            ocr_result = self.ocr_handler.recognize_from_bytes(captcha_image)

            if ocr_result.success:
                captcha_text = ocr_result.text.strip()
                confidence = getattr(ocr_result, 'confidence', 0.0)

                self.logger.info(f"尝试 {attempt} - OCR识别结果: '{captcha_text}', 置信度: {confidence:.3f}")

                # 验证识别结果的合理性
                if len(captcha_text) < 3:
                    self.logger.warning(f"识别结果过短: '{captcha_text}', 长度: {len(captcha_text)}")
                elif len(captcha_text) > 8:
                    self.logger.warning(f"识别结果过长: '{captcha_text}', 长度: {len(captcha_text)}")

                return captcha_text, confidence
            else:
                error_msg = getattr(ocr_result, 'error_msg', '未知错误')
                self.logger.error(f"OCR识别失败: {error_msg}")
                return "", 0.0

        except Exception as e:
            self.logger.error(f"OCR识别过程中出现异常: {e}")
            return "", 0.0

    @allure.step("自动识别并输入验证码")
    def auto_input_captcha(self, max_retries: int = 3, min_confidence: float = 0.6) -> str:
        """
        自动识别验证码并输入

        Args:
            max_retries: 最大重试次数
            min_confidence: 最小置信度要求

        Returns:
            识别出的验证码文本
        """
        self.logger.info(f"开始自动识别验证码，最大重试次数: {max_retries}, 最小置信度: {min_confidence}")

        for attempt in range(1, max_retries + 1):
            try:
                self.logger.info(f"验证码识别尝试 {attempt}/{max_retries}")

                # 1. 截取验证码图片并保存到调试目录
                captcha_image = self.capture_captcha_image(attempt)

                # 2. 使用 ddddocr 库进行 OCR 识别
                captcha_text, confidence = self.recognize_captcha(captcha_image, attempt)

                # 3. 验证识别结果
                if captcha_text and (confidence >= min_confidence or len(captcha_text) >= 4):
                    self.logger.info(f"验证码识别成功: '{captcha_text}', 置信度: {confidence:.3f}")

                    # 4. 将识别结果输入到验证码输入框
                    self.input_captcha(captcha_text)

                    return captcha_text
                else:
                    self.logger.warning(f"验证码识别质量不足 - 文本: '{captcha_text}', 置信度: {confidence:.3f}")

                    if attempt < max_retries:
                        self.logger.info(f"准备进行第 {attempt + 1} 次尝试，先刷新验证码")
                        self.refresh_captcha()
                        continue

            except Exception as e:
                self.logger.error(f"验证码识别尝试 {attempt} 失败: {e}")

                # 保存失败截图到 Allure 报告
                try:
                    screenshot = self.take_screenshot()
                    allure.attach(screenshot, name=f"验证码识别失败_尝试{attempt}", attachment_type=allure.attachment_type.PNG)
                except Exception as screenshot_error:
                    self.logger.warning(f"保存失败截图时出错: {screenshot_error}")

                if attempt < max_retries:
                    self.logger.info(f"准备进行第 {attempt + 1} 次尝试，先刷新验证码")
                    try:
                        self.refresh_captcha()
                    except Exception as refresh_error:
                        self.logger.warning(f"刷新验证码失败: {refresh_error}")
                    continue

        # 所有尝试都失败了
        error_msg = f"验证码识别失败，已尝试 {max_retries} 次，均未达到要求的置信度 {min_confidence}"
        self.logger.error(error_msg)

        # 保存最终失败截图
        try:
            screenshot = self.take_screenshot()
            allure.attach(screenshot, name="验证码识别最终失败", attachment_type=allure.attachment_type.PNG)
        except Exception as screenshot_error:
            self.logger.warning(f"保存最终失败截图时出错: {screenshot_error}")

        raise Exception(error_msg)
    
    @allure.step("点击登录按钮")
    def click_login_button(self):
        """点击登录按钮"""
        self.logger.info("点击登录按钮")
        self.click(self.login_button)
        return self
    
    @allure.step("检查登录状态")
    def check_login_status(self, timeout: int = 5000) -> bool:
        """
        检查登录是否成功

        Args:
            timeout: 等待超时时间（毫秒）

        Returns:
            bool: True表示登录成功，False表示仍在登录页面
        """
        try:
            # 等待页面状态稳定
            self.page.wait_for_timeout(1000)

            # 检查当前URL是否还在登录页面
            current_url = self.get_current_url()
            is_still_login_page = self.login_url in current_url

            if is_still_login_page:
                self.logger.info(f"仍在登录页面: {current_url}")
                return False
            else:
                self.logger.info(f"已离开登录页面，当前URL: {current_url}")
                return True

        except Exception as e:
            self.logger.warning(f"检查登录状态时出错: {e}")
            return False

    @allure.step("执行登录操作（带重试机制）")
    def login_with_retry(self, username: str, password: str, max_captcha_retries: int = 5) -> bool:
        """
        执行完整的登录操作，包含验证码重试机制

        Args:
            username: 用户名
            password: 密码
            max_captcha_retries: 验证码最大重试次数

        Returns:
            bool: 登录是否成功
        """
        self.logger.info(f"开始登录流程（带重试），用户名: {username}, 最大重试次数: {max_captcha_retries}")

        # 点击账号登录标签
        self.click_account_login_tab()

        # 输入用户名和密码（只需要输入一次）
        self.input_username(username)
        self.input_password(password)

        # 循环重试逻辑：刷新验证码 → 识别验证码 → 输入验证码 → 点击登录 → 验证登录状态
        for attempt in range(1, max_captcha_retries + 1):
            try:
                self.logger.info(f"登录尝试 {attempt}/{max_captcha_retries}")

                # 1. 如果不是第一次尝试，先刷新验证码
                if attempt > 1:
                    self.logger.info("刷新验证码后重新尝试")
                    self.refresh_captcha()

                # 2. 识别验证码并输入
                captcha_text = self.auto_input_captcha(max_retries=1)  # 单次识别，重试在外层循环
                self.logger.info(f"尝试 {attempt} - 使用验证码: {captcha_text}")

                # 3. 点击登录按钮
                self.click_login_button()

                # 4. 验证登录状态
                self.logger.info("检查登录状态...")
                if self.check_login_status():
                    self.logger.info(f"登录成功！尝试次数: {attempt}")
                    return True
                else:
                    self.logger.warning(f"登录失败，尝试 {attempt}/{max_captcha_retries}")

                    if attempt < max_captcha_retries:
                        self.logger.info("准备下一次登录尝试")
                        continue
                    else:
                        self.logger.error(f"登录失败，已达到最大重试次数 {max_captcha_retries}")
                        break

            except Exception as e:
                self.logger.error(f"登录尝试 {attempt} 出现异常: {e}")

                # 保存失败截图
                try:
                    screenshot = self.take_screenshot()
                    allure.attach(screenshot, name=f"登录失败_尝试{attempt}", attachment_type=allure.attachment_type.PNG)
                except Exception as screenshot_error:
                    self.logger.warning(f"保存登录失败截图时出错: {screenshot_error}")

                if attempt < max_captcha_retries:
                    self.logger.info(f"准备第 {attempt + 1} 次登录尝试")
                    continue
                else:
                    self.logger.error(f"登录过程出现异常，已达到最大重试次数: {e}")
                    raise Exception(f"登录失败，已尝试 {max_captcha_retries} 次: {e}")

        # 所有尝试都失败
        error_msg = f"登录失败，已尝试 {max_captcha_retries} 次，均未成功"
        self.logger.error(error_msg)

        # 保存最终失败截图
        try:
            screenshot = self.take_screenshot()
            allure.attach(screenshot, name="登录最终失败", attachment_type=allure.attachment_type.PNG)
        except Exception as screenshot_error:
            self.logger.warning(f"保存最终失败截图时出错: {screenshot_error}")

        raise Exception(error_msg)

    @allure.step("执行登录操作")
    def login(self, username: str, password: str, captcha: str | None = None, auto_captcha: bool = True):
        """
        执行完整的登录操作（兼容原有接口）

        Args:
            username: 用户名
            password: 密码
            captcha: 验证码（可选，如果不提供且auto_captcha为True则自动识别）
            auto_captcha: 是否自动识别验证码
        """
        self.logger.info(f"开始登录流程，用户名: {username}")

        if captcha:
            # 如果提供了验证码，使用传统方式登录
            self.click_account_login_tab()
            self.input_username(username)
            self.input_password(password)
            self.input_captcha(captcha)
            self.click_login_button()
        elif auto_captcha:
            # 使用带重试机制的登录
            self.login_with_retry(username, password)
        else:
            # 不使用验证码的登录（可能用于特殊场景）
            self.click_account_login_tab()
            self.input_username(username)
            self.input_password(password)
            self.click_login_button()

        self.logger.info("登录操作完成")
        return self

    @allure.step("手机号登录")
    def phone_login(self, phone: str, sms_code: str = None, captcha: str = None, auto_captcha: bool = True):
        """
        执行手机号登录操作

        Args:
            phone: 手机号
            sms_code: 短信验证码（可选）
            captcha: 图形验证码（可选，如果不提供且auto_captcha为True则自动识别）
            auto_captcha: 是否自动识别图形验证码
        """
        self.logger.info(f"开始手机号登录流程，手机号: {phone}")

        # 确保在手机号登录标签页
        self.click_phone_login_tab()

        # 输入手机号
        self.input_phone(phone)

        # 处理图形验证码
        if captcha:
            self.input_captcha(captcha)
        elif auto_captcha:
            try:
                self.auto_input_captcha()
            except Exception as e:
                self.logger.warning(f"自动识别图形验证码失败: {e}")
                # 可以选择继续或抛出异常
                raise

        # 获取短信验证码（如果需要）
        if not sms_code:
            self.click_get_sms_code()
            self.logger.info("已点击获取短信验证码，请手动输入收到的验证码")
            # 这里可以添加等待用户输入的逻辑
            raise Exception("需要手动输入短信验证码")
        else:
            self.input_sms_code(sms_code)

        # 点击登录按钮
        self.click_login_button()

        self.logger.info("手机号登录操作完成")
        return self
    
    @allure.step("验证登录成功")
    def verify_login_success(self, expected_url_pattern: str = None):
        """
        验证登录是否成功
        
        Args:
            expected_url_pattern: 期望的URL模式
        """
        self.logger.info("验证登录成功状态")
        
        # 等待页面跳转
        self.wait_for_load_state("networkidle")
        
        # 验证URL变化
        if expected_url_pattern:
            current_url = self.get_current_url()
            assert expected_url_pattern in current_url, f"登录后URL不符合预期: 期望包含 {expected_url_pattern}, 实际 {current_url}"
            self.logger.info(f"URL验证通过: {current_url}")
        
        # 验证是否还在登录页面（如果还在登录页面说明登录失败）
        current_url = self.get_current_url()
        assert self.login_url not in current_url, f"登录失败，仍在登录页面: {current_url}"
        
        self.logger.info("登录成功验证通过")
        return self
    
    @allure.step("验证登录失败")
    def verify_login_failure(self, expected_error: str = None):
        """
        验证登录失败
        
        Args:
            expected_error: 期望的错误消息
        """
        self.logger.info("验证登录失败状态")
        
        # 验证仍在登录页面
        current_url = self.get_current_url()
        assert self.login_url in current_url, f"应该仍在登录页面，但当前URL: {current_url}"
        
        # 验证错误消息（如果页面有错误消息显示）
        if expected_error and self.is_visible(self.error_message):
            error_text = self.get_text(self.error_message)
            assert expected_error in error_text, f"错误消息不符合预期: 期望包含 {expected_error}, 实际 {error_text}"
            self.logger.info(f"错误消息验证通过: {error_text}")
        
        self.logger.info("登录失败验证通过")
        return self
    
    @allure.step("刷新验证码")
    def refresh_captcha(self):
        """
        刷新验证码
        使用指定的选择器点击刷新验证码
        """
        self.logger.info("开始刷新验证码")

        try:
            # 使用指定的选择器刷新验证码
            # page.locator("#kaptchaCode").get_by_role("img").nth(1).click()
            refresh_selector = "#kaptchaCode"

            # 等待验证码容器可见
            self.wait_for_element(refresh_selector, timeout=10000)

            # 获取验证码容器中的第二个图片元素（索引为1）
            captcha_container = self.page.locator(refresh_selector)
            refresh_element = captcha_container.get_by_role("img").nth(1)

            # 检查元素是否可见
            if refresh_element.is_visible():
                self.logger.info("找到验证码刷新元素，准备点击")
                refresh_element.click()
                self.logger.info("已点击验证码刷新元素")
            else:
                # 备用方案：点击验证码图片本身
                self.logger.warning("指定的刷新元素不可见，尝试备用方案")
                backup_selectors = [
                    "#kaptchaCode img",  # 验证码容器中的图片
                    ".arco-image",       # 验证码图片容器
                    self.captcha_image   # 验证码图片
                ]

                refreshed = False
                for selector in backup_selectors:
                    try:
                        if self.is_visible(selector):
                            self.logger.debug(f"尝试备用刷新方法: {selector}")
                            self.click(selector)
                            refreshed = True
                            break
                    except Exception as e:
                        self.logger.debug(f"备用刷新方法 {selector} 失败: {e}")
                        continue

                if not refreshed:
                    raise Exception("所有验证码刷新方法都失败")

            # 等待新验证码加载完成
            self.logger.info("等待新验证码加载...")
            self.page.wait_for_timeout(2000)  # 等待2秒确保新验证码加载

            # 验证新验证码是否加载成功
            try:
                self.wait_for_element(self.captcha_image, timeout=5000)
                self.logger.info("验证码刷新完成，新验证码已加载")
            except Exception as e:
                self.logger.warning(f"等待新验证码加载时出现问题: {e}")

        except Exception as e:
            self.logger.error(f"刷新验证码失败: {e}")
            # 保存失败截图
            try:
                screenshot = self.take_screenshot()
                allure.attach(screenshot, name="验证码刷新失败", attachment_type=allure.attachment_type.PNG)
            except Exception as screenshot_error:
                self.logger.warning(f"保存刷新失败截图时出错: {screenshot_error}")
            raise Exception(f"验证码刷新失败: {e}")

        return self
    
    @allure.step("清空表单")
    def clear_form(self):
        """清空登录表单"""
        self.logger.info("清空登录表单")
        
        # 清空用户名
        if self.is_visible(self.username_input):
            self.page.locator(self.username_input).clear()
        
        # 清空密码
        if self.is_visible(self.password_input):
            self.page.locator(self.password_input).clear()
        
        # 清空验证码
        if self.is_visible(self.captcha_input):
            self.page.locator(self.captcha_input).clear()
        
        return self
    
    def is_login_page(self) -> bool:
        """
        判断当前是否在登录页面
        
        Returns:
            是否在登录页面
        """
        current_url = self.get_current_url()
        return self.login_url in current_url and self.is_visible(self.login_button)
