#!/usr/bin/env python3
"""
SCF 主页面对象
包含备案登记入口等功能
"""

import allure
from playwright.sync_api import Page
from pages.base_page import BasePage
from components.log_manager import get_logger


class SCFMainPage(BasePage):
    """SCF 主页面对象类"""
    
    def __init__(self, page: Page):
        super().__init__(page)
        self.logger = get_logger(__name__)
        
        # 页面元素定位符
        self.filing_registration_menu = "text=备案登记"
        self.filing_application_button = "role=button[name='备案申请']"
        self.filing_query_button = "role=button[name='备案查询']"
        self.filing_management_button = "role=button[name='备案管理']"
        
        # 导航菜单
        self.main_menu = ".main-menu"
        self.user_info = ".user-info"
        self.logout_button = "text=退出登录"
        
    @allure.step("点击备案登记菜单")
    def click_filing_registration_menu(self):
        """点击备案登记菜单"""
        self.logger.info("点击备案登记菜单")
        self.click(self.filing_registration_menu)
        return self
    
    @allure.step("点击备案申请按钮")
    def click_filing_application_button(self):
        """点击备案申请按钮"""
        self.logger.info("点击备案申请按钮")
        self.click(self.filing_application_button)
        return self
    
    @allure.step("点击备案查询按钮")
    def click_filing_query_button(self):
        """点击备案查询按钮"""
        self.logger.info("点击备案查询按钮")
        self.click(self.filing_query_button)
        return self
    
    @allure.step("点击备案管理按钮")
    def click_filing_management_button(self):
        """点击备案管理按钮"""
        self.logger.info("点击备案管理按钮")
        self.click(self.filing_management_button)
        return self
    
    @allure.step("验证主页面加载完成")
    def verify_main_page_loaded(self):
        """验证主页面是否加载完成"""
        self.logger.info("验证主页面加载完成")
        
        # 等待关键元素可见
        self.wait_for_element(self.filing_registration_menu)
        
        # 验证主要功能按钮存在
        assert self.is_visible(self.filing_application_button), "备案申请按钮不可见"
        
        self.logger.info("主页面加载验证完成")
        return self
    
    @allure.step("导航到备案申请页面")
    def navigate_to_filing_application(self):
        """导航到备案申请页面"""
        self.logger.info("导航到备案申请页面")
        
        # 点击备案登记菜单
        self.click_filing_registration_menu()
        
        # 点击备案申请按钮
        self.click_filing_application_button()
        
        # 等待页面跳转
        self.wait_for_load_state("networkidle")
        
        self.logger.info("成功导航到备案申请页面")
        return self
    
    @allure.step("退出登录")
    def logout(self):
        """退出登录"""
        self.logger.info("执行退出登录")
        
        try:
            if self.is_visible(self.logout_button):
                self.click(self.logout_button)
                self.wait_for_load_state("networkidle")
                self.logger.info("退出登录成功")
            else:
                self.logger.warning("未找到退出登录按钮")
        except Exception as e:
            self.logger.error(f"退出登录失败: {e}")
            raise
        
        return self
