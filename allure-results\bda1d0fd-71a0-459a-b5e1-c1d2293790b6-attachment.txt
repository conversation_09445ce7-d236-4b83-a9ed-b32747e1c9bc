[35mDEBUG   [0m faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.address`.
[35mDEBUG   [0m faker.factory:factory.py:97 Provider `faker.providers.address` has been localized to `en_US`.
[35mDEBUG   [0m faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.automotive`.
[35mDEBUG   [0m faker.factory:factory.py:97 Provider `faker.providers.automotive` has been localized to `en_US`.
[35mDEBUG   [0m faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.bank`.
[35mDEBUG   [0m faker.factory:factory.py:88 Specified locale `en_US` is not available for provider `faker.providers.bank`. Locale reset to `en_GB` for this provider.
[35mDEBUG   [0m faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.barcode`.
[35mDEBUG   [0m faker.factory:factory.py:97 Provider `faker.providers.barcode` has been localized to `en_US`.
[35mDEBUG   [0m faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.color`.
[35mDEBUG   [0m faker.factory:factory.py:97 Provider `faker.providers.color` has been localized to `en_US`.
[35mDEBUG   [0m faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.company`.
[35mDEBUG   [0m faker.factory:factory.py:97 Provider `faker.providers.company` has been localized to `en_US`.
[35mDEBUG   [0m faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.credit_card`.
[35mDEBUG   [0m faker.factory:factory.py:97 Provider `faker.providers.credit_card` has been localized to `en_US`.
[35mDEBUG   [0m faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.currency`.
[35mDEBUG   [0m faker.factory:factory.py:97 Provider `faker.providers.currency` has been localized to `en_US`.
[35mDEBUG   [0m faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.date_time`.
[35mDEBUG   [0m faker.factory:factory.py:97 Provider `faker.providers.date_time` has been localized to `en_US`.
[35mDEBUG   [0m faker.factory:factory.py:108 Provider `faker.providers.doi` does not feature localization. Specified locale `en_US` is not utilized for this provider.
[35mDEBUG   [0m faker.factory:factory.py:108 Provider `faker.providers.emoji` does not feature localization. Specified locale `en_US` is not utilized for this provider.
[35mDEBUG   [0m faker.factory:factory.py:108 Provider `faker.providers.file` does not feature localization. Specified locale `en_US` is not utilized for this provider.
[35mDEBUG   [0m faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.geo`.
[35mDEBUG   [0m faker.factory:factory.py:97 Provider `faker.providers.geo` has been localized to `en_US`.
[35mDEBUG   [0m faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.internet`.
[35mDEBUG   [0m faker.factory:factory.py:97 Provider `faker.providers.internet` has been localized to `en_US`.
[35mDEBUG   [0m faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.isbn`.
[35mDEBUG   [0m faker.factory:factory.py:97 Provider `faker.providers.isbn` has been localized to `en_US`.
[35mDEBUG   [0m faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.job`.
[35mDEBUG   [0m faker.factory:factory.py:97 Provider `faker.providers.job` has been localized to `en_US`.
[35mDEBUG   [0m faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.lorem`.
[35mDEBUG   [0m faker.factory:factory.py:97 Provider `faker.providers.lorem` has been localized to `en_US`.
[35mDEBUG   [0m faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.misc`.
[35mDEBUG   [0m faker.factory:factory.py:97 Provider `faker.providers.misc` has been localized to `en_US`.
[35mDEBUG   [0m faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.passport`.
[35mDEBUG   [0m faker.factory:factory.py:97 Provider `faker.providers.passport` has been localized to `en_US`.
[35mDEBUG   [0m faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.person`.
[35mDEBUG   [0m faker.factory:factory.py:97 Provider `faker.providers.person` has been localized to `en_US`.
[35mDEBUG   [0m faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.phone_number`.
[35mDEBUG   [0m faker.factory:factory.py:97 Provider `faker.providers.phone_number` has been localized to `en_US`.
[35mDEBUG   [0m faker.factory:factory.py:108 Provider `faker.providers.profile` does not feature localization. Specified locale `en_US` is not utilized for this provider.
[35mDEBUG   [0m faker.factory:factory.py:108 Provider `faker.providers.python` does not feature localization. Specified locale `en_US` is not utilized for this provider.
[35mDEBUG   [0m faker.factory:factory.py:108 Provider `faker.providers.sbn` does not feature localization. Specified locale `en_US` is not utilized for this provider.
[35mDEBUG   [0m faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.ssn`.
[35mDEBUG   [0m faker.factory:factory.py:97 Provider `faker.providers.ssn` has been localized to `en_US`.
[35mDEBUG   [0m faker.factory:factory.py:108 Provider `faker.providers.user_agent` does not feature localization. Specified locale `en_US` is not utilized for this provider.
[32mINFO    [0m fixtures.scf_fixtures:scf_fixtures.py:32 加载SCF系统配置成功
[35mDEBUG   [0m asyncio:proactor_events.py:634 Using proactor: IocpProactor
[32mINFO    [0m components.ocr_util:ocr_util.py:60 OCR引擎初始化成功
[32mINFO    [0m SCFLoginPage:scf_login_page.py:55 导航到登录页面: http://172.18.12.128:8080/login
[32mINFO    [0m pages.base_page:base_page.py:30 导航到页面: http://172.18.12.128:8080/login
[32mINFO    [0m SCFLoginPage:scf_login_page.py:62 点击账号登录标签
[32mINFO    [0m pages.base_page:base_page.py:190 等待元素 text=账号登录 状态: visible
[35mDEBUG   [0m components.retry_util:retry_util.py:87 执行函数 click，尝试 1/3
[32mINFO    [0m pages.base_page:base_page.py:106 点击元素: text=账号登录
[35mDEBUG   [0m pages.base_page:base_page.py:65 元素 text=账号登录 已稳定
[32mINFO    [0m SCFLoginPage:scf_login_page.py:570 开始刷新验证码
[32mINFO    [0m pages.base_page:base_page.py:190 等待元素 #kaptchaCode 状态: visible
[32mINFO    [0m SCFLoginPage:scf_login_page.py:586 找到验证码刷新元素，准备点击
[32mINFO    [0m SCFLoginPage:scf_login_page.py:588 已点击验证码刷新元素
[32mINFO    [0m SCFLoginPage:scf_login_page.py:614 等待新验证码加载...
[32mINFO    [0m pages.base_page:base_page.py:190 等待元素 .arco-image img 状态: visible
[32mINFO    [0m SCFLoginPage:scf_login_page.py:620 验证码刷新完成，新验证码已加载
[32mINFO    [0m SCFLoginPage:scf_login_page.py:132 输入图形验证码: 1234
[35mDEBUG   [0m components.retry_util:retry_util.py:87 执行函数 fill，尝试 1/3
[32mINFO    [0m pages.base_page:base_page.py:130 填充文本到元素 input[placeholder='请输入图形验证码']: 1234
[35mDEBUG   [0m pages.base_page:base_page.py:65 元素 input[placeholder='请输入图形验证码'] 已稳定