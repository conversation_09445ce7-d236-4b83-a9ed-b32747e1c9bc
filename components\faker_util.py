"""
Faker 数据工厂封装
提供统一的测试数据生成接口，支持从模板生成数据
"""

from faker import Faker
from typing import Dict, Any, Optional
import json
import random
from pathlib import Path


class FakerFactory:
    """Faker 数据工厂类"""
    
    def __init__(self, locale: str = 'zh_CN'):
        """
        初始化 Faker 工厂
        
        Args:
            locale: 本地化设置，默认为中文
        """
        self.fake = Faker(locale)
        Faker.seed(0)  # 设置种子以确保可重现性
    
    def generate_user_data(self, template: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        生成用户数据
        
        Args:
            template: 数据模板，可以指定特定字段的值
            
        Returns:
            包含用户信息的字典
        """
        base_data = {
            'username': self.fake.user_name(),
            'email': self.fake.email(),
            'password': self.fake.password(length=12),
            'first_name': self.fake.first_name(),
            'last_name': self.fake.last_name(),
            'phone': self.fake.phone_number(),
            'address': self.fake.address(),
            'company': self.fake.company(),
            'job_title': self.fake.job(),
            'birth_date': self.fake.date_of_birth().strftime('%Y-%m-%d'),
            'id_card': self.fake.ssn(),
        }
        
        if template:
            base_data.update(template)
            
        return base_data
    
    def generate_product_data(self, template: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        生成商品数据
        
        Args:
            template: 数据模板
            
        Returns:
            包含商品信息的字典
        """
        base_data = {
            'name': self.fake.catch_phrase(),
            'description': self.fake.text(max_nb_chars=200),
            'price': round(random.uniform(10.0, 1000.0), 2),
            'category': random.choice(['电子产品', '服装', '食品', '图书', '家居']),
            'brand': self.fake.company(),
            'sku': self.fake.ean13(),
            'stock': random.randint(0, 100),
            'weight': round(random.uniform(0.1, 10.0), 2),
            'dimensions': f"{random.randint(10, 100)}x{random.randint(10, 100)}x{random.randint(10, 100)}cm"
        }
        
        if template:
            base_data.update(template)
            
        return base_data
    
    def generate_order_data(self, template: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        生成订单数据
        
        Args:
            template: 数据模板
            
        Returns:
            包含订单信息的字典
        """
        base_data = {
            'order_id': self.fake.uuid4(),
            'customer_name': self.fake.name(),
            'customer_email': self.fake.email(),
            'customer_phone': self.fake.phone_number(),
            'shipping_address': self.fake.address(),
            'billing_address': self.fake.address(),
            'total_amount': round(random.uniform(50.0, 5000.0), 2),
            'status': random.choice(['pending', 'processing', 'shipped', 'delivered', 'cancelled']),
            'payment_method': random.choice(['credit_card', 'debit_card', 'paypal', 'alipay', 'wechat_pay']),
            'order_date': self.fake.date_time_this_year().strftime('%Y-%m-%d %H:%M:%S'),
            'notes': self.fake.text(max_nb_chars=100)
        }
        
        if template:
            base_data.update(template)
            
        return base_data
    
    def generate_from_template_file(self, template_path: str) -> Dict[str, Any]:
        """
        从模板文件生成数据
        
        Args:
            template_path: 模板文件路径（JSON格式）
            
        Returns:
            生成的数据字典
        """
        template_file = Path(template_path)
        if not template_file.exists():
            raise FileNotFoundError(f"Template file not found: {template_path}")
        
        with open(template_file, 'r', encoding='utf-8') as f:
            template = json.load(f)
        
        # 根据模板类型生成数据
        data_type = template.get('type', 'user')
        template_data = template.get('data', {})
        
        if data_type == 'user':
            return self.generate_user_data(template_data)
        elif data_type == 'product':
            return self.generate_product_data(template_data)
        elif data_type == 'order':
            return self.generate_order_data(template_data)
        else:
            raise ValueError(f"Unsupported template type: {data_type}")
    
    def generate_batch_data(self, data_type: str, count: int, template: Optional[Dict[str, Any]] = None) -> list:
        """
        批量生成数据
        
        Args:
            data_type: 数据类型 ('user', 'product', 'order')
            count: 生成数量
            template: 数据模板
            
        Returns:
            数据列表
        """
        data_list = []
        
        for _ in range(count):
            if data_type == 'user':
                data_list.append(self.generate_user_data(template))
            elif data_type == 'product':
                data_list.append(self.generate_product_data(template))
            elif data_type == 'order':
                data_list.append(self.generate_order_data(template))
            else:
                raise ValueError(f"Unsupported data type: {data_type}")
        
        return data_list
    
    def generate_chinese_name(self) -> str:
        """生成中文姓名"""
        return self.fake.name()
    
    def generate_chinese_phone(self) -> str:
        """生成中国手机号"""
        return self.fake.phone_number()
    
    def generate_chinese_id_card(self) -> str:
        """生成中国身份证号"""
        return self.fake.ssn()
    
    def generate_email_with_domain(self, domain: str) -> str:
        """
        生成指定域名的邮箱
        
        Args:
            domain: 邮箱域名
            
        Returns:
            邮箱地址
        """
        username = self.fake.user_name()
        return f"{username}@{domain}"


# 全局实例
faker_factory = FakerFactory()


def get_faker_factory(locale: str = 'zh_CN') -> FakerFactory:
    """
    获取 Faker 工厂实例
    
    Args:
        locale: 本地化设置
        
    Returns:
        FakerFactory 实例
    """
    return FakerFactory(locale)
