{"name": "test_login_failure_scenarios[chromium-invalid_user-\\u5e94\\u8be5\\u663e\\u793a\\u9519\\u8bef\\u4fe1\\u606f]", "status": "failed", "statusDetails": {"message": "AssertionError: 应该显示错误信息，但当前URL: about:blank\nassert '/login' in 'about:blank'", "trace": "test_scf_login.py:100: in test_login_failure_scenarios\n    assert \"/login\" in current_url, f\"{expected_behavior}，但当前URL: {current_url}\"\nE   AssertionError: 应该显示错误信息，但当前URL: about:blank\nE   assert '/login' in 'about:blank'"}, "description": "测试登录失败场景", "steps": [{"name": "使用invalid_user凭证登录", "status": "passed", "steps": [{"name": "登录系统", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: Page.goto: net::ERR_CONNECTION_TIMED_OUT at http://*************:8080/login\nCall log:\n  - navigating to \"http://*************:8080/login\", waiting until \"domcontentloaded\"\n\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\workflows\\scf_registration_workflow.py\", line 113, in _login_to_system\n    self.login_page.navigate_to_login(base_url)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 51, in navigate_to_login\n    self.navigate(full_url)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 31, in navigate\n    self.page.goto(url, wait_until=wait_until, timeout=self.default_timeout)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 9002, in goto\n    self._sync(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 115, in _sync\n    return task.result()\n           ^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_page.py\", line 556, in goto\n    return await self._main_frame.goto(**locals_to_params(locals()))\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py\", line 146, in goto\n    await self._channel.send(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 69, in send\n    return await self._connection.wrap_api_call(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 558, in wrap_api_call\n    raise rewrite_error(error, f\"{parsed_st['apiName']}: {error}\") from None\n"}, "steps": [{"name": "导航到登录页面", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: Page.goto: net::ERR_CONNECTION_TIMED_OUT at http://*************:8080/login\nCall log:\n  - navigating to \"http://*************:8080/login\", waiting until \"domcontentloaded\"\n\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 51, in navigate_to_login\n    self.navigate(full_url)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 31, in navigate\n    self.page.goto(url, wait_until=wait_until, timeout=self.default_timeout)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 9002, in goto\n    self._sync(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 115, in _sync\n    return task.result()\n           ^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_page.py\", line 556, in goto\n    return await self._main_frame.goto(**locals_to_params(locals()))\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py\", line 146, in goto\n    await self._channel.send(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 69, in send\n    return await self._connection.wrap_api_call(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 558, in wrap_api_call\n    raise rewrite_error(error, f\"{parsed_st['apiName']}: {error}\") from None\n"}, "steps": [{"name": "导航到页面: 'http://*************:8080/login'", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: Page.goto: net::ERR_CONNECTION_TIMED_OUT at http://*************:8080/login\nCall log:\n  - navigating to \"http://*************:8080/login\", waiting until \"domcontentloaded\"\n\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 31, in navigate\n    self.page.goto(url, wait_until=wait_until, timeout=self.default_timeout)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 9002, in goto\n    self._sync(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 115, in _sync\n    return task.result()\n           ^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_page.py\", line 556, in goto\n    return await self._main_frame.goto(**locals_to_params(locals()))\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py\", line 146, in goto\n    await self._channel.send(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 69, in send\n    return await self._connection.wrap_api_call(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 558, in wrap_api_call\n    raise rewrite_error(error, f\"{parsed_st['apiName']}: {error}\") from None\n"}, "parameters": [{"name": "url", "value": "'http://*************:8080/login'"}, {"name": "wait_until", "value": "'domcontentloaded'"}], "start": 1751851532584, "stop": 1751851553651}], "parameters": [{"name": "base_url", "value": "'http://*************:8080'"}], "start": 1751851532583, "stop": 1751851553652}], "parameters": [{"name": "credentials", "value": "{'username': 'invalid_user', 'password': 'invalid_password'}"}, {"name": "base_url", "value": "'http://*************:8080'"}], "start": 1751851532583, "stop": 1751851553653}], "attachments": [{"name": "登录失败信息", "source": "c24703e6-d286-4cbc-a00c-a8e2db91f8da-attachment.txt", "type": "text/plain"}], "start": 1751851532582, "stop": 1751851553655}, {"name": "验证登录失败行为", "status": "failed", "statusDetails": {"message": "AssertionError: 应该显示错误信息，但当前URL: about:blank\nassert '/login' in 'about:blank'\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\tests\\ui\\smoke\\test_scf_login.py\", line 100, in test_login_failure_scenarios\n    assert \"/login\" in current_url, f\"{expected_behavior}，但当前URL: {current_url}\"\n"}, "start": 1751851553655, "stop": 1751851553655}], "attachments": [{"name": "失败截图", "source": "2320f373-4d1d-4118-9a5a-f0111d910134-attachment.png", "type": "image/png"}, {"name": "页面HTML", "source": "4b95883a-3c06-456f-899c-f52845d9521b-attachment.html", "type": "text/html"}, {"name": "当前URL", "source": "7391d1dd-e821-4180-8a79-19c6eae8fa35-attachment.txt", "type": "text/plain"}, {"name": "log", "source": "5fc7bf2f-71f0-4a53-89f4-6ee162d56b42-attachment.txt", "type": "text/plain"}, {"name": "stderr", "source": "a376e320-16ea-4b3a-ad73-c1cd82b9cd08-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "browser_name", "value": "'chromium'"}, {"name": "credentials_key", "value": "'invalid_user'"}, {"name": "expected_behavior", "value": "'应该显示错误信息'"}], "start": 1751851532542, "stop": 1751851553656, "uuid": "09b7aa96-284a-48df-9c0e-26a919fe2375", "historyId": "14f7173cd53e4fc1432a01c8e67b0401", "testCaseId": "a99cc8e5e6e05ba82a9dc1ae7049de1b", "fullName": "tests.ui.smoke.test_scf_login.TestSCFLogin#test_login_failure_scenarios", "labels": [{"name": "feature", "value": "用户登录"}, {"name": "story", "value": "登录失败场景"}, {"name": "severity", "value": "normal"}, {"name": "epic", "value": "供应链金融备案系统"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "tests.ui.smoke"}, {"name": "suite", "value": "test_scf_login"}, {"name": "subSuite", "value": "TestSC<PERSON><PERSON>in"}, {"name": "host", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "thread", "value": "24864-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.ui.smoke.test_scf_login"}]}