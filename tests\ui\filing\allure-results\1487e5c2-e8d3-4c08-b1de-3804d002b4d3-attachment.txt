conftest - INFO - 测试服务器连接正常
conftest - INFO - ============================================================
conftest - INFO - 开始 SCF 备案流程测试会话
conftest - INFO - ============================================================
conftest - INFO - 开始测试类: TestSCFFilingBoundary
fixtures.filing_fixtures - INFO - 开始备案测试: test_concurrent_operations_boundary[chromium]
components.ocr_util - INFO - OCR引擎初始化成功
components.filing_data_manager - INFO - 生成备案测试数据，场景: normal
components.filing_data_manager - INFO - 会话数据已保存: e76b45c4
components.filing_data_manager - INFO - 测试数据生成完成，会话ID: e76b45c4
workflows.scf_filing_workflow - INFO - 登录系统，用户名: scf_4nuioc
SCFLoginPage - INFO - 开始登录流程（带重试），用户名: scf_4nuioc, 最大重试次数: 5
SCFLoginPage - INFO - 点击账号登录标签
pages.base_page - INFO - 等待元素 text=账号登录 状态: visible
SCFLoginPage - WARNING - 点击账号登录标签失败: Locator.wait_for: Timeout 10000ms exceeded.
Call log:
  - waiting for locator("text=账号登录") to be visible

SCFLoginPage - INFO - 输入用户名: scf_4nuioc
pages.base_page - INFO - 填充文本到元素 input[placeholder='请输入账号']: scf_4nuioc
components.retry_util - WARNING - 函数 fill 第 1 次尝试失败: Locator.wait_for: Timeout 30000ms exceeded.
Call log:
  - waiting for locator("input[placeholder='请输入账号']") to be visible

pages.base_page - INFO - 填充文本到元素 input[placeholder='请输入账号']: scf_4nuioc
components.retry_util - WARNING - 函数 fill 第 2 次尝试失败: Locator.wait_for: Timeout 30000ms exceeded.
Call log:
  - waiting for locator("input[placeholder='请输入账号']") to be visible

pages.base_page - INFO - 填充文本到元素 input[placeholder='请输入账号']: scf_4nuioc
components.retry_util - WARNING - 函数 fill 第 3 次尝试失败: Locator.wait_for: Timeout 30000ms exceeded.
Call log:
  - waiting for locator("input[placeholder='请输入账号']") to be visible

components.retry_util - ERROR - 函数 fill 在 3 次尝试后仍然失败
conftest - ERROR - 测试失败: test_concurrent_operations_boundary[chromium] - Locator.wait_for: Timeout 30000ms exceeded.
Call log:
  - waiting for locator("input[placeholder='请输入账号']") to be visible

fixtures.conftest - ERROR - 测试失败，已保存调试信息: test_concurrent_operations_boundary[chromium]
conftest - INFO - 完成测试: test_concurrent_operations_boundary[chromium]
components.filing_data_manager - INFO - 清理了 0 个旧会话数据
fixtures.filing_fixtures - INFO - 备案测试完成: test_concurrent_operations_boundary[chromium]
conftest - INFO - 结束测试类: TestSCFFilingBoundary
conftest - INFO - ============================================================
conftest - INFO - 结束 SCF 备案流程测试会话
conftest - INFO - ============================================================
conftest - INFO - 测试环境检查完成
