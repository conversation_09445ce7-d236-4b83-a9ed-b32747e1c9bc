"""
供应链金融备案申请页面对象
Supply Chain Finance (SCF) Registration Page Object
"""

import allure
from typing import Any, Dict, List, Optional, Union
from pathlib import Path
from pages.base_page import BasePage
from components.log_manager import get_logger

logger = get_logger(__name__)


class SCFRegistrationPage(BasePage):
    """供应链金融备案申请页面"""
    
    def __init__(self, page):
        """
        初始化备案申请页面
        
        Args:
            page: Playwright页面对象
        """
        super().__init__(page)
        self.logger = get_logger(self.__class__.__name__)
        
        # 导航元素
        self.registration_menu = "text=备案登记"
        self.registration_apply_button = "role=button[name='备案申请']"
        self.next_step_button = "role=button[name='下一步']"
        self.save_button = "role=button[name='保存']"
        
        # 文件上传相关元素
        self.upload_card = ".upload-card"
        self.upload_icon_text = ".upload-icon-text"
        self.upload_attachment_text = "text=点击此处上传附件"
        
        # 表单元素 - 是/否选择
        self.yes_option = "text=是"
        self.no_option = "text=否"
        self.description_input = "role=textbox[name='情况说明（必填），最多1000字符']"
        
        # 专业机构选择
        self.institution_dropdown = "role=textbox[name='请选择专业机构']"
        self.institution_option = "text=天津中互金数据科技有限公司"
    
    @allure.step("点击备案登记菜单")
    def click_registration_menu(self):
        """点击备案登记菜单"""
        self.logger.info("点击备案登记菜单")
        self.click(self.registration_menu)
        return self
    
    @allure.step("点击备案申请按钮")
    def click_registration_apply(self):
        """点击备案申请按钮"""
        self.logger.info("点击备案申请按钮")
        self.click(self.registration_apply_button)
        return self
    
    @allure.step("上传文件: {file_path}")
    def upload_file(self, file_path: Union[str, Path], upload_selector: str = None):
        """
        上传文件
        
        Args:
            file_path: 文件路径
            upload_selector: 上传区域选择器（可选）
        """
        file_path_str = str(file_path)
        self.logger.info(f"上传文件: {file_path_str}")
        
        if upload_selector:
            # 点击指定的上传区域
            self.click(upload_selector)
        else:
            # 点击第一个上传卡片
            self.click(f"{self.upload_card}:first-child")
        
        # 设置文件
        self.page.set_input_files("body", file_path_str)
        return self
    
    @allure.step("批量上传文件到指定区域")
    def upload_files_to_areas(self, file_uploads: Dict[str, str]):
        """
        批量上传文件到指定区域
        
        Args:
            file_uploads: 文件上传映射，键为上传区域ID，值为文件路径
        """
        self.logger.info(f"批量上传文件，共 {len(file_uploads)} 个文件")
        
        for area_id, file_path in file_uploads.items():
            with allure.step(f"上传文件到 {area_id}: {file_path}"):
                # 构建上传区域选择器
                upload_selector = f"#{area_id} {self.upload_attachment_text}"
                
                # 点击上传区域
                if self.is_visible(upload_selector):
                    self.click(upload_selector)
                    self.page.set_input_files("body", file_path)
                    self.logger.info(f"文件上传成功: {area_id} -> {file_path}")
                else:
                    self.logger.warning(f"上传区域不可见: {area_id}")
        
        return self
    
    @allure.step("选择是/否选项: {option} 在区域 {area_id}")
    def select_yes_no_option(self, area_id: str, option: str, description: str = None):
        """
        在指定区域选择是/否选项
        
        Args:
            area_id: 区域ID
            option: 选项 ("是" 或 "否")
            description: 情况说明（如果选择"否"通常需要填写）
        """
        self.logger.info(f"在区域 {area_id} 选择选项: {option}")
        
        # 构建选择器
        option_selector = f"#{area_id} text={option}"
        
        # 点击选项
        self.click(option_selector)
        
        # 如果提供了描述，填写情况说明
        if description:
            description_selector = f"#{area_id}Des {self.description_input}"
            if self.is_visible(description_selector):
                self.fill(description_selector, description)
                self.logger.info(f"填写情况说明: {description}")
        
        return self
    
    @allure.step("填写情况说明")
    def fill_description(self, area_id: str, description: str):
        """
        填写情况说明
        
        Args:
            area_id: 区域ID
            description: 说明内容
        """
        self.logger.info(f"填写 {area_id} 的情况说明")
        
        # 尝试多种可能的选择器
        possible_selectors = [
            f"#{area_id}Des {self.description_input}",
            f"#{area_id} {self.description_input}",
            f"#{area_id}Des .arco-textarea",
            f"#{area_id} .arco-textarea"
        ]
        
        for selector in possible_selectors:
            if self.is_visible(selector):
                self.fill(selector, description)
                self.logger.info(f"成功填写情况说明: {selector}")
                return self
        
        self.logger.warning(f"未找到 {area_id} 的情况说明输入框")
        return self
    
    @allure.step("点击下一步")
    def click_next_step(self):
        """点击下一步按钮"""
        self.logger.info("点击下一步按钮")
        self.click(self.next_step_button)
        # 等待页面加载
        self.wait_for_load_state("networkidle")
        return self
    
    @allure.step("选择专业机构")
    def select_institution(self, institution_name: str = "天津中互金数据科技有限公司"):
        """
        选择专业机构
        
        Args:
            institution_name: 机构名称
        """
        self.logger.info(f"选择专业机构: {institution_name}")
        
        # 点击下拉框
        self.click(self.institution_dropdown)
        
        # 选择机构
        institution_selector = f"text={institution_name}"
        self.click(institution_selector)
        
        return self
    
    @allure.step("保存申请")
    def save_application(self):
        """保存申请"""
        self.logger.info("保存申请")
        self.click(self.save_button)
        return self
    
    @allure.step("填写基础材料表单")
    def fill_basic_materials_form(self, form_data: Dict[str, Any]):
        """
        填写基础材料表单
        
        Args:
            form_data: 表单数据字典
        """
        self.logger.info("填写基础材料表单")
        
        # 处理文件上传
        if "file_uploads" in form_data:
            file_uploads = {
                "bfmArtAssocId": form_data["file_uploads"].get("articles_association", ""),
                "bfmAuditRptId": form_data["file_uploads"].get("audit_report", ""),
                "bfmFrameCoopId": form_data["file_uploads"].get("framework_cooperation", ""),
                "bfmClearSetId": form_data["file_uploads"].get("clearing_settlement", ""),
                "bfmICPCertId": form_data["file_uploads"].get("icp_certificate", ""),
                "bfmSCStatusId": form_data["file_uploads"].get("sc_status", "")
            }
            
            # 过滤空值
            file_uploads = {k: v for k, v in file_uploads.items() if v}
            self.upload_files_to_areas(file_uploads)
        
        return self
    
    @allure.step("填写业务合规表单")
    def fill_business_compliance_form(self, form_data: Dict[str, Any]):
        """
        填写业务合规表单
        
        Args:
            form_data: 表单数据字典
        """
        self.logger.info("填写业务合规表单")
        
        # 业务合规相关的字段映射
        compliance_fields = [
            "bcVele", "bcVbsaroa", "bcCoo", "bcPcabsmrc", 
            "bcFswscsv", "bcRosctedt", "bcSmflee", "bcCbscud"
        ]
        
        for field in compliance_fields:
            if field in form_data:
                field_data = form_data[field]
                option = field_data.get("option", "否")
                description = field_data.get("description", "测试")
                
                self.select_yes_no_option(field, option, description)
        
        return self
    
    @allure.step("填写系统合规表单")
    def fill_system_compliance_form(self, form_data: Dict[str, Any]):
        """
        填写系统合规表单
        
        Args:
            form_data: 表单数据字典
        """
        self.logger.info("填写系统合规表单")
        
        # 系统合规相关的字段
        system_fields = [
            "scoSsdama", "scoSccmfd", "scoSml3", "scoSmdoa"
        ]
        
        for field in system_fields:
            if field in form_data:
                field_data = form_data[field]
                option = field_data.get("option", "否")
                description = field_data.get("description", "测试")
                
                self.select_yes_no_option(field, option, description)
        
        return self
    
    @allure.step("完成整个申请流程")
    def complete_full_application(self, application_data: Dict[str, Any]):
        """
        完成整个申请流程
        
        Args:
            application_data: 完整的申请数据
        """
        self.logger.info("开始完成整个申请流程")
        
        # 1. 点击备案登记
        self.click_registration_menu()
        
        # 2. 点击备案申请
        self.click_registration_apply()
        
        # 3. 填写基础材料
        if "basic_materials" in application_data:
            self.fill_basic_materials_form(application_data["basic_materials"])
            self.click_next_step()
        
        # 4. 填写业务合规
        if "business_compliance" in application_data:
            self.fill_business_compliance_form(application_data["business_compliance"])
            self.click_next_step()
        
        # 5. 填写系统合规
        if "system_compliance" in application_data:
            self.fill_system_compliance_form(application_data["system_compliance"])
            self.click_next_step()
        
        # 6. 选择专业机构
        if "institution" in application_data:
            self.select_institution(application_data["institution"])
        
        # 7. 保存申请
        self.save_application()
        
        self.logger.info("申请流程完成")
        return self


class SCFFormStepsPage(BasePage):
    """供应链金融备案申请表单步骤页面"""

    def __init__(self, page):
        """
        初始化表单步骤页面

        Args:
            page: Playwright页面对象
        """
        super().__init__(page)
        self.logger = get_logger(self.__class__.__name__)

    @allure.step("填写业务管理表单")
    def fill_business_management_form(self, form_data: Dict[str, Any]):
        """
        填写业务管理表单

        Args:
            form_data: 表单数据字典
        """
        self.logger.info("填写业务管理表单")

        # 业务管理相关字段
        bm_fields = [
            "bmBctms", "bmCicrm", "bmNmcsf", "bmNfsadp",
            "bmCamm", "bmCiv", "bmNsuvc", "bmFvrap",
            "bmClba", "bmCfia", "bmAmlct", "bmSscdm"
        ]

        for field in bm_fields:
            if field in form_data:
                field_data = form_data[field]
                option = field_data.get("option", "否")
                description = field_data.get("description", "测试")

                self._select_yes_no_with_description(field, option, description)

        return self

    @allure.step("填写业务流程与合规表单")
    def fill_business_process_compliance_form(self, form_data: Dict[str, Any]):
        """
        填写业务流程与合规表单

        Args:
            form_data: 表单数据字典
        """
        self.logger.info("填写业务流程与合规表单")

        # 业务流程合规字段
        bpl_fields = [
            "bplDbqav", "bplRuivr", "bplFclb"
        ]

        for field in bpl_fields:
            if field in form_data:
                field_data = form_data[field]
                option = field_data.get("option", "否")
                description = field_data.get("description", "测试")

                self._select_yes_no_with_description(field, option, description)

        return self

    @allure.step("填写操作风险表单")
    def fill_operational_risk_form(self, form_data: Dict[str, Any]):
        """
        填写操作风险表单

        Args:
            form_data: 表单数据字典
        """
        self.logger.info("填写操作风险表单")

        # 操作风险字段
        op_fields = [
            "opUtbdi", "opCceiq", "opReiar"
        ]

        for field in op_fields:
            if field in form_data:
                field_data = form_data[field]
                option = field_data.get("option", "否")
                description = field_data.get("description", "测试")

                self._select_yes_no_with_description(field, option, description)

        return self

    @allure.step("填写第三方合作表单")
    def fill_third_party_cooperation_form(self, form_data: Dict[str, Any]):
        """
        填写第三方合作表单

        Args:
            form_data: 表单数据字典
        """
        self.logger.info("填写第三方合作表单")

        # 第三方合作字段
        tp_fields = [
            "tpCtr", "tpNdpt", "tpErt5l"
        ]

        for field in tp_fields:
            if field in form_data:
                field_data = form_data[field]
                option = field_data.get("option", "否")
                description = field_data.get("description", "测试")

                self._select_yes_no_with_description(field, option, description)

        return self

    @allure.step("填写资金方表单")
    def fill_funding_party_form(self, form_data: Dict[str, Any]):
        """
        填写资金方表单

        Args:
            form_data: 表单数据字典
        """
        self.logger.info("填写资金方表单")

        # 资金方字段
        fp_fields = [
            "fpUtbdf", "fpMpft", "fpNffli"
        ]

        for field in fp_fields:
            if field in form_data:
                field_data = form_data[field]
                option = field_data.get("option", "否")
                description = field_data.get("description", "测试")

                self._select_yes_no_with_description(field, option, description)

        return self

    @allure.step("填写付款方表单")
    def fill_payer_form(self, form_data: Dict[str, Any]):
        """
        填写付款方表单

        Args:
            form_data: 表单数据字典
        """
        self.logger.info("填写付款方表单")

        # 付款方字段
        pp_fields = [
            "ppPrmer", "ppSnidd"
        ]

        for field in pp_fields:
            if field in form_data:
                field_data = form_data[field]
                option = field_data.get("option", "否")
                description = field_data.get("description", "测试")

                self._select_yes_no_with_description(field, option, description)

        return self

    @allure.step("填写费用管理表单")
    def fill_fee_management_form(self, form_data: Dict[str, Any]):
        """
        填写费用管理表单

        Args:
            form_data: 表单数据字典
        """
        self.logger.info("填写费用管理表单")

        # 费用管理字段
        fm_fields = [
            "fmTfp", "fmNfspd", "fmNufur", "fmSsfcf",
            "fmCfofp", "fmFrsme", "fmDfpt", "fmNuf"
        ]

        for field in fm_fields:
            if field in form_data:
                field_data = form_data[field]
                option = field_data.get("option", "否")
                description = field_data.get("description", "测试")

                self._select_yes_no_with_description(field, option, description)

        return self

    @allure.step("填写信息报送表单")
    def fill_information_reporting_form(self, form_data: Dict[str, Any]):
        """
        填写信息报送表单

        Args:
            form_data: 表单数据字典
        """
        self.logger.info("填写信息报送表单")

        # 信息报送字段
        ir_fields = [
            "irSirr", "irSiid"
        ]

        for field in ir_fields:
            if field in form_data:
                field_data = form_data[field]
                option = field_data.get("option", "否")
                description = field_data.get("description", "测试")

                self._select_yes_no_with_description(field, option, description)

        return self

    def _select_yes_no_with_description(self, field_id: str, option: str, description: str):
        """
        选择是/否选项并填写描述

        Args:
            field_id: 字段ID
            option: 选项 ("是" 或 "否")
            description: 描述内容
        """
        # 选择是/否
        option_selector = f"#{field_id} text={option}"
        if self.is_visible(option_selector):
            self.click(option_selector)

        # 填写描述
        description_selectors = [
            f"#{field_id}Des role=textbox[name='情况说明（必填），最多1000字符']",
            f"#{field_id}Des .arco-textarea",
            f"#{field_id} .arco-textarea"
        ]

        for selector in description_selectors:
            if self.is_visible(selector):
                self.fill(selector, description)
                break
