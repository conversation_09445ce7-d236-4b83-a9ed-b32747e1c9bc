#!/usr/bin/env python3
"""
备案流程测试专用 Fixtures
提供备案测试所需的数据、配置和工具
"""

import pytest
import allure
from pathlib import Path
from typing import Dict, Generator
from components.filing_data_manager import FilingDataManager
from components.log_manager import get_logger
from workflows.scf_filing_workflow import SCFFilingWorkflow
from playwright.sync_api import Page


@pytest.fixture(scope="session")
def filing_data_manager() -> FilingDataManager:
    """
    备案数据管理器 fixture
    会话级别，整个测试会话共享
    """
    logger = get_logger(__name__)
    logger.info("初始化备案数据管理器")
    
    data_manager = FilingDataManager()
    
    # 清理旧数据
    data_manager.cleanup_old_sessions(days=7)
    
    yield data_manager
    
    # 测试结束后清理
    logger.info("清理备案数据管理器")
    data_manager.cleanup_old_sessions(days=1)


@pytest.fixture(scope="function")
def filing_test_data(filing_data_manager: FilingDataManager, request) -> Dict:
    """
    备案测试数据 fixture
    函数级别，每个测试函数独立的数据
    """
    logger = get_logger(__name__)
    
    # 从测试标记中获取场景类型
    scenario = "normal"
    if hasattr(request, 'node'):
        for mark in request.node.iter_markers():
            if mark.name == "scenario":
                scenario = mark.args[0] if mark.args else "normal"
                break
    
    logger.info(f"生成备案测试数据，场景: {scenario}")
    
    # 生成测试数据
    test_data = filing_data_manager.generate_filing_test_data(scenario)
    
    # 添加到 Allure 报告
    allure.attach(
        str(test_data),
        name=f"测试数据_{scenario}",
        attachment_type=allure.attachment_type.TEXT
    )
    
    yield test_data
    
    # 测试结束后记录数据使用情况
    logger.info(f"测试数据使用完成，会话ID: {test_data.get('session_id')}")


@pytest.fixture(scope="function")
def filing_workflow(page: Page) -> Generator[SCFFilingWorkflow, None, None]:
    """
    备案工作流 fixture
    函数级别，每个测试函数独立的工作流实例
    """
    logger = get_logger(__name__)
    logger.info("初始化备案工作流")
    
    workflow = SCFFilingWorkflow(page)
    
    yield workflow
    
    # 测试结束后清理
    try:
        # 尝试退出登录（如果已登录）
        if hasattr(workflow, 'main_page'):
            workflow.main_page.logout()
    except Exception as e:
        logger.warning(f"清理工作流时出错: {e}")
    
    logger.info("备案工作流清理完成")


@pytest.fixture(scope="function")
def filing_test_files() -> Dict[str, str]:
    """
    备案测试文件 fixture
    提供测试所需的文件路径
    """
    logger = get_logger(__name__)
    logger.info("准备备案测试文件")
    
    # 测试文件目录
    test_files_dir = Path(__file__).parent.parent / "test_data" / "files"
    test_files_dir.mkdir(parents=True, exist_ok=True)
    
    # 定义测试文件
    test_files = {
        "basic_doc": "异常信息共享系统V1.5需求.docx",
        "assoc_doc": "格式二模版.xlsx",
        "supplement_doc": "供应链金融测试报告V1.6.0.pdf",
        "audit_report": "供应链金融测试报告V1.6.0.pdf",
        "framework_coop": "供应链金融测试报告V1.6.0.pdf"
    }
    
    # 验证文件存在
    missing_files = []
    for key, filename in test_files.items():
        file_path = test_files_dir / filename
        if not file_path.exists():
            missing_files.append(filename)
    
    if missing_files:
        logger.warning(f"缺少测试文件: {missing_files}")
        # 可以选择跳过测试或创建空文件
        for filename in missing_files:
            file_path = test_files_dir / filename
            file_path.touch()  # 创建空文件
            logger.info(f"创建空测试文件: {filename}")
    
    # 转换为绝对路径
    absolute_paths = {}
    for key, filename in test_files.items():
        absolute_paths[key] = str(test_files_dir / filename)
    
    logger.info(f"备案测试文件准备完成: {len(absolute_paths)} 个文件")
    
    yield absolute_paths
    
    logger.info("备案测试文件清理完成")


@pytest.fixture(scope="function")
def filing_credentials() -> Dict[str, str]:
    """
    备案测试凭证 fixture
    提供测试用的登录凭证
    """
    return {
        "username": "scf_4nuioc",
        "password": "Scf123456."
    }


@pytest.fixture(scope="function")
def filing_base_url() -> str:
    """
    备案系统基础URL fixture
    """
    return "http://172.18.12.128:8080"


@pytest.fixture(scope="function", autouse=True)
def filing_test_setup_teardown(request, page: Page):
    """
    备案测试自动设置和清理 fixture
    自动应用到所有备案测试
    """
    logger = get_logger(__name__)
    test_name = request.node.name
    
    logger.info(f"开始备案测试: {test_name}")
    
    # 测试前设置
    start_url = page.url
    
    yield
    
    # 测试后清理
    try:
        # 保存最终截图
        screenshot = page.screenshot()
        allure.attach(
            screenshot,
            name=f"测试结束截图_{test_name}",
            attachment_type=allure.attachment_type.PNG
        )
        
        # 记录最终URL
        final_url = page.url
        if final_url != start_url:
            logger.info(f"URL变化: {start_url} -> {final_url}")
        
    except Exception as e:
        logger.warning(f"测试清理时出错: {e}")
    
    logger.info(f"备案测试完成: {test_name}")


@pytest.fixture(scope="function")
def filing_error_handler():
    """
    备案测试错误处理器 fixture
    提供统一的错误处理和重试机制
    """
    logger = get_logger(__name__)
    
    class FilingErrorHandler:
        def __init__(self):
            self.retry_count = 0
            self.max_retries = 3
        
        def handle_error(self, error: Exception, operation: str) -> bool:
            """
            处理错误并决定是否重试
            
            Args:
                error: 异常对象
                operation: 操作描述
                
            Returns:
                是否应该重试
            """
            self.retry_count += 1
            logger.error(f"操作失败 '{operation}': {error}")
            
            if self.retry_count < self.max_retries:
                logger.info(f"准备重试 ({self.retry_count}/{self.max_retries})")
                return True
            else:
                logger.error(f"达到最大重试次数，操作最终失败: {operation}")
                return False
        
        def reset_retry_count(self):
            """重置重试计数"""
            self.retry_count = 0
    
    yield FilingErrorHandler()


@pytest.fixture(scope="function")
def filing_performance_monitor():
    """
    备案测试性能监控器 fixture
    监控测试执行性能
    """
    logger = get_logger(__name__)
    
    class FilingPerformanceMonitor:
        def __init__(self):
            self.start_time = None
            self.checkpoints = []
        
        def start(self):
            """开始性能监控"""
            import time
            self.start_time = time.time()
            logger.info("开始性能监控")
        
        def checkpoint(self, name: str):
            """添加性能检查点"""
            import time
            if self.start_time:
                elapsed = time.time() - self.start_time
                self.checkpoints.append((name, elapsed))
                logger.info(f"性能检查点 '{name}': {elapsed:.2f}秒")
        
        def finish(self):
            """结束性能监控"""
            import time
            if self.start_time:
                total_time = time.time() - self.start_time
                logger.info(f"总执行时间: {total_time:.2f}秒")
                
                # 添加到 Allure 报告
                performance_report = f"总时间: {total_time:.2f}秒\n"
                for name, elapsed in self.checkpoints:
                    performance_report += f"{name}: {elapsed:.2f}秒\n"
                
                allure.attach(
                    performance_report,
                    name="性能报告",
                    attachment_type=allure.attachment_type.TEXT
                )
    
    monitor = FilingPerformanceMonitor()
    monitor.start()
    
    yield monitor
    
    monitor.finish()


# 测试标记定义
def pytest_configure(config):
    """配置 pytest 标记"""
    config.addinivalue_line(
        "markers", "scenario(name): 指定测试场景类型 (normal, boundary, invalid, edge_case)"
    )
    config.addinivalue_line(
        "markers", "filing: 备案流程相关测试"
    )
    config.addinivalue_line(
        "markers", "filing_smoke: 备案流程冒烟测试"
    )
    config.addinivalue_line(
        "markers", "filing_regression: 备案流程回归测试"
    )
    config.addinivalue_line(
        "markers", "filing_boundary: 备案流程边界值测试"
    )
    config.addinivalue_line(
        "markers", "filing_exception: 备案流程异常测试"
    )
