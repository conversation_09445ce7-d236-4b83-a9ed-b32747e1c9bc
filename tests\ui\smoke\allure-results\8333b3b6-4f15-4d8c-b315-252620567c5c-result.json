{"name": "test_login_page_elements[chromium]", "status": "broken", "statusDetails": {"message": "KeyError: 'locator'", "trace": "test_scf_login.py:110: in test_login_page_elements\n    workflow.login_page.click_account_login_tab()\n..\\..\\..\\pages\\scf_login_page.py:58: in click_account_login_tab\n    self.click(self.account_tab)\nE   KeyError: 'locator'"}, "description": "验证登录页面必要元素存在", "steps": [{"name": "导航到登录页面", "status": "passed", "steps": [{"name": "导航到登录页面", "status": "passed", "steps": [{"name": "导航到页面: 'http://*************:8080/login'", "status": "passed", "parameters": [{"name": "url", "value": "'http://*************:8080/login'"}, {"name": "wait_until", "value": "'domcontentloaded'"}], "start": *************, "stop": *************}], "parameters": [{"name": "base_url", "value": "'http://*************:8080'"}], "start": *************, "stop": *************}], "start": *************, "stop": *************}, {"name": "验证登录页面元素", "status": "broken", "statusDetails": {"message": "KeyError: 'locator'\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\tests\\ui\\smoke\\test_scf_login.py\", line 110, in test_login_page_elements\n    workflow.login_page.click_account_login_tab()\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 58, in click_account_login_tab\n    self.click(self.account_tab)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 201, in impl\n    with StepContext(self.title.format(*args, **params), params):\n                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "steps": [{"name": "检查元素是否可见: 'text=账号登录'", "status": "passed", "parameters": [{"name": "locator", "value": "'text=账号登录'"}, {"name": "timeout", "value": "None"}], "start": *************, "stop": *************}, {"name": "点击账号登录标签", "status": "broken", "statusDetails": {"message": "KeyError: 'locator'\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 58, in click_account_login_tab\n    self.click(self.account_tab)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 201, in impl\n    with StepContext(self.title.format(*args, **params), params):\n                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "start": *************, "stop": *************}], "start": *************, "stop": *************}], "attachments": [{"name": "失败截图", "source": "55f3bc61-3403-4519-8aa1-9657a0589d65-attachment.png", "type": "image/png"}, {"name": "页面HTML", "source": "5a095e18-c574-4be4-9b6d-7a9e5c522b61-attachment.html", "type": "text/html"}, {"name": "当前URL", "source": "e82e93d5-3c57-469d-a7f4-319e64df014b-attachment.txt", "type": "text/plain"}, {"name": "log", "source": "b922d42b-a121-4b2b-afbd-9cf9311ef597-attachment.txt", "type": "text/plain"}, {"name": "stderr", "source": "209154f5-7a70-4628-bf36-d37408f50a6d-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "browser_name", "value": "'chromium'"}], "start": 1751635103287, "stop": *************, "uuid": "e81bc93e-07cd-4935-8108-979a2a3d69e0", "historyId": "7869c781375e544239aebc49d435f721", "testCaseId": "bf3beae4a949bf431d6c971553df874b", "fullName": "tests.ui.smoke.test_scf_login.TestSCFLogin#test_login_page_elements", "labels": [{"name": "feature", "value": "用户登录"}, {"name": "epic", "value": "供应链金融备案系统"}, {"name": "severity", "value": "minor"}, {"name": "story", "value": "登录页面元素验证"}, {"name": "tag", "value": "component"}, {"name": "parentSuite", "value": "tests.ui.smoke"}, {"name": "suite", "value": "test_scf_login"}, {"name": "subSuite", "value": "TestSC<PERSON><PERSON>in"}, {"name": "host", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "thread", "value": "21436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.ui.smoke.test_scf_login"}]}