{"name": "test_login_page_elements[chromium]", "status": "passed", "description": "验证登录页面必要元素存在", "steps": [{"name": "导航到登录页面", "status": "passed", "steps": [{"name": "导航到登录页面", "status": "passed", "steps": [{"name": "导航到页面: 'http://*************:8080/login'", "status": "passed", "parameters": [{"name": "url", "value": "'http://*************:8080/login'"}, {"name": "wait_until", "value": "'domcontentloaded'"}], "start": 1751853961966, "stop": 1751853962875}], "parameters": [{"name": "base_url", "value": "'http://*************:8080'"}], "start": 1751853961966, "stop": 1751853962875}], "start": 1751853961966, "stop": 1751853962875}, {"name": "验证登录页面元素", "status": "passed", "steps": [{"name": "检查元素是否可见: 'text=账号登录'", "status": "passed", "parameters": [{"name": "locator", "value": "'text=账号登录'"}, {"name": "timeout", "value": "None"}], "start": 1751853962875, "stop": 1751853963082}, {"name": "点击账号登录标签", "status": "passed", "steps": [{"name": "等待元素出现: 'text=账号登录'", "status": "passed", "parameters": [{"name": "locator", "value": "'text=账号登录'"}, {"name": "state", "value": "'visible'"}, {"name": "timeout", "value": "10000"}], "start": 1751853963082, "stop": 1751853963088}, {"name": "智能点击: 'text=账号登录'", "status": "passed", "steps": [{"name": "等待元素稳定: 'text=账号登录'", "status": "passed", "parameters": [{"name": "locator", "value": "'text=账号登录'"}, {"name": "timeout", "value": "None"}], "start": 1751853963088, "stop": 1751853963422}], "parameters": [{"name": "locator", "value": "'text=账号登录'"}, {"name": "force", "value": "False"}, {"name": "timeout", "value": "None"}], "start": 1751853963088, "stop": 1751853963487}], "start": 1751853963082, "stop": 1751853964494}, {"name": "检查元素是否可见: 'input[placeholder='请输入账号']'", "status": "passed", "parameters": [{"name": "locator", "value": "'input[placeholder='请输入账号']'"}, {"name": "timeout", "value": "None"}], "start": 1751853964494, "stop": 1751853964499}, {"name": "检查元素是否可见: 'input[type='password']'", "status": "passed", "parameters": [{"name": "locator", "value": "'input[type='password']'"}, {"name": "timeout", "value": "None"}], "start": 1751853964499, "stop": 1751853964503}, {"name": "检查元素是否可见: 'input[placeholder='请输入图形验证码']'", "status": "passed", "parameters": [{"name": "locator", "value": "'input[placeholder='请输入图形验证码']'"}, {"name": "timeout", "value": "None"}], "start": 1751853964503, "stop": 1751853964507}, {"name": "检查元素是否可见: 'button[type='submit']'", "status": "passed", "parameters": [{"name": "locator", "value": "'button[type='submit']'"}, {"name": "timeout", "value": "None"}], "start": 1751853964507, "stop": 1751853964512}], "start": 1751853962875, "stop": 1751853964512}], "attachments": [{"name": "log", "source": "eb327cf7-305a-4a79-929e-398f5a7f1593-attachment.txt", "type": "text/plain"}, {"name": "stdout", "source": "5798e500-3859-48a0-9016-aeed611ea593-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "browser_name", "value": "'chromium'"}], "start": 1751853961920, "stop": 1751853964512, "uuid": "7e3c754e-c899-4c5b-a6e4-2a1006e36a47", "historyId": "7869c781375e544239aebc49d435f721", "testCaseId": "bf3beae4a949bf431d6c971553df874b", "fullName": "tests.ui.smoke.test_scf_login.TestSCFLogin#test_login_page_elements", "labels": [{"name": "feature", "value": "用户登录"}, {"name": "story", "value": "登录页面元素验证"}, {"name": "severity", "value": "minor"}, {"name": "epic", "value": "供应链金融备案系统"}, {"name": "tag", "value": "component"}, {"name": "parentSuite", "value": "tests.ui.smoke"}, {"name": "suite", "value": "test_scf_login"}, {"name": "subSuite", "value": "TestSC<PERSON><PERSON>in"}, {"name": "host", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "thread", "value": "14940-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.ui.smoke.test_scf_login"}]}