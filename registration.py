import re
from playwright.sync_api import Playwright, sync_playwright, expect


def run(playwright: Playwright) -> None:
    browser = playwright.chromium.launch(headless=False)
    context = browser.new_context()
    page = context.new_page()
    page.goto("http://172.18.12.128:8080/login")
    page.get_by_text("账号登录").click()
    page.get_by_role("textbox", name="请输入账号").click()
    page.get_by_role("textbox", name="请输入账号").click()
    page.locator("div").filter(has_text=re.compile(r"^密码 \|$")).locator("div").first.click()
    page.get_by_role("textbox", name="请输入密码").click()
    page.get_by_role("textbox", name="请输入密码").fill("Scf123456.")
    page.get_by_role("textbox", name="请输入账号").click()
    page.get_by_role("textbox", name="请输入账号").click()
    page.get_by_role("textbox", name="请输入账号").fill("scf_4nuioc")
    page.get_by_role("textbox", name="请输入图形验证码").click()
    page.get_by_role("textbox", name="请输入图形验证码").fill("3923")
    page.get_by_role("button", name="登录").click()
    page.get_by_text("备案登记").click()
    page.get_by_role("button", name="备案申请").click()
    page.locator(".upload-card").first.click()
    page.locator("body").set_input_files("异常信息共享系统V1.5需求.docx")
    page.locator("#bfmArtAssocId").get_by_text("点击此处上传附件").click()
    page.locator("body").set_input_files("格式二模版.xlsx")
    page.get_by_role("button", name="下一步").click()
    page.get_by_role("button", name="下一步").click()
    page.locator("#bfmArtAssocId").get_by_text("点击此处上传附件").click()
    page.locator("body").set_input_files("异常信息共享系统V1.5需求.docx")
    page.locator(".upload-card").first.click()
    page.locator("body").set_input_files("异常信息共享系统V1.5需求.docx")
    page.locator("#bfmArtAssocId").get_by_text("点击此处上传附件").click()
    page.locator("body").set_input_files("供应链金融测试报告V1.6.0.pdf")
    page.locator(".upload-icon-text").first.click()
    page.locator("body").set_input_files("供应链金融测试报告V1.6.0.pdf")
    page.locator("#bfmAuditRptId").get_by_text("点击此处上传附件").click()
    page.locator("body").set_input_files("供应链金融测试报告V1.6.0.pdf")
    page.locator(".upload-icon-text").first.click()
    page.locator("body").set_input_files("供应链金融测试报告V1.6.0.pdf")
    page.locator(".upload-icon-text").first.click()
    page.locator("body").set_input_files("供应链金融测试报告V1.6.0.pdf")
    page.locator("#bfmFrameCoopId").get_by_text("点击此处上传附件").click()
    page.locator("body").set_input_files("供应链金融测试报告V1.6.0.pdf")
    page.locator("#bfmClearSetId").get_by_text("点击此处上传附件").click()
    page.locator("body").set_input_files("供应链金融测试报告V1.6.0.pdf")
    page.locator("#bfmICPCertId").get_by_text("点击此处上传附件").click()
    page.locator("body").set_input_files("供应链金融测试报告V1.6.0.pdf")
    page.locator(".upload-icon-text").first.click()
    page.locator("body").set_input_files("供应链金融测试报告V1.6.0.pdf")
    page.locator(".upload-icon-text").first.click()
    page.locator("body").set_input_files("供应链金融测试报告V1.6.0.pdf")
    page.locator("span > .upload-card").first.click()
    page.locator("body").set_input_files("供应链金融测试报告V1.6.0.pdf")
    page.locator("#bfmSCStatusId").get_by_text("点击此处上传附件").click()
    page.locator("body").set_input_files("供应链金融测试报告V1.6.0.pdf")
    page.get_by_role("button", name="下一步").click()
    page.locator("#bcVele").get_by_text("是").click()
    page.locator("#bcVele").get_by_text("否").click()
    page.locator("#bcVeleDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").click()
    page.locator("#bcVeleDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").fill("测试")
    page.locator(".upload-icon-text").first.click()
    page.locator("body").set_input_files("供应链金融测试报告V1.6.0.pdf")
    page.locator("#bcVbsaroa").get_by_text("是").click()
    page.locator("#bcVbsaroa").get_by_text("否").click()
    page.locator("#bcVbsaroaDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").click()
    page.locator("#bcVbsaroaDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").fill("测试")
    page.locator(".upload-icon-text").first.click()
    page.locator("body").set_input_files("供应链金融测试报告V1.6.0.pdf")
    page.locator("#bcCoo").get_by_text("是").click()
    page.locator("#bcCoo").get_by_text("否").click()
    page.locator("#bcCooDes > .arco-form-item-content-wrapper > .arco-form-item-content > .arco-textarea-wrapper > .arco-textarea").click()
    page.locator(".arco-textarea-wrapper.arco-textarea-focus > .arco-textarea").fill("测试")
    page.locator("#bcQualcompId").get_by_text("点击此处上传附件").click()
    page.locator("body").set_input_files("供应链金融测试报告V1.6.0.pdf")
    page.locator("#bcPcabsmrc").get_by_text("是").click()
    page.locator("#bcPcabsmrc").get_by_text("否").click()
    page.locator("#bcPcabsmrcDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").click()
    page.locator("#bcPcabsmrcDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").fill("测试")
    page.locator("span > .upload-card").first.click()
    page.locator("body").set_input_files("供应链金融测试报告V1.6.0.pdf")
    page.locator("#bcFswscsv").get_by_text("否").click()
    page.locator("#bcFswscsv").get_by_text("是").click()
    page.locator("#bcFswscsvDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").click()
    page.locator("#bcFswscsvDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").fill("测试")
    page.locator("#bcFinstabId").get_by_text("点击此处上传附件").click()
    page.locator("body").set_input_files("供应链金融测试报告V1.6.0.pdf")
    page.locator("#bcRosctedt").get_by_text("是").click()
    page.locator("#bcRosctedt").get_by_text("否").click()
    page.locator("#bcRosctedtDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").click()
    page.locator("#bcRosctedtDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").fill("测试")
    page.locator("#bcOpstechId").get_by_text("点击此处上传附件").click()
    page.locator("body").set_input_files("供应链金融测试报告V1.6.0.pdf")
    page.locator("#bcSmflee").get_by_text("是").click()
    page.locator("#bcSmflee").get_by_text("否").click()
    page.locator("#bcSmfleeDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").click()
    page.locator("#bcSmfleeDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").fill("测试")
    page.locator("#bcFinmgmtId").get_by_text("点击此处上传附件").click()
    page.locator("body").set_input_files("供应链金融测试报告V1.6.0.pdf")
    page.locator("#bcCbscud").get_by_text("是").click()
    page.locator("#bcCbscud").get_by_text("否").click()
    page.locator("#bcCbscud").get_by_role("radio", name="是").press("c")
    page.locator("#bcCbscud").get_by_role("radio", name="是").press("s")
    page.locator("#bcCbscud").get_by_role("radio", name="是").press("c")
    page.locator("#bcCbscud").get_by_role("radio", name="是").press("s")
    page.locator("#bcCbscudDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").click()
    page.locator("#bcCbscudDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").fill("测试")
    page.locator("#bcScclientId").get_by_text("点击此处上传附件").click()
    page.locator("body").set_input_files("供应链金融测试报告V1.6.0.pdf")
    page.get_by_role("button", name="下一步").click()
    page.locator("#scoSsdama").get_by_text("是").click()
    page.locator("#scoSsdama").get_by_text("否").click()
    page.locator("#scoSsdama").get_by_role("radio", name="是").press("c")
    page.locator("#scoSsdama").get_by_role("radio", name="是").press("c")
    page.locator("#scoSsdama").get_by_role("radio", name="是").press("c")
    page.locator("#scoSsdama").get_by_role("radio", name="是").press("c")
    page.locator("#scoSsdama").get_by_role("radio", name="是").press("c")
    page.locator("#scoSsdama").get_by_role("radio", name="是").press("c")
    page.locator("#scoSsdama").get_by_role("radio", name="是").press("c")
    page.locator("#scoSsdama").get_by_role("radio", name="是").press("c")
    page.locator("#scoSsdama").get_by_role("radio", name="是").press("c")
    page.locator("#scoSsdama").get_by_role("radio", name="是").press("c")
    page.locator("#scoSsdama").get_by_role("radio", name="是").press("c")
    page.locator("#scoSsdama").get_by_role("radio", name="是").press("c")
    page.locator("#scoSsdama").get_by_role("radio", name="是").press("c")
    page.locator("#scoSsdama").get_by_role("radio", name="是").press("c")
    page.locator("#scoSsdama").get_by_role("radio", name="是").press("c")
    page.locator("#scoSsdama").get_by_role("radio", name="是").press("c")
    page.locator("#scoSsdama").get_by_role("radio", name="是").press("c")
    page.locator("#scoSsdama").get_by_role("radio", name="是").press("c")
    page.locator("#scoSsdama").get_by_role("radio", name="是").press("c")
    page.locator("#scoSsdama").get_by_role("radio", name="是").press("c")
    page.locator("#scoSsdama").get_by_text("是").click()
    page.locator("#scoSsdamaDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").click()
    page.locator("#scoSsdamaDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").fill("测试")
    page.locator("#scoScisecId").get_by_text("点击此处上传附件").click()
    page.locator("body").set_input_files("供应链金融测试报告V1.6.0.pdf")
    page.locator("#scoSccmfd").get_by_text("是").click()
    page.locator("#scoSccmfd").get_by_text("否").click()
    page.locator("#scoSccmfdDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").click()
    page.locator("#scoSccmfdDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").fill("测试")
    page.locator("#scoSysrelId").get_by_text("点击此处上传附件").click()
    page.locator("body").set_input_files("供应链金融测试报告V1.6.0.pdf")
    page.locator("#scoSml3 > .arco-form-item-content-wrapper > .arco-form-item-content > .arco-radio-group > label > .arco-tag").first.click()
    page.locator("#scoSml3 > .arco-form-item-content-wrapper > .arco-form-item-content > .arco-radio-group > label:nth-child(2) > .arco-tag").click()
    page.get_by_role("textbox", name="情况说明（必填），最多1000字符").nth(3).click()
    page.get_by_role("textbox", name="情况说明（必填），最多1000字符").nth(3).fill("测试")
    page.locator("#scoDataretnId > .arco-form-item-content-wrapper > .arco-form-item-content > .arco-grid > .arco-grid-item > .arco-upload > span > .upload-card").click()
    page.locator("body").set_input_files("供应链金融测试报告V1.6.0.pdf")
    page.locator("#scoSmdoa").get_by_text("是").click()
    page.locator("#scoSmdoa").get_by_text("否").click()
    page.locator("#scoSmdoaDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").fill("测试")
    page.locator("body").set_input_files("供应链金融测试报告V1.6.0.pdf")
    page.get_by_role("button", name="下一步").click()
    page.locator("#scoCdls5y > .arco-form-item-content-wrapper > .arco-form-item-content > .arco-radio-group > label > .arco-tag").first.click()
    page.get_by_role("button", name="下一步").click()
    page.locator("form div").filter(has_text="是 否 请填写").get_by_placeholder("情况说明（必填），最多1000字符").click()
    page.locator("form div").filter(has_text="是 否 请填写").get_by_placeholder("情况说明（必填），最多1000字符").fill("测试")
    page.get_by_role("button", name="下一步").click()
    page.locator("#bmBctms").get_by_text("是").click()
    page.locator("#bmBctms").get_by_text("否").click()
    page.locator("#bmBctmsDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").click()
    page.locator("#bmBctmsDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").fill("测试")
    page.locator("#bmCompRiskId > .arco-form-item-content-wrapper > .arco-form-item-content > .arco-grid > .arco-grid-item > .arco-upload > span > .upload-card > .upload-icon-text").click()
    page.locator("body").set_input_files("供应链金融测试报告V1.6.0.pdf")
    page.locator("#bmCicrm").get_by_text("是").click()
    page.locator("#bmCicrm").get_by_text("否").click()
    page.locator("#bmCicrmDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").click()
    page.locator("#bmCicrmDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").fill("测试")
    page.locator("#bmCtrlRiskId > .arco-form-item-content-wrapper > .arco-form-item-content > .arco-grid > .arco-grid-item > .arco-upload > span > .upload-card > .upload-icon-text").click()
    page.locator("body").set_input_files("供应链金融测试报告V1.6.0.pdf")
    page.locator("#bmNmcsf").get_by_text("是").click()
    page.locator("#bmNmcsf").get_by_text("否").click()
    page.locator("#bmNmcsfDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").click()
    page.locator("#bmNmcsfDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").fill("测试")
    page.locator("#bmNfsadp").get_by_text("是").click()
    page.locator("#bmNfsadp").get_by_text("否").click()
    page.locator("#bmNfsadpDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").click()
    page.locator("#bmNfsadpDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").fill("测试")
    page.locator("#bmCamm").get_by_text("是").click()
    page.locator("#bmCamm").get_by_text("否").click()
    page.locator("#bmCammDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").click()
    page.locator("#bmCammDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").fill("测试")
    page.locator("#bmClientMgId > .arco-form-item-content-wrapper > .arco-form-item-content > .arco-grid > .arco-grid-item > .arco-upload > span > .upload-card > .upload-icon-text").click()
    page.locator("body").set_input_files("供应链金融测试报告V1.6.0.pdf")
    page.locator("#bmCiv").get_by_text("是").click()
    page.locator("#bmCiv").get_by_text("否").click()
    page.locator("#bmCivDes > .arco-form-item-content-wrapper > .arco-form-item-content > .arco-textarea-wrapper > .arco-textarea").click()
    page.locator(".arco-textarea-wrapper.arco-textarea-focus > .arco-textarea").fill("测试")
    page.locator("#bmNsuvc").get_by_text("是").click()
    page.locator("#bmNsuvc").get_by_text("否").click()
    page.locator("#bmNsuvcDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").click()
    page.locator("#bmNsuvcDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").fill("测试")
    page.locator("#bmFvrap").get_by_text("是").click()
    page.locator("#bmFvrap").get_by_text("否").click()
    page.locator("#bmFvrapDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").click()
    page.locator("#bmFvrapDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").fill("测试")
    page.locator("#bmRiskAgrId > .arco-form-item-content-wrapper > .arco-form-item-content > .arco-grid > .arco-grid-item > .arco-upload > span > .upload-card > .upload-icon-text").click()
    page.locator("body").set_input_files("供应链金融测试报告V1.6.0.pdf")
    page.locator("#bmClba").get_by_text("是").click()
    page.locator("#bmClba").get_by_text("否").click()
    page.locator("#bmClbaDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").click()
    page.locator("#bmClbaDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").fill("测试")
    page.locator("#bmLegAgrId > .arco-form-item-content-wrapper > .arco-form-item-content > .arco-grid > .arco-grid-item > .arco-upload > span > .upload-card > .upload-icon-text").click()
    page.locator("body").set_input_files("供应链金融测试报告V1.6.0.pdf")
    page.locator("#bmCfia").get_by_text("是").click()
    page.locator("#bmCfia").get_by_text("否").click()
    page.locator("#bmCfiaDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").click()
    page.locator("#bmCfiaDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").fill("测试")
    page.locator("#bmAmlct").get_by_text("是").click()
    page.locator("#bmAmlct").get_by_text("否").click()
    page.locator("#bmAmlctDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").click()
    page.locator("#bmAmlctDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").fill("测试")
    page.locator("#bmSscdm").get_by_text("是").click()
    page.locator("#bmSscdm").get_by_text("否").click()
    page.locator("#bmSscdmDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").click()
    page.locator("#bmSscdmDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").fill("测试")
    page.get_by_role("button", name="下一步").click()
    page.locator("#bplDbqav").get_by_text("是").click()
    page.locator("#bplDbqav").get_by_text("否").click()
    page.locator("#bplDbqavDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").click()
    page.locator("#bplDbqavDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").fill("测试")
    page.locator("#bplSupportId").get_by_text("点击此处上传附件").click()
    page.locator("body").set_input_files("供应链金融测试报告V1.6.0.pdf")
    page.locator("#bplRuivr").get_by_text("是").click()
    page.locator("#bplRuivr").get_by_text("否").click()
    page.locator("#bplRuivrDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").click()
    page.locator("#bplRuivrDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").fill("测试")
    page.locator("#bplProcManId > .arco-form-item-content-wrapper > .arco-form-item-content > .arco-grid > .arco-grid-item > .arco-upload > span > .upload-card > .upload-icon-text").click()
    page.locator("body").set_input_files("供应链金融测试报告V1.6.0.pdf")
    page.locator("#bplFclb").get_by_text("是").click()
    page.locator("#bplFclb").get_by_text("否").click()
    page.locator("#bplFclbDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").fill("测试")
    page.locator("#bplClearDocId").get_by_text("点击此处上传附件").click()
    page.locator("body").set_input_files("供应链金融测试报告V1.6.0.pdf")
    page.get_by_role("button", name="下一步").click()
    page.locator("#opCredIssuId > .arco-form-item-content-wrapper > .arco-form-item-content > .arco-grid > .arco-grid-item > .arco-upload > span > .upload-card > .upload-icon-text").click()
    page.locator("body").set_input_files("供应链金融测试报告V1.6.0.pdf")
    page.locator("#opUtbdi").get_by_text("是").click()
    page.locator("#opUtbdi").get_by_text("否").click()
    page.locator("#opUtbdiDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").click()
    page.locator("#opUtbdiDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").fill("测试")
    page.locator("#opTradeBgId > .arco-form-item-content-wrapper > .arco-form-item-content > .arco-grid > .arco-grid-item > .arco-upload > span > .upload-card > .upload-icon-text").click()
    page.locator("body").set_input_files("供应链金融测试报告V1.6.0.pdf")
    page.locator("#opCceiq").get_by_text("是").click()
    page.locator("#opCceiq").get_by_text("否").click()
    page.locator("#opCceiqDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").click()
    page.locator("#opCceiqDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").fill("测试")
    page.locator("#opReiar").get_by_text("是").click()
    page.locator("#opReiar").get_by_text("否").click()
    page.locator("#opReiarDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").fill("测试")
    page.locator("#opOverDataId > .arco-form-item-content-wrapper > .arco-form-item-content > .arco-grid > .arco-grid-item > .arco-upload > span > .upload-card > .upload-icon-text").click()
    page.locator("body").set_input_files("供应链金融测试报告V1.6.0.pdf")
    page.get_by_role("button", name="下一步").click()
    page.locator("#tpCtr").get_by_text("是").click()
    page.locator("#tpCtr").get_by_text("否").click()
    page.get_by_role("textbox", name="情况说明（必填），最多1000字符").first.click()
    page.get_by_role("textbox", name="情况说明（必填），最多1000字符").first.fill("测试")
    page.locator("#tpCredTransId").get_by_text("点击此处上传附件").click()
    page.locator("body").set_input_files("供应链金融测试报告V1.6.0.pdf")
    page.locator("#tpNdpt").get_by_text("是").click()
    page.locator("#tpNdpt").get_by_text("否").click()
    page.locator("#tpNdptDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").click()
    page.locator("#tpNdptDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").fill("测试")
    page.locator("#tpTransNotiId").get_by_text("点击此处上传附件").click()
    page.locator("body").set_input_files("供应链金融测试报告V1.6.0.pdf")
    page.locator("#tpErt5l > .arco-form-item-content-wrapper > .arco-form-item-content > .arco-radio-group > label > .arco-tag").first.click()
    page.locator("#tpErt5l > .arco-form-item-content-wrapper > .arco-form-item-content > .arco-radio-group > label:nth-child(2) > .arco-tag").click()
    page.get_by_role("textbox", name="情况说明（必填），最多1000字符").nth(2).click()
    page.get_by_role("textbox", name="情况说明（必填），最多1000字符").nth(2).fill("测试")
    page.locator("#tpTrans5lvId > .arco-form-item-content-wrapper > .arco-form-item-content > .arco-grid > .arco-grid-item > .arco-upload > span > .upload-card > .upload-icon-text").click()
    page.locator("body").set_input_files("供应链金融测试报告V1.6.0.pdf")
    page.get_by_role("button", name="下一步").click()
    page.locator("#fpUtbdf").get_by_text("是").click()
    page.locator("#fpUtbdf").get_by_text("否").click()
    page.locator("#fpUtbdfDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").click()
    page.locator("#fpUtbdfDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").fill("测试")
    page.locator("#fpTradeBgId > .arco-form-item-content-wrapper > .arco-form-item-content > .arco-grid > .arco-grid-item > .arco-upload > span > .upload-card > .upload-icon-text").click()
    page.locator("body").set_input_files("供应链金融测试报告V1.6.0.pdf")
    page.locator("#fpMpft").get_by_text("是").click()
    page.locator("#fpMpft").get_by_text("否").click()
    page.locator("#fpMpftDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").click()
    page.locator("#fpMpftDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").fill("测试")
    page.locator("#fpSysScreenId").get_by_text("点击此处上传附件").click()
    page.locator("body").set_input_files("供应链金融测试报告V1.6.0.pdf")
    page.locator("#fpCredFinId > .arco-form-item-content-wrapper > .arco-form-item-content > .arco-grid > .arco-grid-item > .arco-upload > span > .upload-card > .upload-icon-text").click()
    page.locator("body").set_input_files("供应链金融测试报告V1.6.0.pdf")
    page.locator("#fpNffli").get_by_text("是").click()
    page.locator("#fpNffli").get_by_text("否").click()
    page.locator("#fpNffliDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").fill("测试")
    page.get_by_role("button", name="下一步").click()
    page.locator("#ppPrmer").get_by_text("是").click()
    page.locator("#ppPrmer").get_by_text("否").click()
    page.locator("#ppPrmerDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").click()
    page.locator("#ppPrmerDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").fill("测试")
    page.locator("#ppPayNoticeId").get_by_text("点击此处上传附件").click()
    page.locator("body").set_input_files("供应链金融测试报告V1.6.0.pdf")
    page.locator("#ppSnidd").get_by_text("是").click()
    page.locator("#ppSnidd").get_by_text("否").click()
    page.locator("#ppSniddDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").fill("测试")
    page.get_by_role("button", name="下一步").click()
    page.locator("#fmTfp").get_by_text("是").click()
    page.locator("#fmTfp").get_by_text("否").click()
    page.locator("#fmTfpDes > .arco-form-item-content-wrapper > .arco-form-item-content > .arco-textarea-wrapper > .arco-textarea").click()
    page.locator(".arco-textarea-wrapper.arco-textarea-focus > .arco-textarea").fill("测试")
    page.get_by_text("点击此处上传附件").nth(1).click()
    page.locator("body").set_input_files("供应链金融测试报告V1.6.0.pdf")
    page.locator("#fmNfspd").get_by_text("是").click()
    page.locator("#fmNfspd").get_by_text("否").click()
    page.locator("#fmNfspdDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").click()
    page.locator("#fmNfspdDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").fill("测试")
    page.locator("#fmNufur").get_by_text("是").click()
    page.locator("#fmNufur").get_by_text("否").click()
    page.locator("#fmNufurDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").click()
    page.locator("#fmNufurDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").fill("测试")
    page.locator("#fmSsfcf").get_by_text("是").click()
    page.locator("#fmSsfcf").get_by_text("否").click()
    page.locator("#fmSsfcfDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").click()
    page.locator("#fmSsfcfDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").fill("测试")
    page.locator("#fmCfofp").get_by_text("是").click()
    page.locator("#fmCfofp").get_by_text("否").click()
    page.locator("#fmCfofpDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").click()
    page.locator("#fmCfofpDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").fill("测试")
    page.locator("#fmFrsme").get_by_text("是").click()
    page.locator("#fmFrsme").get_by_text("否").click()
    page.locator("#fmFrsmeDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").click()
    page.locator("#fmFrsmeDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").fill("测试")
    page.locator("#fmSMEFeeId").get_by_text("点击此处上传附件").click()
    page.locator("body").set_input_files("供应链金融测试报告V1.6.0.pdf")
    page.locator("#fmDfpt").get_by_text("是").click()
    page.locator("#fmDfpt").get_by_text("否").click()
    page.locator("#fmDfptDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").click()
    page.locator("#fmDfptDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").fill("测试")
    page.locator("#fmNuf").get_by_text("是").click()
    page.locator("#fmNuf").get_by_text("否").click()
    page.locator("#fmNufDes > .arco-form-item-content-wrapper > .arco-form-item-content > .arco-textarea-wrapper > .arco-textarea").fill("测试")
    page.get_by_role("button", name="下一步").click()
    page.locator("#irSirr").get_by_text("是").click()
    page.locator("#irSirr").get_by_text("否").click()
    page.locator("#irSirrDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").fill("测试")
    page.locator("#irSiid").get_by_text("是").click()
    page.locator("#irSiid").get_by_text("否").click()
    page.locator("#irSiidDes").get_by_role("textbox", name="情况说明（必填），最多1000字符").fill("测试")
    page.get_by_role("button", name="下一步").click()
    page.locator("#irmRecLetterIds").get_by_text("点击此处上传附件").click()
    page.locator("body").set_input_files("供应链金融测试报告V1.6.0.pdf")
    page.locator("#irmRecLetterIds").get_by_text("点击此处上传附件").click()
    page.locator("body").set_input_files("供应链金融测试报告V1.6.0.pdf")
    page.locator("#irmRecLetterIds").get_by_text("点击此处上传附件").click()
    page.locator("body").set_input_files("供应链金融测试报告V1.6.0.pdf")
    page.locator("#irmRecLetterIds").get_by_text("点击此处上传附件").click()
    page.locator("body").set_input_files("供应链金融测试报告V1.6.0.pdf")
    page.locator("#omOtherSuppId").get_by_text("点击此处上传附件").click()
    page.locator("body").set_input_files("供应链金融测试报告V1.6.0.pdf")
    page.locator("#omOtherSuppId").get_by_text("供应链金融测试报告V1.6.0.pdf").click()
    page.locator("#omOtherSuppId > .arco-form-item-content-wrapper > .arco-form-item-content > .arco-grid > .arco-grid-item > .upload-card").click()
    page.get_by_role("textbox", name="请选择专业机构").click()
    page.get_by_text("天津中互金数据科技有限公司").click()
    page.get_by_role("button", name="保存").click()

    # ---------------------
    context.close()
    browser.close()


with sync_playwright() as playwright:
    run(playwright)
