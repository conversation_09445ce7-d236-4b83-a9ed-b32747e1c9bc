{"name": "test_standard_user_login_success[chromium]", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: Page.goto: net::ERR_CONNECTION_TIMED_OUT at http://*************:8080/login\nCall log:\n  - navigating to \"http://*************:8080/login\", waiting until \"domcontentloaded\"", "trace": "test_scf_login.py:37: in test_standard_user_login_success\n    workflow._login_to_system(credentials, scf_base_url)\n..\\..\\..\\workflows\\scf_registration_workflow.py:113: in _login_to_system\n    self.login_page.navigate_to_login(base_url)\n..\\..\\..\\pages\\scf_login_page.py:51: in navigate_to_login\n    self.navigate(full_url)\n..\\..\\..\\pages\\base_page.py:31: in navigate\n    self.page.goto(url, wait_until=wait_until, timeout=self.default_timeout)\n..\\..\\..\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py:9002: in goto\n    self._sync(\n..\\..\\..\\.venv\\Lib\\site-packages\\playwright\\_impl\\_page.py:556: in goto\n    return await self._main_frame.goto(**locals_to_params(locals()))\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n..\\..\\..\\.venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py:146: in goto\n    await self._channel.send(\n..\\..\\..\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:69: in send\n    return await self._connection.wrap_api_call(\n..\\..\\..\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:558: in wrap_api_call\n    raise rewrite_error(error, f\"{parsed_st['apiName']}: {error}\") from None\nE   playwright._impl._errors.Error: Page.goto: net::ERR_CONNECTION_TIMED_OUT at http://*************:8080/login\nE   Call log:\nE     - navigating to \"http://*************:8080/login\", waiting until \"domcontentloaded\""}, "description": "测试标准用户成功登录", "steps": [{"name": "执行标准用户登录", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: Page.goto: net::ERR_CONNECTION_TIMED_OUT at http://*************:8080/login\nCall log:\n  - navigating to \"http://*************:8080/login\", waiting until \"domcontentloaded\"\n\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\tests\\ui\\smoke\\test_scf_login.py\", line 37, in test_standard_user_login_success\n    workflow._login_to_system(credentials, scf_base_url)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\workflows\\scf_registration_workflow.py\", line 113, in _login_to_system\n    self.login_page.navigate_to_login(base_url)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 51, in navigate_to_login\n    self.navigate(full_url)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 31, in navigate\n    self.page.goto(url, wait_until=wait_until, timeout=self.default_timeout)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 9002, in goto\n    self._sync(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 115, in _sync\n    return task.result()\n           ^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_page.py\", line 556, in goto\n    return await self._main_frame.goto(**locals_to_params(locals()))\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py\", line 146, in goto\n    await self._channel.send(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 69, in send\n    return await self._connection.wrap_api_call(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 558, in wrap_api_call\n    raise rewrite_error(error, f\"{parsed_st['apiName']}: {error}\") from None\n"}, "steps": [{"name": "登录系统", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: Page.goto: net::ERR_CONNECTION_TIMED_OUT at http://*************:8080/login\nCall log:\n  - navigating to \"http://*************:8080/login\", waiting until \"domcontentloaded\"\n\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\workflows\\scf_registration_workflow.py\", line 113, in _login_to_system\n    self.login_page.navigate_to_login(base_url)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 51, in navigate_to_login\n    self.navigate(full_url)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 31, in navigate\n    self.page.goto(url, wait_until=wait_until, timeout=self.default_timeout)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 9002, in goto\n    self._sync(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 115, in _sync\n    return task.result()\n           ^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_page.py\", line 556, in goto\n    return await self._main_frame.goto(**locals_to_params(locals()))\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py\", line 146, in goto\n    await self._channel.send(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 69, in send\n    return await self._connection.wrap_api_call(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 558, in wrap_api_call\n    raise rewrite_error(error, f\"{parsed_st['apiName']}: {error}\") from None\n"}, "steps": [{"name": "导航到登录页面", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: Page.goto: net::ERR_CONNECTION_TIMED_OUT at http://*************:8080/login\nCall log:\n  - navigating to \"http://*************:8080/login\", waiting until \"domcontentloaded\"\n\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 51, in navigate_to_login\n    self.navigate(full_url)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 31, in navigate\n    self.page.goto(url, wait_until=wait_until, timeout=self.default_timeout)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 9002, in goto\n    self._sync(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 115, in _sync\n    return task.result()\n           ^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_page.py\", line 556, in goto\n    return await self._main_frame.goto(**locals_to_params(locals()))\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py\", line 146, in goto\n    await self._channel.send(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 69, in send\n    return await self._connection.wrap_api_call(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 558, in wrap_api_call\n    raise rewrite_error(error, f\"{parsed_st['apiName']}: {error}\") from None\n"}, "steps": [{"name": "导航到页面: 'http://*************:8080/login'", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: Page.goto: net::ERR_CONNECTION_TIMED_OUT at http://*************:8080/login\nCall log:\n  - navigating to \"http://*************:8080/login\", waiting until \"domcontentloaded\"\n\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 31, in navigate\n    self.page.goto(url, wait_until=wait_until, timeout=self.default_timeout)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 9002, in goto\n    self._sync(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 115, in _sync\n    return task.result()\n           ^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_page.py\", line 556, in goto\n    return await self._main_frame.goto(**locals_to_params(locals()))\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py\", line 146, in goto\n    await self._channel.send(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 69, in send\n    return await self._connection.wrap_api_call(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 558, in wrap_api_call\n    raise rewrite_error(error, f\"{parsed_st['apiName']}: {error}\") from None\n"}, "parameters": [{"name": "url", "value": "'http://*************:8080/login'"}, {"name": "wait_until", "value": "'domcontentloaded'"}], "start": 1751851489877, "stop": 1751851510913}], "parameters": [{"name": "base_url", "value": "'http://*************:8080'"}], "start": 1751851489876, "stop": 1751851510913}], "parameters": [{"name": "credentials", "value": "{'username': 'scf_4nuioc', 'password': 'Scf123456.', 'description': '标准测试用户'}"}, {"name": "base_url", "value": "'http://*************:8080'"}], "start": 1751851489875, "stop": 1751851510914}], "start": 1751851489875, "stop": 1751851510914}], "attachments": [{"name": "失败截图", "source": "87892d22-68c2-4d61-a70a-b8036bb5ac89-attachment.png", "type": "image/png"}, {"name": "页面HTML", "source": "35534ccc-8847-412b-9346-68a3c1a5c87a-attachment.html", "type": "text/html"}, {"name": "当前URL", "source": "43b0c33b-d35c-4a6d-84e8-8d7a81809e49-attachment.txt", "type": "text/plain"}, {"name": "log", "source": "54f85882-bb72-4ed5-aefd-de7ae001816a-attachment.txt", "type": "text/plain"}, {"name": "stderr", "source": "a829d52b-b000-42ac-bbe0-a20d1d849235-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "browser_name", "value": "'chromium'"}], "start": 1751851489832, "stop": 1751851510915, "uuid": "2cd4d68d-f69c-45d8-8946-9e51e1cb6b19", "historyId": "83c32d878021effc5e3dcc4d134c4bd1", "testCaseId": "2cf6f352a9af813bd18233473cb242da", "fullName": "tests.ui.smoke.test_scf_login.TestSCFLogin#test_standard_user_login_success", "labels": [{"name": "severity", "value": "blocker"}, {"name": "feature", "value": "用户登录"}, {"name": "epic", "value": "供应链金融备案系统"}, {"name": "story", "value": "标准用户登录"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "tests.ui.smoke"}, {"name": "suite", "value": "test_scf_login"}, {"name": "subSuite", "value": "TestSC<PERSON><PERSON>in"}, {"name": "host", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "thread", "value": "24864-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.ui.smoke.test_scf_login"}]}