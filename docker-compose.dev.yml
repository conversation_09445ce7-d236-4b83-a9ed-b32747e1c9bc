# 开发环境 Docker Compose 覆盖配置
# 用于本地开发和调试

version: '3.8'

services:
  playwright-tests:
    # 开发模式下不自动运行测试
    command: >
      sh -c "
        echo 'Development mode - container ready for manual testing' &&
        tail -f /dev/null
      "
    environment:
      # 开发环境变量
      - TEST_ENV=dev
      - DEBUG=true
      - LOG_LEVEL=DEBUG
    # 开发模式下保持容器运行
    stdin_open: true
    tty: true
    # 端口映射（用于调试）
    ports:
      - "9229:9229"  # Node.js 调试端口（如果需要）

  test-app:
    # 开发模式下的应用配置
    environment:
      - ENV=development
    # 挂载开发用的测试页面
    volumes:
      - ./test-app:/usr/share/nginx/html:rw  # 读写模式，方便修改

  allure-server:
    # 开发模式下更频繁地检查结果
    environment:
      CHECK_RESULTS_EVERY_SECONDS: 1
      KEEP_HISTORY: 5
