# SCF 备案流程自动化测试指南

## 概述

本文档介绍 SCF（供应链金融）备案流程自动化测试套件的使用方法和架构设计。

## 项目结构

```
playwright-python-template/
├── pages/                          # 页面对象模式 (POM)
│   ├── scf_login_page.py           # 登录页面
│   ├── scf_main_page.py            # 主页面
│   └── scf_filing_page.py          # 备案申请页面
├── workflows/                       # 业务流程封装
│   └── scf_filing_workflow.py     # 备案流程工作流
├── components/                      # 组件和工具
│   └── filing_data_manager.py     # 备案数据管理器
├── tests/ui/filing/                # 备案测试用例
│   ├── conftest.py                 # 测试配置
│   ├── test_scf_filing_normal.py   # 正常流程测试
│   ├── test_scf_filing_exceptions.py # 异常场景测试
│   └── test_scf_filing_boundary.py # 边界值测试
├── fixtures/                       # 测试夹具
│   └── filing_fixtures.py         # 备案专用夹具
├── test_data/                      # 测试数据
│   └── files/                      # 测试文件
└── run_filing_tests.py            # 测试运行脚本
```

## 快速开始

### 1. 环境检查

```bash
# 检查测试环境
python run_filing_tests.py --check
```

### 2. 运行测试

```bash
# 运行所有备案测试（显示界面）
python run_filing_tests.py

# 运行特定类型的测试
python run_filing_tests.py --type normal    # 正常流程测试
python run_filing_tests.py --type exception # 异常场景测试
python run_filing_tests.py --type boundary  # 边界值测试
python run_filing_tests.py --type smoke     # 冒烟测试

# 无界面模式运行
python run_filing_tests.py --headless

# 使用不同浏览器
python run_filing_tests.py --browser firefox
python run_filing_tests.py --browser webkit
```

### 3. 查看测试报告

测试完成后会自动生成 Allure 报告并在浏览器中打开。

## 测试场景

### 正常流程测试 (test_scf_filing_normal.py)

- **完整备案流程**: 测试从登录到提交的完整流程
- **基础材料上传**: 测试基础文档上传功能
- **补充材料上传**: 测试补充材料上传功能
- **合规性检查**: 测试合规性检查表单填写
- **专业机构选择**: 测试专业机构选择功能
- **申请保存**: 测试申请保存功能
- **多文件上传**: 测试多文件上传功能

### 异常场景测试 (test_scf_filing_exceptions.py)

- **必填字段验证**: 测试必填字段为空的验证
- **无效文件格式**: 测试不支持的文件格式上传
- **文件大小限制**: 测试超出大小限制的文件
- **网络中断处理**: 测试网络异常情况处理
- **重复提交处理**: 测试重复提交的处理
- **会话超时处理**: 测试会话超时的处理
- **无效数据输入**: 测试各种无效数据输入
- **权限验证**: 测试权限控制

### 边界值测试 (test_scf_filing_boundary.py)

- **字符长度边界**: 测试输入框的最小/最大长度
- **文件大小边界**: 测试文件大小的边界值
- **数值范围边界**: 测试数值输入的边界值
- **并发操作边界**: 测试并发操作的处理
- **浏览器兼容性**: 测试不同窗口尺寸的兼容性
- **时间边界条件**: 测试时间相关的边界条件

## 测试数据管理

### 数据生成

测试数据通过 `FilingDataManager` 自动生成，支持多种场景：

- **normal**: 正常测试数据
- **boundary**: 边界值测试数据
- **invalid**: 无效测试数据
- **edge_case**: 边缘情况测试数据

### 数据隔离

每个测试用例都有独立的测试数据，通过会话ID进行隔离，确保测试之间不会相互影响。

### 数据清理

测试完成后自动清理过期的测试数据，保持测试环境的整洁。

## 页面对象模式 (POM)

### SCFLoginPage
- 登录功能
- 验证码处理
- 登录状态验证

### SCFMainPage
- 主页面导航
- 备案登记入口
- 用户信息管理

### SCFFilingPage
- 备案申请表单
- 文件上传功能
- 步骤导航
- 合规性检查

## 工作流封装

### SCFFilingWorkflow
- 完整备案流程编排
- 错误处理和重试
- 性能监控
- 结果验证

## 企业级特性

### 日志管理
- 统一的日志格式
- 分级日志记录
- 错误追踪

### 错误处理
- 自动重试机制
- 优雅降级
- 详细错误信息

### 性能监控
- 执行时间监控
- 性能检查点
- 性能报告生成

### 测试报告
- Allure 集成
- 自动截图
- 详细步骤记录

## 配置说明

### 测试配置 (tests/ui/filing/conftest.py)

```python
FILING_TEST_CONFIG = {
    "base_url": "http://172.18.12.128:8080",
    "timeout": {
        "default": 30000,
        "file_upload": 60000,
        "form_submit": 45000
    },
    "retry": {
        "max_attempts": 3,
        "delay": 2000
    }
}
```

### 浏览器配置 (pyproject.toml)

```toml
[tool.pytest.ini_options]
addopts = [
    "--headed",        # 显示浏览器界面
    "--slowmo=500"     # 慢速执行，便于观察
]
```

## 最佳实践

### 1. 测试编写
- 遵循 POM 模式
- 使用 Allure 注解
- 添加详细的测试步骤
- 包含错误处理

### 2. 数据管理
- 使用 Faker 生成测试数据
- 确保数据隔离
- 及时清理测试数据

### 3. 错误处理
- 添加重试机制
- 保存失败截图
- 记录详细日志

### 4. 性能优化
- 合理使用等待
- 避免硬编码延迟
- 监控执行时间

## 故障排除

### 常见问题

1. **测试文件缺失**
   ```bash
   # 检查测试文件
   python run_filing_tests.py --check
   ```

2. **服务器连接失败**
   - 检查服务器地址配置
   - 确认服务器状态
   - 检查网络连接

3. **验证码识别失败**
   - 检查 OCR 组件
   - 调整识别参数
   - 查看调试图片

4. **文件上传失败**
   - 检查文件路径
   - 确认文件格式
   - 验证文件大小

### 调试技巧

1. **启用详细日志**
   ```bash
   python run_filing_tests.py --type normal -v
   ```

2. **单步调试**
   ```bash
   PWDEBUG=1 python run_filing_tests.py --type normal
   ```

3. **保存调试信息**
   - 查看 `logs/` 目录下的日志文件
   - 检查 Allure 报告中的截图
   - 分析性能报告

## 扩展指南

### 添加新的测试场景

1. 在相应的测试文件中添加测试方法
2. 使用适当的 Allure 注解
3. 添加测试数据生成逻辑
4. 更新文档

### 添加新的页面对象

1. 继承 `BasePage` 类
2. 定义页面元素定位符
3. 实现页面操作方法
4. 添加页面验证方法

### 扩展工作流

1. 在 `SCFFilingWorkflow` 中添加新方法
2. 实现错误处理和重试
3. 添加性能监控
4. 更新测试用例

## 维护指南

### 定期维护任务

1. **更新测试数据**
   - 清理过期数据
   - 更新测试文件
   - 验证数据有效性

2. **检查页面元素**
   - 验证元素定位符
   - 更新页面结构变化
   - 测试新功能

3. **性能优化**
   - 分析执行时间
   - 优化等待策略
   - 减少不必要的操作

4. **报告分析**
   - 分析测试结果趋势
   - 识别常见失败原因
   - 优化测试稳定性

## 联系方式

如有问题或建议，请联系测试团队或查看项目文档。
