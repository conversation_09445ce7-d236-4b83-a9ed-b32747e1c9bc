"""
供应链金融备案系统登录功能冒烟测试
SCF Login Smoke Tests
"""

import pytest
import allure
from workflows.scf_registration_workflow import SCFRegistrationWorkflow


@allure.epic("供应链金融备案系统")
@allure.feature("用户登录")
class TestSCFLogin:
    """供应链金融系统登录功能测试类"""
    
    @allure.story("标准用户登录")
    @allure.severity(allure.severity_level.BLOCKER)
    @pytest.mark.smoke
    def test_standard_user_login_success(self, 
                                       page, 
                                       scf_base_url, 
                                       scf_login_credentials):
        """测试标准用户成功登录"""
        # 准备测试数据
        workflow = SCFRegistrationWorkflow(page)
        credentials = scf_login_credentials["standard_user"]
        
        # 执行登录流程
        with allure.step("执行标准用户登录"):
            workflow._login_to_system(credentials, scf_base_url)
        
        with allure.step("验证登录成功"):
            # 验证已不在登录页面
            current_url = workflow.get_current_url()
            assert "/login" not in current_url, f"登录失败，仍在登录页面: {current_url}"
    
    @allure.story("管理员用户登录")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def test_admin_user_login_success(self, 
                                    page, 
                                    scf_base_url, 
                                    scf_login_credentials):
        """测试管理员用户成功登录"""
        # 准备测试数据
        workflow = SCFRegistrationWorkflow(page)
        credentials = scf_login_credentials.get("admin_user", {})
        
        # 跳过测试如果没有管理员凭证
        if not credentials.get("username"):
            pytest.skip("管理员凭证未配置，跳过测试")
        
        # 执行登录流程
        with allure.step("执行管理员登录"):
            workflow._login_to_system(credentials, scf_base_url)
        
        with allure.step("验证管理员登录成功"):
            current_url = workflow.get_current_url()
            assert "/login" not in current_url, f"管理员登录失败: {current_url}"
    
    @allure.story("登录失败场景")
    @allure.severity(allure.severity_level.NORMAL)
    @pytest.mark.smoke
    @pytest.mark.parametrize("credentials_key,expected_behavior", [
        ("invalid_user", "应该显示错误信息"),
        ("empty_credentials", "应该提示输入用户名和密码"),
    ])
    def test_login_failure_scenarios(self, 
                                   page, 
                                   scf_base_url, 
                                   scf_login_credentials,
                                   credentials_key,
                                   expected_behavior):
        """测试登录失败场景"""
        # 准备测试数据
        workflow = SCFRegistrationWorkflow(page)
        credentials = scf_login_credentials.get(credentials_key, {})
        
        # 执行登录流程（预期失败）
        with allure.step(f"使用{credentials_key}凭证登录"):
            try:
                workflow._login_to_system(credentials, scf_base_url)
                # 如果没有抛出异常，检查是否仍在登录页面
                current_url = workflow.get_current_url()
                assert "/login" in current_url, f"预期登录失败但似乎成功了: {current_url}"
            except Exception as e:
                # 预期的登录失败
                allure.attach(str(e), "登录失败信息", allure.attachment_type.TEXT)
        
        with allure.step("验证登录失败行为"):
            # 验证仍在登录页面
            current_url = workflow.get_current_url()
            assert "/login" in current_url, f"{expected_behavior}，但当前URL: {current_url}"
    
    @allure.story("登录页面元素验证")
    @allure.severity(allure.severity_level.MINOR)
    @pytest.mark.component
    def test_login_page_elements(self, page, scf_base_url):
        """验证登录页面必要元素存在"""
        workflow = SCFRegistrationWorkflow(page)
        
        with allure.step("导航到登录页面"):
            workflow.login_page.navigate_to_login(scf_base_url)
        
        with allure.step("验证登录页面元素"):
            # 验证账号登录标签
            assert workflow.login_page.is_visible(workflow.login_page.account_tab), "账号登录标签应该可见"
            
            # 点击账号登录标签
            workflow.login_page.click_account_login_tab()
            
            # 验证输入框
            assert workflow.login_page.is_visible(workflow.login_page.username_input), "用户名输入框应该可见"
            assert workflow.login_page.is_visible(workflow.login_page.password_input), "密码输入框应该可见"
            assert workflow.login_page.is_visible(workflow.login_page.captcha_input), "验证码输入框应该可见"
            
            # 验证登录按钮
            assert workflow.login_page.is_visible(workflow.login_page.login_button), "登录按钮应该可见"
    
    @allure.story("验证码功能测试")
    @allure.severity(allure.severity_level.NORMAL)
    @pytest.mark.component
    def test_captcha_functionality(self, page, scf_base_url):
        """测试验证码功能"""
        workflow = SCFRegistrationWorkflow(page)
        
        with allure.step("导航到登录页面"):
            workflow.login_page.navigate_to_login(scf_base_url)
            workflow.login_page.click_account_login_tab()
        
        with allure.step("测试验证码刷新功能"):
            # 如果有验证码图片，测试刷新功能
            if workflow.login_page.is_visible(workflow.login_page.captcha_image):
                workflow.login_page.refresh_captcha()
                # 验证验证码输入框仍然可见
                assert workflow.login_page.is_visible(workflow.login_page.captcha_input), "刷新后验证码输入框应该仍然可见"
        
        with allure.step("测试验证码输入"):
            # 测试手动输入验证码
            workflow.login_page.input_captcha("1234")
            
            # 验证输入成功
            captcha_value = workflow.login_page.page.locator(workflow.login_page.captcha_input).input_value()
            assert captcha_value == "1234", "验证码输入应该成功"
