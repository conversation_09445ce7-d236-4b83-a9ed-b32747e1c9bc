{"name": "test_login_failure_scenarios[chromium-empty_credentials-\\u5e94\\u8be5\\u63d0\\u793a\\u8f93\\u5165\\u7528\\u6237\\u540d\\u548c\\u5bc6\\u7801]", "status": "failed", "statusDetails": {"message": "AssertionError: 应该提示输入用户名和密码，但当前URL: about:blank\nassert '/login' in 'about:blank'", "trace": "test_scf_login.py:100: in test_login_failure_scenarios\n    assert \"/login\" in current_url, f\"{expected_behavior}，但当前URL: {current_url}\"\nE   AssertionError: 应该提示输入用户名和密码，但当前URL: about:blank\nE   assert '/login' in 'about:blank'"}, "description": "测试登录失败场景", "steps": [{"name": "使用empty_credentials凭证登录", "status": "passed", "steps": [{"name": "登录系统", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: Page.goto: net::ERR_CONNECTION_TIMED_OUT at http://*************:8080/login\nCall log:\n  - navigating to \"http://*************:8080/login\", waiting until \"domcontentloaded\"\n\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\workflows\\scf_registration_workflow.py\", line 113, in _login_to_system\n    self.login_page.navigate_to_login(base_url)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 51, in navigate_to_login\n    self.navigate(full_url)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 31, in navigate\n    self.page.goto(url, wait_until=wait_until, timeout=self.default_timeout)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 9002, in goto\n    self._sync(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 115, in _sync\n    return task.result()\n           ^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_page.py\", line 556, in goto\n    return await self._main_frame.goto(**locals_to_params(locals()))\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py\", line 146, in goto\n    await self._channel.send(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 69, in send\n    return await self._connection.wrap_api_call(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 558, in wrap_api_call\n    raise rewrite_error(error, f\"{parsed_st['apiName']}: {error}\") from None\n"}, "steps": [{"name": "导航到登录页面", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: Page.goto: net::ERR_CONNECTION_TIMED_OUT at http://*************:8080/login\nCall log:\n  - navigating to \"http://*************:8080/login\", waiting until \"domcontentloaded\"\n\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 51, in navigate_to_login\n    self.navigate(full_url)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 31, in navigate\n    self.page.goto(url, wait_until=wait_until, timeout=self.default_timeout)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 9002, in goto\n    self._sync(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 115, in _sync\n    return task.result()\n           ^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_page.py\", line 556, in goto\n    return await self._main_frame.goto(**locals_to_params(locals()))\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py\", line 146, in goto\n    await self._channel.send(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 69, in send\n    return await self._connection.wrap_api_call(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 558, in wrap_api_call\n    raise rewrite_error(error, f\"{parsed_st['apiName']}: {error}\") from None\n"}, "steps": [{"name": "导航到页面: 'http://*************:8080/login'", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: Page.goto: net::ERR_CONNECTION_TIMED_OUT at http://*************:8080/login\nCall log:\n  - navigating to \"http://*************:8080/login\", waiting until \"domcontentloaded\"\n\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 31, in navigate\n    self.page.goto(url, wait_until=wait_until, timeout=self.default_timeout)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 9002, in goto\n    self._sync(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 115, in _sync\n    return task.result()\n           ^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_page.py\", line 556, in goto\n    return await self._main_frame.goto(**locals_to_params(locals()))\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py\", line 146, in goto\n    await self._channel.send(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 69, in send\n    return await self._connection.wrap_api_call(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 558, in wrap_api_call\n    raise rewrite_error(error, f\"{parsed_st['apiName']}: {error}\") from None\n"}, "parameters": [{"name": "url", "value": "'http://*************:8080/login'"}, {"name": "wait_until", "value": "'domcontentloaded'"}], "start": 1751851597313, "stop": 1751851618346}], "parameters": [{"name": "base_url", "value": "'http://*************:8080'"}], "start": 1751851597311, "stop": 1751851618347}], "parameters": [{"name": "credentials", "value": "{'username': '', 'password': ''}"}, {"name": "base_url", "value": "'http://*************:8080'"}], "start": 1751851597310, "stop": 1751851618347}], "attachments": [{"name": "登录失败信息", "source": "b80921d1-707d-4b69-a9fc-39f68d4da578-attachment.txt", "type": "text/plain"}], "start": 1751851597310, "stop": 1751851618357}, {"name": "验证登录失败行为", "status": "failed", "statusDetails": {"message": "AssertionError: 应该提示输入用户名和密码，但当前URL: about:blank\nassert '/login' in 'about:blank'\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\tests\\ui\\smoke\\test_scf_login.py\", line 100, in test_login_failure_scenarios\n    assert \"/login\" in current_url, f\"{expected_behavior}，但当前URL: {current_url}\"\n"}, "start": 1751851618357, "stop": 1751851618358}], "attachments": [{"name": "失败截图", "source": "859a1f4a-6f9d-45b5-b042-3c604e687dff-attachment.png", "type": "image/png"}, {"name": "页面HTML", "source": "ae4d8338-0557-4fc6-9f33-c4b2cabf3d85-attachment.html", "type": "text/html"}, {"name": "当前URL", "source": "e76670cc-ef0c-42e6-a3bb-0b7272d85976-attachment.txt", "type": "text/plain"}, {"name": "log", "source": "c1bee0a3-a973-4673-9d10-cfc58d1401a4-attachment.txt", "type": "text/plain"}, {"name": "stderr", "source": "8518e226-d01f-4510-b890-7c70725f4fe5-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "browser_name", "value": "'chromium'"}, {"name": "credentials_key", "value": "'empty_credentials'"}, {"name": "expected_behavior", "value": "'应该提示输入用户名和密码'"}], "start": 1751851597194, "stop": 1751851618359, "uuid": "eb5bb201-ae00-4f9e-928c-7dc1277042cb", "historyId": "d370f9b387261f6b14d16c225a143019", "testCaseId": "a99cc8e5e6e05ba82a9dc1ae7049de1b", "fullName": "tests.ui.smoke.test_scf_login.TestSCFLogin#test_login_failure_scenarios", "labels": [{"name": "feature", "value": "用户登录"}, {"name": "story", "value": "登录失败场景"}, {"name": "severity", "value": "normal"}, {"name": "epic", "value": "供应链金融备案系统"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "tests.ui.smoke"}, {"name": "suite", "value": "test_scf_login"}, {"name": "subSuite", "value": "TestSC<PERSON><PERSON>in"}, {"name": "host", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "thread", "value": "24864-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.ui.smoke.test_scf_login"}]}