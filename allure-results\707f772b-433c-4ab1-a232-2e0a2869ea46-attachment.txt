[32mINFO    [0m fixtures.scf_fixtures:scf_fixtures.py:32 加载SCF系统配置成功
[32mINFO    [0m fixtures.scf_fixtures:scf_fixtures.py:52 加载SCF测试数据成功
[32mINFO    [0m fixtures.scf_fixtures:scf_fixtures.py:79 准备SCF登录凭证
[32mINFO    [0m components.ocr_util:ocr_util.py:60 OCR引擎初始化成功
[32mINFO    [0m SCFRegistrationWorkflow:scf_registration_workflow.py:110 开始登录系统
[32mINFO    [0m SCFLoginPage:scf_login_page.py:50 导航到登录页面: http://172.18.12.128:8080/login
[32mINFO    [0m pages.base_page:base_page.py:30 导航到页面: http://172.18.12.128:8080/login
[32mINFO    [0m SCFLoginPage:scf_login_page.py:154 开始登录流程，用户名: scf_4nuioc
[32mINFO    [0m SCFLoginPage:scf_login_page.py:57 点击账号登录标签
[35mDEBUG   [0m components.retry_util:retry_util.py:87 执行函数 click，尝试 1/3
[32mINFO    [0m pages.base_page:base_page.py:106 点击元素: text=账号登录
[35mDEBUG   [0m pages.base_page:base_page.py:65 元素 text=账号登录 已稳定
[32mINFO    [0m SCFLoginPage:scf_login_page.py:69 输入用户名: scf_4nuioc
[35mDEBUG   [0m components.retry_util:retry_util.py:87 执行函数 fill，尝试 1/3
[32mINFO    [0m pages.base_page:base_page.py:130 填充文本到元素 role=textbox[name='请输入账号']: scf_4nuioc
[35mDEBUG   [0m pages.base_page:base_page.py:65 元素 role=textbox[name='请输入账号'] 已稳定
[32mINFO    [0m SCFLoginPage:scf_login_page.py:81 输入密码
[35mDEBUG   [0m components.retry_util:retry_util.py:87 执行函数 fill，尝试 1/3
[32mINFO    [0m pages.base_page:base_page.py:130 填充文本到元素 role=textbox[name='请输入密码']: Scf123456.
[35mDEBUG   [0m pages.base_page:base_page.py:65 元素 role=textbox[name='请输入密码'] 已稳定
[32mINFO    [0m SCFLoginPage:scf_login_page.py:106 开始自动识别验证码
[32mINFO    [0m pages.base_page:base_page.py:190 等待元素 .captcha-image 状态: visible
[1m[31mERROR   [0m SCFLoginPage:scf_login_page.py:131 自动识别验证码失败: Locator.wait_for: Timeout 30000ms exceeded.
Call log:
  - waiting for locator(".captcha-image") to be visible

[33mWARNING [0m SCFLoginPage:scf_login_page.py:172 自动识别验证码失败，需要手动处理: 'SCFLoginPage' object has no attribute 'take_screenshot_for_report'