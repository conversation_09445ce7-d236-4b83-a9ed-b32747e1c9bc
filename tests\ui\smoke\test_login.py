"""
登录功能冒烟测试
验证基本登录功能是否正常工作
"""

import pytest
import allure
from workflows.login_workflow import LoginWorkflow


@allure.epic("用户认证")
@allure.feature("用户登录")
class TestLogin:
    """登录功能测试类"""

    @allure.story("标准用户登录")
    @allure.severity(allure.severity_level.BLOCKER)
    @pytest.mark.smoke
    def test_standard_user_login_success(self, page, env_config, test_credentials):
        """测试标准用户成功登录"""
        # 准备测试数据
        login_workflow = LoginWorkflow(page)
        base_url = env_config["url"]
        username = test_credentials["standard_user"]["username"]
        password = test_credentials["standard_user"]["password"]

        # 执行测试步骤
        with allure.step("导航到登录页面"):
            page.goto(base_url)

        with allure.step("执行登录操作"):
            login_workflow.login(username, password)

        with allure.step("验证登录成功"):
            # 这里需要根据实际应用调整验证逻辑
            # login_workflow.verify_login_success(
            #     expected_url_pattern="/dashboard",
            #     expected_element="#user-menu"
            # )
            pass  # 临时占位，实际测试时需要添加具体验证

    @allure.story("管理员用户登录")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def test_admin_user_login_success(self, page, env_config, test_credentials):
        """测试管理员用户成功登录"""
        # 准备测试数据
        login_workflow = LoginWorkflow(page)
        base_url = env_config["url"]
        username = test_credentials.get("admin_user", {}).get("username", "admin")
        password = test_credentials.get("admin_user", {}).get("password", "admin_password")

        # 执行测试步骤
        with allure.step("导航到登录页面"):
            page.goto(base_url)

        with allure.step("执行管理员登录"):
            login_workflow.login(username, password)

        with allure.step("验证管理员登录成功"):
            # 这里需要根据实际应用调整验证逻辑
            pass  # 临时占位

    @allure.story("登录失败场景")
    @allure.severity(allure.severity_level.NORMAL)
    @pytest.mark.smoke
    @pytest.mark.parametrize("username,password,expected_error", [
        ("invalid_user", "invalid_pass", "用户名或密码错误"),
        ("", "", "请输入用户名和密码"),
        ("valid_user", "", "请输入密码"),
        ("", "valid_pass", "请输入用户名"),
    ])
    def test_login_with_invalid_credentials(self, page, env_config, username, password, expected_error):
        """测试无效凭证登录失败"""
        # 准备测试数据
        login_workflow = LoginWorkflow(page)
        base_url = env_config["url"]

        # 执行测试步骤
        with allure.step("导航到登录页面"):
            page.goto(base_url)

        with allure.step(f"使用无效凭证登录: {username}/{password}"):
            login_workflow.login(username, password)

        with allure.step("验证登录失败"):
            # 这里需要根据实际应用调整验证逻辑
            # login_workflow.verify_login_failure(
            #     error_message_locator=".error-message",
            #     expected_error=expected_error
            # )
            pass  # 临时占位

    @allure.story("登录页面元素验证")
    @allure.severity(allure.severity_level.MINOR)
    @pytest.mark.component
    def test_login_page_elements(self, page, env_config):
        """验证登录页面必要元素存在"""
        base_url = env_config["url"]

        with allure.step("导航到登录页面"):
            page.goto(base_url)

        with allure.step("验证登录表单元素"):
            # 验证用户名输入框
            username_input = page.locator("#username")
            assert username_input.is_visible(), "用户名输入框应该可见"

            # 验证密码输入框
            password_input = page.locator("#password")
            assert password_input.is_visible(), "密码输入框应该可见"

            # 验证登录按钮
            login_button = page.locator("#login-button")
            assert login_button.is_visible(), "登录按钮应该可见"
            assert login_button.is_enabled(), "登录按钮应该可点击"
