fixtures.scf_fixtures - INFO - 加载SCF系统配置成功
fixtures.scf_fixtures - INFO - 加载SCF测试数据成功
fixtures.scf_fixtures - INFO - 准备SCF登录凭证
components.ocr_util - INFO - OCR引擎初始化成功
SCFRegistrationWorkflow - INFO - 开始登录系统
SCFLoginPage - INFO - 导航到登录页面: http://172.18.12.128:8080/login
pages.base_page - INFO - 导航到页面: http://172.18.12.128:8080/login
SCFLoginPage - INFO - 开始登录流程，用户名: scf_4nuioc
SCFLoginPage - INFO - 点击账号登录标签
components.retry_util - DEBUG - 执行函数 click，尝试 1/3
pages.base_page - INFO - 点击元素: text=账号登录
pages.base_page - DEBUG - 元素 text=账号登录 已稳定
SCFLoginPage - INFO - 输入用户名: scf_4nuioc
components.retry_util - DEBUG - 执行函数 fill，尝试 1/3
pages.base_page - INFO - 填充文本到元素 role=textbox[name='请输入账号']: scf_4nuioc
pages.base_page - DEBUG - 元素 role=textbox[name='请输入账号'] 已稳定
SCFLoginPage - INFO - 输入密码
components.retry_util - DEBUG - 执行函数 fill，尝试 1/3
pages.base_page - INFO - 填充文本到元素 role=textbox[name='请输入密码']: Scf123456.
pages.base_page - DEBUG - 元素 role=textbox[name='请输入密码'] 已稳定
SCFLoginPage - INFO - 开始自动识别验证码
pages.base_page - INFO - 等待元素 .captcha-image 状态: visible
SCFLoginPage - ERROR - 自动识别验证码失败: Locator.wait_for: Timeout 30000ms exceeded.
Call log:
  - waiting for locator(".captcha-image") to be visible

SCFLoginPage - WARNING - 自动识别验证码失败，需要手动处理: 'SCFLoginPage' object has no attribute 'take_screenshot_for_report'
fixtures.conftest - ERROR - 测试失败，已保存调试信息: test_standard_user_login_success[chromium]
