{"name": "test_captcha_functionality[chromium]", "status": "passed", "description": "测试验证码功能", "steps": [{"name": "导航到登录页面", "status": "passed", "steps": [{"name": "导航到登录页面", "status": "passed", "steps": [{"name": "导航到页面: 'http://*************:8080/login'", "status": "passed", "parameters": [{"name": "url", "value": "'http://*************:8080/login'"}, {"name": "wait_until", "value": "'domcontentloaded'"}], "start": 1751637799432, "stop": 1751637800141}], "parameters": [{"name": "base_url", "value": "'http://*************:8080'"}], "start": 1751637799432, "stop": 1751637800141}, {"name": "点击账号登录标签", "status": "passed", "steps": [{"name": "智能点击: 'text=账号登录'", "status": "passed", "steps": [{"name": "等待元素稳定: 'text=账号登录'", "status": "passed", "parameters": [{"name": "locator", "value": "'text=账号登录'"}, {"name": "timeout", "value": "None"}], "start": 1751637800142, "stop": 1751637800614}], "parameters": [{"name": "locator", "value": "'text=账号登录'"}, {"name": "force", "value": "False"}, {"name": "timeout", "value": "None"}], "start": 1751637800141, "stop": 1751637800709}], "start": 1751637800141, "stop": 1751637800709}], "start": 1751637799432, "stop": 1751637800709}, {"name": "测试验证码刷新功能", "status": "broken", "statusDetails": {"message": "KeyboardInterrupt\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\tests\\ui\\smoke\\test_scf_login.py\", line 133, in test_captcha_functionality\n    if workflow.login_page.is_visible(workflow.login_page.captcha_image):\n       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 220, in is_visible\n    element.wait_for(state=\"visible\", timeout=timeout or 5000)  # 较短的超时时间\n    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 17937, in wait_for\n    self._sync(self._impl_obj.wait_for(timeout=timeout, state=state))\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 113, in _sync\n    self._dispatcher_fiber.switch()\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_context_manager.py\", line 56, in greenlet_main\n    self._loop.run_until_complete(self._connection.run_as_sync())\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.11-windows-x86_64-none\\Lib\\asyncio\\base_events.py\", line 678, in run_until_complete\n    self.run_forever()\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.11-windows-x86_64-none\\Lib\\asyncio\\windows_events.py\", line 322, in run_forever\n    super().run_forever()\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.11-windows-x86_64-none\\Lib\\asyncio\\base_events.py\", line 645, in run_forever\n    self._run_once()\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.11-windows-x86_64-none\\Lib\\asyncio\\base_events.py\", line 1961, in _run_once\n    event_list = self._selector.select(timeout)\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.11-windows-x86_64-none\\Lib\\asyncio\\windows_events.py\", line 445, in select\n    self._poll(timeout)\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.11-windows-x86_64-none\\Lib\\asyncio\\windows_events.py\", line 774, in _poll\n    status = _overlapped.GetQueuedCompletionStatus(self._iocp, ms)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "steps": [{"name": "检查元素是否可见: '.captcha-image'", "status": "broken", "statusDetails": {"message": "KeyboardInterrupt\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 220, in is_visible\n    element.wait_for(state=\"visible\", timeout=timeout or 5000)  # 较短的超时时间\n    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 17937, in wait_for\n    self._sync(self._impl_obj.wait_for(timeout=timeout, state=state))\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 113, in _sync\n    self._dispatcher_fiber.switch()\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_context_manager.py\", line 56, in greenlet_main\n    self._loop.run_until_complete(self._connection.run_as_sync())\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.11-windows-x86_64-none\\Lib\\asyncio\\base_events.py\", line 678, in run_until_complete\n    self.run_forever()\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.11-windows-x86_64-none\\Lib\\asyncio\\windows_events.py\", line 322, in run_forever\n    super().run_forever()\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.11-windows-x86_64-none\\Lib\\asyncio\\base_events.py\", line 645, in run_forever\n    self._run_once()\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.11-windows-x86_64-none\\Lib\\asyncio\\base_events.py\", line 1961, in _run_once\n    event_list = self._selector.select(timeout)\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.11-windows-x86_64-none\\Lib\\asyncio\\windows_events.py\", line 445, in select\n    self._poll(timeout)\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\uv\\python\\cpython-3.12.11-windows-x86_64-none\\Lib\\asyncio\\windows_events.py\", line 774, in _poll\n    status = _overlapped.GetQueuedCompletionStatus(self._iocp, ms)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "parameters": [{"name": "locator", "value": "'.captcha-image'"}, {"name": "timeout", "value": "None"}], "start": 1751637800710, "stop": 1751637803196}], "start": 1751637800709, "stop": 1751637803198}], "parameters": [{"name": "browser_name", "value": "'chromium'"}], "start": 1751637799287, "stop": 1751637803200, "uuid": "5e7d17cb-3546-4e60-a0b6-ffa19c503750", "testCaseId": "c5ee54436ca1b45fcb049d6e49e06abd", "fullName": "tests.ui.smoke.test_scf_login.TestSCFLogin#test_captcha_functionality"}