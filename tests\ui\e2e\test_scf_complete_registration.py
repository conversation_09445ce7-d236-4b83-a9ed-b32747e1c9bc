"""
供应链金融备案申请完整流程端到端测试
SCF Complete Registration E2E Tests
"""

import pytest
import allure
from workflows.scf_registration_workflow import SCFRegistrationWorkflow


@allure.epic("供应链金融备案系统")
@allure.feature("完整备案申请流程")
class TestSCFCompleteRegistration:
    """供应链金融完整备案申请流程测试类"""
    
    @allure.story("完整备案申请流程")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.e2e
    @pytest.mark.slow
    def test_complete_registration_process(self, 
                                         page, 
                                         scf_base_url, 
                                         scf_login_credentials,
                                         scf_application_data,
                                         scf_test_files_validator,
                                         scf_data_cleanup):
        """测试完整的备案申请流程"""
        # 准备测试数据
        workflow = SCFRegistrationWorkflow(page)
        credentials = scf_login_credentials["standard_user"]
        
        # 验证所有必需的测试文件
        all_file_uploads = {}
        
        # 收集所有文件上传需求
        basic_materials = scf_application_data.get("basic_materials", {})
        if "file_uploads" in basic_materials:
            all_file_uploads.update(basic_materials["file_uploads"])
        
        recommendation_materials = scf_application_data.get("recommendation_materials", {})
        if "recommendation_letters" in recommendation_materials:
            for i, file_path in enumerate(recommendation_materials["recommendation_letters"]):
                all_file_uploads[f"recommendation_letter_{i}"] = file_path
        
        if "other_materials" in recommendation_materials:
            for i, file_path in enumerate(recommendation_materials["other_materials"]):
                all_file_uploads[f"other_material_{i}"] = file_path
        
        # 验证文件存在
        if all_file_uploads:
            validation_results = scf_test_files_validator(all_file_uploads)
            missing_files = [k for k, v in validation_results.items() if not v]
            if missing_files:
                pytest.skip(f"测试文件缺失，跳过完整流程测试: {missing_files}")
        
        # 注册数据清理任务
        def cleanup_application_data():
            """清理申请数据"""
            # 这里可以添加具体的数据清理逻辑
            # 例如删除创建的申请记录等
            pass
        
        scf_data_cleanup(cleanup_application_data)
        
        # 执行完整申请流程
        with allure.step("执行完整备案申请流程"):
            workflow.complete_registration_process(
                login_credentials=credentials,
                application_data=scf_application_data,
                base_url=scf_base_url
            )
        
        with allure.step("验证申请提交成功"):
            workflow.verify_application_submitted()
        
        with allure.step("记录申请完成状态"):
            current_url = workflow.get_current_url()
            allure.attach(current_url, "申请完成后URL", allure.attachment_type.TEXT)
            
            # 截图记录最终状态
            workflow.take_screenshot_for_report("完整申请流程完成")
    
    @allure.story("参数化申请流程测试")
    @allure.severity(allure.severity_level.NORMAL)
    @pytest.mark.e2e
    def test_parameterized_registration_process(self, 
                                              page, 
                                              scf_base_url, 
                                              scf_login_credentials,
                                              scf_parameterized_application_data,
                                              scf_test_files_validator):
        """测试参数化的申请流程"""
        # 准备测试数据
        workflow = SCFRegistrationWorkflow(page)
        credentials = scf_login_credentials["standard_user"]
        
        scenario_name = scf_parameterized_application_data["scenario_name"]
        scenario_description = scf_parameterized_application_data["scenario_description"]
        application_data = scf_parameterized_application_data["data"]
        
        allure.dynamic.title(f"参数化申请流程测试 - {scenario_name}")
        allure.dynamic.description(scenario_description)
        
        # 验证测试文件（如果需要）
        basic_materials = application_data.get("basic_materials", {})
        file_uploads = basic_materials.get("file_uploads", {})
        
        if file_uploads:
            validation_results = scf_test_files_validator(file_uploads)
            missing_files = [k for k, v in validation_results.items() if not v]
            if missing_files:
                pytest.skip(f"场景 {scenario_name} 的测试文件缺失: {missing_files}")
        
        # 执行申请流程
        with allure.step(f"执行 {scenario_name} 申请流程"):
            workflow.complete_registration_process(
                login_credentials=credentials,
                application_data=application_data,
                base_url=scf_base_url
            )
        
        with allure.step("验证申请结果"):
            workflow.verify_application_submitted()
    
    @allure.story("申请流程性能测试")
    @allure.severity(allure.severity_level.MINOR)
    @pytest.mark.e2e
    @pytest.mark.performance
    def test_registration_process_performance(self, 
                                            page, 
                                            scf_base_url, 
                                            scf_login_credentials,
                                            scf_minimal_application_data,
                                            scf_timeout_config):
        """测试申请流程的性能"""
        import time
        
        # 准备测试数据
        workflow = SCFRegistrationWorkflow(page)
        credentials = scf_login_credentials["standard_user"]
        
        # 记录开始时间
        start_time = time.time()
        
        # 执行申请流程
        with allure.step("执行申请流程并记录性能"):
            workflow.complete_registration_process(
                login_credentials=credentials,
                application_data=scf_minimal_application_data,
                base_url=scf_base_url
            )
        
        # 记录结束时间
        end_time = time.time()
        total_time = end_time - start_time
        
        with allure.step("验证性能指标"):
            # 获取性能阈值（假设最大允许时间为5分钟）
            max_allowed_time = scf_timeout_config.get("form_submit", 30000) / 1000 * 10  # 转换为秒并放宽10倍
            
            allure.attach(f"{total_time:.2f} 秒", "总执行时间", allure.attachment_type.TEXT)
            allure.attach(f"{max_allowed_time:.2f} 秒", "最大允许时间", allure.attachment_type.TEXT)
            
            assert total_time < max_allowed_time, f"申请流程执行时间过长: {total_time:.2f}s > {max_allowed_time:.2f}s"
    
    @allure.story("申请流程错误恢复")
    @allure.severity(allure.severity_level.NORMAL)
    @pytest.mark.e2e
    def test_registration_process_error_recovery(self, 
                                               page, 
                                               scf_base_url, 
                                               scf_login_credentials,
                                               scf_negative_test_data):
        """测试申请流程的错误恢复能力"""
        # 准备测试数据
        workflow = SCFRegistrationWorkflow(page)
        credentials = scf_login_credentials["standard_user"]
        
        # 使用异常测试数据
        with allure.step("使用异常数据执行申请流程"):
            try:
                workflow.complete_registration_process(
                    login_credentials=credentials,
                    application_data=scf_negative_test_data,
                    base_url=scf_base_url
                )
            except Exception as e:
                # 记录异常信息
                allure.attach(str(e), "预期的异常信息", allure.attachment_type.TEXT)
        
        with allure.step("验证系统错误处理"):
            # 验证系统仍然可用
            current_url = workflow.get_current_url()
            
            # 系统应该仍然可以响应
            assert workflow.page.title(), "页面应该仍然可以访问"
            
            # 记录当前状态
            allure.attach(current_url, "错误处理后的URL", allure.attachment_type.TEXT)
    
    @allure.story("多用户并发申请")
    @allure.severity(allure.severity_level.MINOR)
    @pytest.mark.e2e
    @pytest.mark.skip(reason="需要多个用户凭证支持")
    def test_concurrent_registration_process(self, 
                                           page, 
                                           scf_base_url, 
                                           scf_login_credentials,
                                           scf_minimal_application_data):
        """测试多用户并发申请（需要多个用户凭证）"""
        # 这个测试需要多个有效的用户凭证
        # 目前跳过，可以在有多个测试用户时启用
        pass
    
    @allure.story("申请数据完整性验证")
    @allure.severity(allure.severity_level.NORMAL)
    @pytest.mark.e2e
    def test_application_data_integrity(self, 
                                      page, 
                                      scf_base_url, 
                                      scf_login_credentials,
                                      scf_application_data):
        """测试申请数据的完整性"""
        # 准备测试数据
        workflow = SCFRegistrationWorkflow(page)
        credentials = scf_login_credentials["standard_user"]
        
        # 执行申请流程
        with allure.step("执行申请流程"):
            workflow.complete_registration_process(
                login_credentials=credentials,
                application_data=scf_application_data,
                base_url=scf_base_url
            )
        
        with allure.step("验证数据完整性"):
            # 这里可以添加具体的数据完整性验证
            # 例如检查提交的数据是否与输入的数据一致
            
            # 验证申请成功
            workflow.verify_application_submitted()
            
            # 记录数据完整性检查结果
            allure.attach("数据完整性检查通过", "完整性验证结果", allure.attachment_type.TEXT)
