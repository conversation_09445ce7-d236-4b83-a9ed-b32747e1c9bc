{"name": "test_standard_user_login_success[chromium]", "status": "failed", "statusDetails": {"message": "AssertionError: 登录失败，仍在登录页面: http://*************:8080/login", "trace": "test_scf_login.py:37: in test_standard_user_login_success\n    workflow._login_to_system(credentials, scf_base_url)\n..\\..\\..\\workflows\\scf_registration_workflow.py:126: in _login_to_system\n    self.login_page.verify_login_success()\n..\\..\\..\\pages\\scf_login_page.py:366: in verify_login_success\n    assert self.login_url not in current_url, f\"登录失败，仍在登录页面: {current_url}\"\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nE   AssertionError: 登录失败，仍在登录页面: http://*************:8080/login"}, "description": "测试标准用户成功登录", "steps": [{"name": "执行标准用户登录", "status": "failed", "statusDetails": {"message": "AssertionError: 登录失败，仍在登录页面: http://*************:8080/login\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\tests\\ui\\smoke\\test_scf_login.py\", line 37, in test_standard_user_login_success\n    workflow._login_to_system(credentials, scf_base_url)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\workflows\\scf_registration_workflow.py\", line 126, in _login_to_system\n    self.login_page.verify_login_success()\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 366, in verify_login_success\n    assert self.login_url not in current_url, f\"登录失败，仍在登录页面: {current_url}\"\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "steps": [{"name": "登录系统", "status": "failed", "statusDetails": {"message": "AssertionError: 登录失败，仍在登录页面: http://*************:8080/login\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\workflows\\scf_registration_workflow.py\", line 126, in _login_to_system\n    self.login_page.verify_login_success()\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 366, in verify_login_success\n    assert self.login_url not in current_url, f\"登录失败，仍在登录页面: {current_url}\"\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "steps": [{"name": "导航到登录页面", "status": "passed", "steps": [{"name": "导航到页面: 'http://*************:8080/login'", "status": "passed", "parameters": [{"name": "url", "value": "'http://*************:8080/login'"}, {"name": "wait_until", "value": "'domcontentloaded'"}], "start": 1751853945080, "stop": 1751853946005}], "parameters": [{"name": "base_url", "value": "'http://*************:8080'"}], "start": 1751853945080, "stop": 1751853946005}, {"name": "执行登录操作", "status": "passed", "steps": [{"name": "点击账号登录标签", "status": "passed", "steps": [{"name": "等待元素出现: 'text=账号登录'", "status": "passed", "parameters": [{"name": "locator", "value": "'text=账号登录'"}, {"name": "state", "value": "'visible'"}, {"name": "timeout", "value": "10000"}], "start": 1751853946006, "stop": 1751853946230}, {"name": "智能点击: 'text=账号登录'", "status": "passed", "steps": [{"name": "等待元素稳定: 'text=账号登录'", "status": "passed", "parameters": [{"name": "locator", "value": "'text=账号登录'"}, {"name": "timeout", "value": "None"}], "start": 1751853946230, "stop": 1751853946571}], "parameters": [{"name": "locator", "value": "'text=账号登录'"}, {"name": "force", "value": "False"}, {"name": "timeout", "value": "None"}], "start": 1751853946230, "stop": 1751853946641}], "start": 1751853946006, "stop": 1751853947645}, {"name": "输入用户名: 'scf_4nuioc'", "status": "passed", "steps": [{"name": "智能填充: 'input[placeholder='请输入账号']' = 'scf_4nuioc'", "status": "passed", "steps": [{"name": "等待元素稳定: 'input[placeholder='请输入账号']'", "status": "passed", "parameters": [{"name": "locator", "value": "'input[placeholder='请输入账号']'"}, {"name": "timeout", "value": "None"}], "start": 1751853947645, "stop": 1751853948006}], "parameters": [{"name": "locator", "value": "'input[placeholder='请输入账号']'"}, {"name": "text", "value": "'scf_4nuioc'"}, {"name": "clear_first", "value": "True"}, {"name": "timeout", "value": "None"}], "start": 1751853947645, "stop": 1751853948035}], "parameters": [{"name": "username", "value": "'scf_4nuioc'"}], "start": 1751853947645, "stop": 1751853948035}, {"name": "输入密码", "status": "passed", "steps": [{"name": "智能填充: 'input[type='password']' = 'Scf123456.'", "status": "passed", "steps": [{"name": "等待元素稳定: 'input[type='password']'", "status": "passed", "parameters": [{"name": "locator", "value": "'input[type='password']'"}, {"name": "timeout", "value": "None"}], "start": 1751853948036, "stop": 1751853948389}], "parameters": [{"name": "locator", "value": "'input[type='password']'"}, {"name": "text", "value": "'Scf123456.'"}, {"name": "clear_first", "value": "True"}, {"name": "timeout", "value": "None"}], "start": 1751853948036, "stop": 1751853948420}], "parameters": [{"name": "password", "value": "'Scf123456.'"}], "start": 1751853948035, "stop": 1751853948420}, {"name": "自动识别并输入验证码", "status": "passed", "steps": [{"name": "等待元素出现: '.arco-image img'", "status": "passed", "parameters": [{"name": "locator", "value": "'.arco-image img'"}, {"name": "state", "value": "'visible'"}, {"name": "timeout", "value": "10000"}], "start": 1751853948421, "stop": 1751853948426}, {"name": "输入图形验证码: '0oo2'", "status": "passed", "steps": [{"name": "智能填充: 'input[placeholder='请输入图形验证码']' = '0oo2'", "status": "passed", "steps": [{"name": "等待元素稳定: 'input[placeholder='请输入图形验证码']'", "status": "passed", "parameters": [{"name": "locator", "value": "'input[placeholder='请输入图形验证码']'"}, {"name": "timeout", "value": "None"}], "start": 1751853949586, "stop": 1751853949961}], "parameters": [{"name": "locator", "value": "'input[placeholder='请输入图形验证码']'"}, {"name": "text", "value": "'0oo2'"}, {"name": "clear_first", "value": "True"}, {"name": "timeout", "value": "None"}], "start": 1751853949586, "stop": 1751853950005}], "parameters": [{"name": "<PERSON><PERSON>a", "value": "'0oo2'"}], "start": 1751853949585, "stop": 1751853950005}], "parameters": [{"name": "max_retries", "value": "3"}], "start": 1751853948420, "stop": 1751853950005}, {"name": "点击登录按钮", "status": "passed", "steps": [{"name": "智能点击: 'button[type='submit']'", "status": "passed", "steps": [{"name": "等待元素稳定: 'button[type='submit']'", "status": "passed", "parameters": [{"name": "locator", "value": "'button[type='submit']'"}, {"name": "timeout", "value": "None"}], "start": 1751853950006, "stop": 1751853950351}], "parameters": [{"name": "locator", "value": "'button[type='submit']'"}, {"name": "force", "value": "False"}, {"name": "timeout", "value": "None"}], "start": 1751853950006, "stop": 1751853950458}], "start": 1751853950005, "stop": 1751853950458}], "parameters": [{"name": "username", "value": "'scf_4nuioc'"}, {"name": "password", "value": "'Scf123456.'"}, {"name": "<PERSON><PERSON>a", "value": "None"}, {"name": "auto_captcha", "value": "True"}], "start": 1751853946005, "stop": 1751853950458}, {"name": "验证登录成功", "status": "failed", "statusDetails": {"message": "AssertionError: 登录失败，仍在登录页面: http://*************:8080/login\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 366, in verify_login_success\n    assert self.login_url not in current_url, f\"登录失败，仍在登录页面: {current_url}\"\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "steps": [{"name": "等待页面加载完成", "status": "passed", "parameters": [{"name": "state", "value": "'networkidle'"}, {"name": "timeout", "value": "None"}], "start": 1751853950458, "stop": 1751853950460}], "parameters": [{"name": "expected_url_pattern", "value": "None"}], "start": 1751853950458, "stop": 1751853950460}], "parameters": [{"name": "credentials", "value": "{'username': 'scf_4nuioc', 'password': 'Scf123456.', 'description': '标准测试用户'}"}, {"name": "base_url", "value": "'http://*************:8080'"}], "start": 1751853945080, "stop": 1751853950460}], "start": 1751853945078, "stop": 1751853950460}], "attachments": [{"name": "失败截图", "source": "264f073d-c6ce-4b0d-8d1b-b3fd5eb1edc8-attachment.png", "type": "image/png"}, {"name": "页面HTML", "source": "0a11ea9d-e044-4f23-ae28-0d2d9510a196-attachment.html", "type": "text/html"}, {"name": "当前URL", "source": "69e6dfac-5bc2-4850-8cd0-a3f241f6fdb7-attachment.txt", "type": "text/plain"}, {"name": "log", "source": "a5854bb2-e116-40a5-ad0e-3805bd764e22-attachment.txt", "type": "text/plain"}, {"name": "stdout", "source": "52570f1c-3f81-4681-8cd1-cc5b21f2c443-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "browser_name", "value": "'chromium'"}], "start": 1751853945036, "stop": 1751853950461, "uuid": "5873476a-c9de-478c-a1ca-42a902b06686", "historyId": "83c32d878021effc5e3dcc4d134c4bd1", "testCaseId": "2cf6f352a9af813bd18233473cb242da", "fullName": "tests.ui.smoke.test_scf_login.TestSCFLogin#test_standard_user_login_success", "labels": [{"name": "feature", "value": "用户登录"}, {"name": "epic", "value": "供应链金融备案系统"}, {"name": "story", "value": "标准用户登录"}, {"name": "severity", "value": "blocker"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "tests.ui.smoke"}, {"name": "suite", "value": "test_scf_login"}, {"name": "subSuite", "value": "TestSC<PERSON><PERSON>in"}, {"name": "host", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "thread", "value": "14940-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.ui.smoke.test_scf_login"}]}