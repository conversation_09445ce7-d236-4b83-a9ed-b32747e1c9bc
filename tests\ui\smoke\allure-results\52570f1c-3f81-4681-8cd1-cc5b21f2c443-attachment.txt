fixtures.scf_fixtures - INFO - 加载SCF系统配置成功
fixtures.scf_fixtures - INFO - 加载SCF测试数据成功
fixtures.scf_fixtures - INFO - 准备SCF登录凭证
components.ocr_util - INFO - OCR引擎初始化成功
SCFRegistrationWorkflow - INFO - 开始登录系统
SCFLoginPage - INFO - 导航到登录页面: http://172.18.12.128:8080/login
pages.base_page - INFO - 导航到页面: http://172.18.12.128:8080/login
SCFLoginPage - INFO - 开始登录流程，用户名: scf_4nuioc
SCFLoginPage - INFO - 点击账号登录标签
pages.base_page - INFO - 等待元素 text=账号登录 状态: visible
pages.base_page - INFO - 点击元素: text=账号登录
SCFLoginPage - INFO - 输入用户名: scf_4nuioc
pages.base_page - INFO - 填充文本到元素 input[placeholder='请输入账号']: scf_4nuioc
SCFLoginPage - INFO - 输入密码
pages.base_page - INFO - 填充文本到元素 input[type='password']: Scf123456.
SCFLoginPage - INFO - 开始自动识别验证码
SCFLoginPage - INFO - 验证码识别尝试 1/3
pages.base_page - INFO - 等待元素 .arco-image img 状态: visible
SCFLoginPage - INFO - 找到验证码元素，使用选择器: .arco-image img
components.ocr_util - WARNING - 图像复杂度分析失败: name 'ImageFilter' is not defined
components.ocr_util - INFO - OCR识别结果: 0oo2, 置信度: 0.8
SCFLoginPage - INFO - 验证码识别结果: 0oo2, 置信度: 0.8
SCFLoginPage - INFO - 输入图形验证码: 0oo2
pages.base_page - INFO - 填充文本到元素 input[placeholder='请输入图形验证码']: 0oo2
SCFLoginPage - INFO - 点击登录按钮
pages.base_page - INFO - 点击元素: button[type='submit']
SCFLoginPage - INFO - 登录操作完成
SCFLoginPage - INFO - 验证登录成功状态
pages.base_page - INFO - 等待页面加载状态: networkidle
fixtures.conftest - ERROR - 测试失败，已保存调试信息: test_standard_user_login_success[chromium]
