# pytest 调试配置文件
# 使用方法: pytest -c pytest-debug.ini tests/ui/smoke/test_scf_registration.py

[tool:pytest]
pythonpath = . fixtures
addopts = 
    --alluredir=allure-results
    --clean-alluredir
    --tb=short
    --strict-markers
    --disable-warnings
    --headed
    --slowmo=1000
    --browser-channel=chrome
    -v
    -s

markers =
    smoke: 冒烟测试
    regression: 回归测试
    e2e: 端到端测试
    component: 组件测试
    api: API测试
    slow: 慢速测试
    skip_ci: 跳过CI执行的测试

testpaths = tests
