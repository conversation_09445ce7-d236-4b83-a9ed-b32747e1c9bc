1. 分支与版本控制
分支模型：main（生产）、develop（集成）、feature/*、hotfix/*

提交规范：遵循 Conventional Commits；PR 标题需包含变更类型与范围。

代码评审：至少一名 Review 批准；CI 全部检查通过后方可合并。

2. 代码风格与质量
格式化：使用 Ruff+ Black。

静态检查：Ruff、Mypy（严格类型检查）。

Pre-commit：所有提交自动运行格式化与检查，禁止绕过。

3. 测试设计
POM 与 Workflow：所有 UI 操作必须通过 Page 对象封装；跨页面业务必须在 Workflow 层实现。

无业务逻辑于用例：tests/ 目录下仅调用 Workflow，不得出现循环或条件分支。

数据隔离：所有测试数据须通过 Fixture 创建与清理，禁止硬编码真实账号或对线上环境数据改动。

4. 报告与日志
Allure 报告：所有测试须生成报告，并附带步骤、截图、Trace、日志附件；CI 失败时提供报告链接。

日志级别：默认 INFO，调试环境 DEBUG；仅在 Workflow 与 Page 层使用 @log_step 记录关键步骤。

5. 安全与凭证
敏感信息：任何凭证、Token、密码统一通过环境变量或 CI Secrets 注入；严禁硬编码。

凭证更新：凭证或接口变更时，需及时更新对应环境 YAML 与 Secrets 配置。

6. CI/CD 要求
流水线稳定性：所有 Job 必须设置合理超时（如 30 min），并对失败进行告警；

并行策略：默认开启并行，但支持在紧急情况下通过参数关闭并行执行以降低不稳定性；

回滚机制：若 CI 失败导致主线阻塞，可通过 hotfix/* 分支应急修复。

7. 文档与知识共享
README：保持最新，涵盖快速启动、运行示例、常见问题。

8. 

设计文档版本管理：文档 (v2.0) 脚本化存储，任何重大变更需在 Git Tag 中标注文档版本。

团队培训：新成员上岗前必须阅读设计文档与项目规则，并完成一轮示例测试。