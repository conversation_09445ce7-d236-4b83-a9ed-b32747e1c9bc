{"name": "test_captcha_functionality[chromium]", "status": "broken", "statusDetails": {"message": "KeyError: 'locator'", "trace": "test_scf_login.py:129: in test_captcha_functionality\n    workflow.login_page.click_account_login_tab()\n..\\..\\..\\pages\\scf_login_page.py:58: in click_account_login_tab\n    self.click(self.account_tab)\nE   KeyError: 'locator'"}, "description": "测试验证码功能", "steps": [{"name": "导航到登录页面", "status": "broken", "statusDetails": {"message": "KeyError: 'locator'\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\tests\\ui\\smoke\\test_scf_login.py\", line 129, in test_captcha_functionality\n    workflow.login_page.click_account_login_tab()\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 58, in click_account_login_tab\n    self.click(self.account_tab)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 201, in impl\n    with StepContext(self.title.format(*args, **params), params):\n                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "steps": [{"name": "导航到登录页面", "status": "passed", "steps": [{"name": "导航到页面: 'http://172.18.12.128:8080/login'", "status": "passed", "parameters": [{"name": "url", "value": "'http://172.18.12.128:8080/login'"}, {"name": "wait_until", "value": "'domcontentloaded'"}], "start": *************, "stop": *************}], "parameters": [{"name": "base_url", "value": "'http://172.18.12.128:8080'"}], "start": *************, "stop": *************}, {"name": "点击账号登录标签", "status": "broken", "statusDetails": {"message": "KeyError: 'locator'\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 58, in click_account_login_tab\n    self.click(self.account_tab)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 201, in impl\n    with StepContext(self.title.format(*args, **params), params):\n                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "start": *************, "stop": *************}], "start": *************, "stop": *************}], "attachments": [{"name": "失败截图", "source": "f0f5e1a1-2fc2-4392-91d1-34a3e86e1d80-attachment.png", "type": "image/png"}, {"name": "页面HTML", "source": "756335fe-8ace-4949-abde-2f98d632bc72-attachment.html", "type": "text/html"}, {"name": "当前URL", "source": "aba55b86-ee82-4659-83a2-7fffdde361a7-attachment.txt", "type": "text/plain"}, {"name": "log", "source": "5b83e05a-8d85-41e2-b56d-37d809e5fc69-attachment.txt", "type": "text/plain"}, {"name": "stderr", "source": "d3999faa-4acf-4640-807d-8e02af188a01-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "browser_name", "value": "'chromium'"}], "start": 1751635104223, "stop": 1751635104831, "uuid": "9acea7e9-79ab-4116-81e2-6beff7fb20e3", "historyId": "9c48d705d01d7be2c7e42debe8eca4c9", "testCaseId": "c5ee54436ca1b45fcb049d6e49e06abd", "fullName": "tests.ui.smoke.test_scf_login.TestSCFLogin#test_captcha_functionality", "labels": [{"name": "story", "value": "验证码功能测试"}, {"name": "feature", "value": "用户登录"}, {"name": "epic", "value": "供应链金融备案系统"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "component"}, {"name": "parentSuite", "value": "tests.ui.smoke"}, {"name": "suite", "value": "test_scf_login"}, {"name": "subSuite", "value": "TestSC<PERSON><PERSON>in"}, {"name": "host", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "thread", "value": "21436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.ui.smoke.test_scf_login"}]}