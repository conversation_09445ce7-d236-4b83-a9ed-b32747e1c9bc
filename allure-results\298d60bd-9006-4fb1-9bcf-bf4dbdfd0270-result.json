{"name": "test_standard_user_login_success[chromium]", "status": "broken", "statusDetails": {"message": "AttributeError: 'SCFLoginPage' object has no attribute 'take_screenshot_for_report'", "trace": "pages\\scf_login_page.py:110: in auto_input_captcha\n    self.wait_for_element(self.captcha_image)\npages\\base_page.py:192: in wait_for_element\n    element.wait_for(state=state, timeout=timeout or self.default_timeout)\n.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py:17937: in wait_for\n    self._sync(self._impl_obj.wait_for(timeout=timeout, state=state))\n.venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py:693: in wait_for\n    await self._frame.wait_for_selector(\n.venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py:341: in wait_for_selector\n    await self._channel.send(\n.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:69: in send\n    return await self._connection.wrap_api_call(\n.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:558: in wrap_api_call\n    raise rewrite_error(error, f\"{parsed_st['apiName']}: {error}\") from None\nE   playwright._impl._errors.TimeoutError: Locator.wait_for: Timeout 30000ms exceeded.\nE   Call log:\nE     - waiting for locator(\".captcha-image\") to be visible\n\nDuring handling of the above exception, another exception occurred:\ntests\\ui\\smoke\\test_scf_login.py:30: in test_standard_user_login_success\n    workflow._login_to_system(credentials, scf_base_url)\nworkflows\\scf_registration_workflow.py:123: in _login_to_system\n    self.login_page.login(username, password, auto_captcha=True)\npages\\scf_login_page.py:170: in login\n    self.auto_input_captcha()\npages\\scf_login_page.py:133: in auto_input_captcha\n    self.take_screenshot_for_report(\"验证码识别失败\")\n    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nE   AttributeError: 'SCFLoginPage' object has no attribute 'take_screenshot_for_report'"}, "description": "测试标准用户成功登录", "steps": [{"name": "执行标准用户登录", "status": "broken", "statusDetails": {"message": "AttributeError: 'SCFLoginPage' object has no attribute 'take_screenshot_for_report'\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\tests\\ui\\smoke\\test_scf_login.py\", line 30, in test_standard_user_login_success\n    workflow._login_to_system(credentials, scf_base_url)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\workflows\\scf_registration_workflow.py\", line 123, in _login_to_system\n    self.login_page.login(username, password, auto_captcha=True)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 170, in login\n    self.auto_input_captcha()\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 133, in auto_input_captcha\n    self.take_screenshot_for_report(\"验证码识别失败\")\n    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "steps": [{"name": "登录系统", "status": "broken", "statusDetails": {"message": "AttributeError: 'SCFLoginPage' object has no attribute 'take_screenshot_for_report'\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\workflows\\scf_registration_workflow.py\", line 123, in _login_to_system\n    self.login_page.login(username, password, auto_captcha=True)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 170, in login\n    self.auto_input_captcha()\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 133, in auto_input_captcha\n    self.take_screenshot_for_report(\"验证码识别失败\")\n    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "steps": [{"name": "导航到登录页面", "status": "passed", "steps": [{"name": "导航到页面: 'http://*************:8080/login'", "status": "passed", "parameters": [{"name": "url", "value": "'http://*************:8080/login'"}, {"name": "wait_until", "value": "'domcontentloaded'"}], "start": 1751637698817, "stop": 1751637699614}], "parameters": [{"name": "base_url", "value": "'http://*************:8080'"}], "start": 1751637698817, "stop": 1751637699614}, {"name": "执行登录操作", "status": "broken", "statusDetails": {"message": "AttributeError: 'SCFLoginPage' object has no attribute 'take_screenshot_for_report'\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 170, in login\n    self.auto_input_captcha()\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 133, in auto_input_captcha\n    self.take_screenshot_for_report(\"验证码识别失败\")\n    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "steps": [{"name": "点击账号登录标签", "status": "passed", "steps": [{"name": "智能点击: 'text=账号登录'", "status": "passed", "steps": [{"name": "等待元素稳定: 'text=账号登录'", "status": "passed", "parameters": [{"name": "locator", "value": "'text=账号登录'"}, {"name": "timeout", "value": "None"}], "start": 1751637699624, "stop": 1751637700095}], "parameters": [{"name": "locator", "value": "'text=账号登录'"}, {"name": "force", "value": "False"}, {"name": "timeout", "value": "None"}], "start": 1751637699624, "stop": 1751637700178}], "start": 1751637699624, "stop": 1751637700178}, {"name": "输入用户名: 'scf_4nuioc'", "status": "passed", "steps": [{"name": "智能填充: 'role=textbox[name='请输入账号']' = 'scf_4nuioc'", "status": "passed", "steps": [{"name": "等待元素稳定: 'role=textbox[name='请输入账号']'", "status": "passed", "parameters": [{"name": "locator", "value": "'role=textbox[name='请输入账号']'"}, {"name": "timeout", "value": "None"}], "start": 1751637700178, "stop": 1751637700526}], "parameters": [{"name": "locator", "value": "'role=textbox[name='请输入账号']'"}, {"name": "text", "value": "'scf_4nuioc'"}, {"name": "clear_first", "value": "True"}, {"name": "timeout", "value": "None"}], "start": 1751637700178, "stop": 1751637700553}], "parameters": [{"name": "username", "value": "'scf_4nuioc'"}], "start": 1751637700178, "stop": 1751637700553}, {"name": "输入密码", "status": "passed", "steps": [{"name": "智能填充: 'role=textbox[name='请输入密码']' = 'Scf123456.'", "status": "passed", "steps": [{"name": "等待元素稳定: 'role=textbox[name='请输入密码']'", "status": "passed", "parameters": [{"name": "locator", "value": "'role=textbox[name='请输入密码']'"}, {"name": "timeout", "value": "None"}], "start": 1751637700553, "stop": 1751637700914}], "parameters": [{"name": "locator", "value": "'role=textbox[name='请输入密码']'"}, {"name": "text", "value": "'Scf123456.'"}, {"name": "clear_first", "value": "True"}, {"name": "timeout", "value": "None"}], "start": 1751637700553, "stop": 1751637701018}], "parameters": [{"name": "password", "value": "'Scf123456.'"}], "start": 1751637700553, "stop": 1751637701018}, {"name": "自动识别并输入验证码", "status": "broken", "statusDetails": {"message": "AttributeError: 'SCFLoginPage' object has no attribute 'take_screenshot_for_report'\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 133, in auto_input_captcha\n    self.take_screenshot_for_report(\"验证码识别失败\")\n    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "steps": [{"name": "等待元素出现: '.captcha-image'", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.TimeoutError: Locator.wait_for: Timeout 30000ms exceeded.\nCall log:\n  - waiting for locator(\".captcha-image\") to be visible\n\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 192, in wait_for_element\n    element.wait_for(state=state, timeout=timeout or self.default_timeout)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 17937, in wait_for\n    self._sync(self._impl_obj.wait_for(timeout=timeout, state=state))\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 115, in _sync\n    return task.result()\n           ^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py\", line 693, in wait_for\n    await self._frame.wait_for_selector(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py\", line 341, in wait_for_selector\n    await self._channel.send(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 69, in send\n    return await self._connection.wrap_api_call(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 558, in wrap_api_call\n    raise rewrite_error(error, f\"{parsed_st['apiName']}: {error}\") from None\n"}, "parameters": [{"name": "locator", "value": "'.captcha-image'"}, {"name": "state", "value": "'visible'"}, {"name": "timeout", "value": "None"}], "start": 1751637701019, "stop": 1751637731031}], "start": 1751637701018, "stop": 1751637731033}], "parameters": [{"name": "username", "value": "'scf_4nuioc'"}, {"name": "password", "value": "'Scf123456.'"}, {"name": "<PERSON><PERSON>a", "value": "None"}, {"name": "auto_captcha", "value": "True"}], "start": 1751637699614, "stop": 1751637731040}], "parameters": [{"name": "credentials", "value": "{'username': 'scf_4nuioc', 'password': 'Scf123456.', 'description': '标准测试用户'}"}, {"name": "base_url", "value": "'http://*************:8080'"}], "start": 1751637698816, "stop": 1751637731046}], "start": 1751637698816, "stop": 1751637731051}], "attachments": [{"name": "失败截图", "source": "6b9796be-8546-48b8-a0e1-a9e4599cd11e-attachment.png", "type": "image/png"}, {"name": "页面HTML", "source": "968e436c-21d8-4e95-a136-9cf792818097-attachment.html", "type": "text/html"}, {"name": "当前URL", "source": "3f491f4d-e2fe-4017-8c96-8741b1801b1c-attachment.txt", "type": "text/plain"}, {"name": "log", "source": "707f772b-433c-4ab1-a232-2e0a2869ea46-attachment.txt", "type": "text/plain"}, {"name": "stderr", "source": "af4f6360-37f3-4384-b570-c21267eb5459-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "browser_name", "value": "'chromium'"}], "start": 1751637698774, "stop": 1751637731057, "uuid": "a789324d-3d74-4caa-b2ea-b0a2e9c9c3e9", "historyId": "83c32d878021effc5e3dcc4d134c4bd1", "testCaseId": "2cf6f352a9af813bd18233473cb242da", "fullName": "tests.ui.smoke.test_scf_login.TestSCFLogin#test_standard_user_login_success", "labels": [{"name": "epic", "value": "供应链金融备案系统"}, {"name": "severity", "value": "blocker"}, {"name": "feature", "value": "用户登录"}, {"name": "story", "value": "标准用户登录"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "tests.ui.smoke"}, {"name": "suite", "value": "test_scf_login"}, {"name": "subSuite", "value": "TestSC<PERSON><PERSON>in"}, {"name": "host", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "thread", "value": "31596-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.ui.smoke.test_scf_login"}]}