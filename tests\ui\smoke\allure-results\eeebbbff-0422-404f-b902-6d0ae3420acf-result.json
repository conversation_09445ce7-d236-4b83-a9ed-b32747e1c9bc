{"name": "test_login_failure_scenarios[chromium-invalid_user-\\u5e94\\u8be5\\u663e\\u793a\\u9519\\u8bef\\u4fe1\\u606f]", "status": "passed", "description": "测试登录失败场景", "steps": [{"name": "使用invalid_user凭证登录", "status": "passed", "steps": [{"name": "登录系统", "status": "failed", "statusDetails": {"message": "AssertionError: 登录失败，仍在登录页面: http://*************:8080/login\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\workflows\\scf_registration_workflow.py\", line 126, in _login_to_system\n    self.login_page.verify_login_success()\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 366, in verify_login_success\n    assert self.login_url not in current_url, f\"登录失败，仍在登录页面: {current_url}\"\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "steps": [{"name": "导航到登录页面", "status": "passed", "steps": [{"name": "导航到页面: 'http://*************:8080/login'", "status": "passed", "parameters": [{"name": "url", "value": "'http://*************:8080/login'"}, {"name": "wait_until", "value": "'domcontentloaded'"}], "start": 1751853956558, "stop": 1751853957448}], "parameters": [{"name": "base_url", "value": "'http://*************:8080'"}], "start": 1751853956557, "stop": 1751853957448}, {"name": "执行登录操作", "status": "passed", "steps": [{"name": "点击账号登录标签", "status": "passed", "steps": [{"name": "等待元素出现: 'text=账号登录'", "status": "passed", "parameters": [{"name": "locator", "value": "'text=账号登录'"}, {"name": "state", "value": "'visible'"}, {"name": "timeout", "value": "10000"}], "start": 1751853957449, "stop": 1751853957681}, {"name": "智能点击: 'text=账号登录'", "status": "passed", "steps": [{"name": "等待元素稳定: 'text=账号登录'", "status": "passed", "parameters": [{"name": "locator", "value": "'text=账号登录'"}, {"name": "timeout", "value": "None"}], "start": 1751853957681, "stop": 1751853958025}], "parameters": [{"name": "locator", "value": "'text=账号登录'"}, {"name": "force", "value": "False"}, {"name": "timeout", "value": "None"}], "start": 1751853957681, "stop": 1751853958086}], "start": 1751853957449, "stop": 1751853959095}, {"name": "输入用户名: 'invalid_user'", "status": "passed", "steps": [{"name": "智能填充: 'input[placeholder='请输入账号']' = 'invalid_user'", "status": "passed", "steps": [{"name": "等待元素稳定: 'input[placeholder='请输入账号']'", "status": "passed", "parameters": [{"name": "locator", "value": "'input[placeholder='请输入账号']'"}, {"name": "timeout", "value": "None"}], "start": 1751853959096, "stop": 1751853959447}], "parameters": [{"name": "locator", "value": "'input[placeholder='请输入账号']'"}, {"name": "text", "value": "'invalid_user'"}, {"name": "clear_first", "value": "True"}, {"name": "timeout", "value": "None"}], "start": 1751853959095, "stop": 1751853959486}], "parameters": [{"name": "username", "value": "'invalid_user'"}], "start": 1751853959095, "stop": 1751853959486}, {"name": "输入密码", "status": "passed", "steps": [{"name": "智能填充: 'input[type='password']' = 'invalid_password'", "status": "passed", "steps": [{"name": "等待元素稳定: 'input[type='password']'", "status": "passed", "parameters": [{"name": "locator", "value": "'input[type='password']'"}, {"name": "timeout", "value": "None"}], "start": 1751853959486, "stop": 1751853959821}], "parameters": [{"name": "locator", "value": "'input[type='password']'"}, {"name": "text", "value": "'invalid_password'"}, {"name": "clear_first", "value": "True"}, {"name": "timeout", "value": "None"}], "start": 1751853959486, "stop": 1751853959851}], "parameters": [{"name": "password", "value": "'invalid_password'"}], "start": 1751853959486, "stop": 1751853959851}, {"name": "自动识别并输入验证码", "status": "passed", "steps": [{"name": "等待元素出现: '.arco-image img'", "status": "passed", "parameters": [{"name": "locator", "value": "'.arco-image img'"}, {"name": "state", "value": "'visible'"}, {"name": "timeout", "value": "10000"}], "start": 1751853959852, "stop": 1751853959857}, {"name": "输入图形验证码: '9698'", "status": "passed", "steps": [{"name": "智能填充: 'input[placeholder='请输入图形验证码']' = '9698'", "status": "passed", "steps": [{"name": "等待元素稳定: 'input[placeholder='请输入图形验证码']'", "status": "passed", "parameters": [{"name": "locator", "value": "'input[placeholder='请输入图形验证码']'"}, {"name": "timeout", "value": "None"}], "start": 1751853960988, "stop": 1751853961338}], "parameters": [{"name": "locator", "value": "'input[placeholder='请输入图形验证码']'"}, {"name": "text", "value": "'9698'"}, {"name": "clear_first", "value": "True"}, {"name": "timeout", "value": "None"}], "start": 1751853960988, "stop": 1751853961368}], "parameters": [{"name": "<PERSON><PERSON>a", "value": "'9698'"}], "start": 1751853960988, "stop": 1751853961368}], "parameters": [{"name": "max_retries", "value": "3"}], "start": 1751853959851, "stop": 1751853961368}, {"name": "点击登录按钮", "status": "passed", "steps": [{"name": "智能点击: 'button[type='submit']'", "status": "passed", "steps": [{"name": "等待元素稳定: 'button[type='submit']'", "status": "passed", "parameters": [{"name": "locator", "value": "'button[type='submit']'"}, {"name": "timeout", "value": "None"}], "start": 1751853961368, "stop": 1751853961716}], "parameters": [{"name": "locator", "value": "'button[type='submit']'"}, {"name": "force", "value": "False"}, {"name": "timeout", "value": "None"}], "start": 1751853961368, "stop": 1751853961823}], "start": 1751853961368, "stop": 1751853961823}], "parameters": [{"name": "username", "value": "'invalid_user'"}, {"name": "password", "value": "'invalid_password'"}, {"name": "<PERSON><PERSON>a", "value": "None"}, {"name": "auto_captcha", "value": "True"}], "start": 1751853957449, "stop": 1751853961823}, {"name": "验证登录成功", "status": "failed", "statusDetails": {"message": "AssertionError: 登录失败，仍在登录页面: http://*************:8080/login\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 366, in verify_login_success\n    assert self.login_url not in current_url, f\"登录失败，仍在登录页面: {current_url}\"\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "steps": [{"name": "等待页面加载完成", "status": "passed", "parameters": [{"name": "state", "value": "'networkidle'"}, {"name": "timeout", "value": "None"}], "start": 1751853961823, "stop": 1751853961825}], "parameters": [{"name": "expected_url_pattern", "value": "None"}], "start": 1751853961823, "stop": 1751853961825}], "parameters": [{"name": "credentials", "value": "{'username': 'invalid_user', 'password': 'invalid_password'}"}, {"name": "base_url", "value": "'http://*************:8080'"}], "start": 1751853956557, "stop": 1751853961825}], "attachments": [{"name": "登录失败信息", "source": "61fe6fa0-66cc-4a71-a3f6-6565635a61ef-attachment.txt", "type": "text/plain"}], "start": 1751853956557, "stop": 1751853961826}, {"name": "验证登录失败行为", "status": "passed", "start": 1751853961826, "stop": 1751853961826}], "attachments": [{"name": "log", "source": "672c947d-8ab9-464a-baf9-82b9a885d7b8-attachment.txt", "type": "text/plain"}, {"name": "stdout", "source": "f3989e5a-b0fb-4454-998b-f746261f1934-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "browser_name", "value": "'chromium'"}, {"name": "credentials_key", "value": "'invalid_user'"}, {"name": "expected_behavior", "value": "'应该显示错误信息'"}], "start": 1751853956515, "stop": 1751853961826, "uuid": "772c4ebf-26ac-4ef0-9ab3-1222615e9a74", "historyId": "14f7173cd53e4fc1432a01c8e67b0401", "testCaseId": "a99cc8e5e6e05ba82a9dc1ae7049de1b", "fullName": "tests.ui.smoke.test_scf_login.TestSCFLogin#test_login_failure_scenarios", "labels": [{"name": "feature", "value": "用户登录"}, {"name": "epic", "value": "供应链金融备案系统"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "登录失败场景"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "tests.ui.smoke"}, {"name": "suite", "value": "test_scf_login"}, {"name": "subSuite", "value": "TestSC<PERSON><PERSON>in"}, {"name": "host", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "thread", "value": "14940-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.ui.smoke.test_scf_login"}]}