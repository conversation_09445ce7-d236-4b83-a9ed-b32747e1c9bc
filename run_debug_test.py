#!/usr/bin/env python3
"""
调试模式运行测试脚本
显示浏览器界面，便于观察测试执行过程
"""

import subprocess
import sys
import argparse
from pathlib import Path

def run_debug_test(test_file=None, test_function=None, browser="chromium", slowmo=1000):
    """
    以调试模式运行测试
    
    Args:
        test_file: 测试文件路径
        test_function: 具体测试函数
        browser: 浏览器类型 (chromium, firefox, webkit)
        slowmo: 慢速执行延迟（毫秒）
    """
    
    # 基础命令
    cmd = [
        "uv", "run", "pytest",
        "--headed",  # 显示浏览器界面
        f"--slowmo={slowmo}",  # 慢速执行
        f"--browser={browser}",  # 指定浏览器
        "--browser-channel=chrome" if browser == "chromium" else "",
        "-v",  # 详细输出
        "-s",  # 不捕获输出
        "--tb=short",  # 简短的错误追踪
        "--alluredir=allure-results",  # Allure 报告
    ]
    
    # 移除空字符串
    cmd = [c for c in cmd if c]
    
    # 添加测试文件
    if test_file:
        if test_function:
            cmd.append(f"{test_file}::{test_function}")
        else:
            cmd.append(test_file)
    else:
        # 默认运行注册测试
        cmd.append("tests/ui/smoke/test_scf_registration.py")
    
    print(f"执行命令: {' '.join(cmd)}")
    print("=" * 60)
    
    try:
        # 运行测试
        result = subprocess.run(cmd, cwd=Path.cwd())
        return result.returncode
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        return 1
    except Exception as e:
        print(f"运行测试时出错: {e}")
        return 1

def main():
    parser = argparse.ArgumentParser(description="调试模式运行自动化测试")
    parser.add_argument("--test-file", "-f", help="测试文件路径")
    parser.add_argument("--test-function", "-t", help="具体测试函数")
    parser.add_argument("--browser", "-b", choices=["chromium", "firefox", "webkit"], 
                       default="chromium", help="浏览器类型")
    parser.add_argument("--slowmo", "-s", type=int, default=1000, 
                       help="慢速执行延迟（毫秒）")
    
    args = parser.parse_args()
    
    print("🚀 启动调试模式测试")
    print(f"📁 测试文件: {args.test_file or 'tests/ui/smoke/test_scf_registration.py'}")
    print(f"🔧 测试函数: {args.test_function or '全部'}")
    print(f"🌐 浏览器: {args.browser}")
    print(f"⏱️  慢速延迟: {args.slowmo}ms")
    print("=" * 60)
    
    return run_debug_test(
        test_file=args.test_file,
        test_function=args.test_function,
        browser=args.browser,
        slowmo=args.slowmo
    )

if __name__ == "__main__":
    sys.exit(main())
