import logging
from logging.handlers import RotatingFileHandler
import os
import sys
import threading
from pathlib import Path
import time

# 全局锁，确保日志配置的线程安全
_logger_lock = threading.Lock()
_configured_loggers: dict[str, logging.Logger] = {}
_handlers_initialized = False


class SafeRotatingFileHandler(RotatingFileHandler):
    """
    Windows 安全的文件轮转处理器
    解决 Windows 环境下的文件锁定问题
    """

    def __init__(self, filename, mode='a', maxBytes=0, backupCount=0, encoding=None, delay=False):
        # 在 Windows 上禁用延迟，确保文件句柄正确管理
        # delay 参数被忽略，强制设为 False
        super().__init__(filename, mode, maxBytes, backupCount, encoding, delay=False)

    def doRollover(self):
        """
        执行日志轮转，增加错误处理和重试机制
        """
        if self.stream:
            self.stream.close()
            self.stream = None

        # 在 Windows 上，添加重试机制
        max_retries = 3
        for attempt in range(max_retries):
            try:
                super().doRollover()
                break
            except (OSError, PermissionError) as e:
                if attempt < max_retries - 1:
                    # 等待一小段时间后重试
                    time.sleep(0.1 * (attempt + 1))
                    continue
                else:
                    # 如果所有重试都失败，记录错误但不中断程序
                    print(f"日志轮转失败: {e}", file=sys.stderr)
                    break

        # 重新打开文件流
        if not self.stream:
            self.stream = self._open()


def _setup_global_handlers():
    """
    设置全局日志处理器（只执行一次）
    """
    global _handlers_initialized

    if _handlers_initialized:
        return

    # 创建日志目录
    log_dir = Path(__file__).parent.parent / "logs"
    log_dir.mkdir(exist_ok=True)

    # 获取根日志器
    root_logger = logging.getLogger()

    # 清除现有的处理器（避免重复）
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # 设置根日志器级别
    root_logger.setLevel(logging.DEBUG)

    # 创建控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_format = logging.Formatter('%(name)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(console_format)

    # 创建文件处理器（使用安全的轮转处理器）
    file_handler = SafeRotatingFileHandler(
        log_dir / 'all.log',
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    file_handler.setLevel(logging.DEBUG)
    file_format = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(file_format)

    # 创建错误日志处理器
    error_handler = SafeRotatingFileHandler(
        log_dir / 'error.log',
        maxBytes=5*1024*1024,  # 5MB
        backupCount=3,
        encoding='utf-8'
    )
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(file_format)

    # 添加处理器到根日志器
    root_logger.addHandler(console_handler)
    root_logger.addHandler(file_handler)
    root_logger.addHandler(error_handler)

    _handlers_initialized = True


def get_logger(name: str) -> logging.Logger:
    """
    获取线程安全的日志器实例

    Args:
        name: 日志器名称

    Returns:
        配置好的日志器实例
    """
    with _logger_lock:
        # 确保全局处理器已初始化
        _setup_global_handlers()

        # 检查是否已经配置过这个日志器
        if name in _configured_loggers:
            return _configured_loggers[name]

        # 创建新的日志器
        logger = logging.getLogger(name)

        # 设置日志级别
        logger.setLevel(logging.DEBUG)

        # 不传播到父日志器（避免重复输出）
        logger.propagate = True

        # 缓存配置好的日志器
        _configured_loggers[name] = logger

        return logger


def cleanup_old_logs(days: int = 7):
    """
    清理旧的日志文件

    Args:
        days: 保留天数
    """
    log_dir = Path(__file__).parent.parent / "logs"
    if not log_dir.exists():
        return

    cutoff_time = time.time() - (days * 24 * 60 * 60)

    try:
        for log_file in log_dir.glob("*.log.*"):
            if log_file.stat().st_mtime < cutoff_time:
                try:
                    log_file.unlink()
                    print(f"已删除旧日志文件: {log_file}")
                except OSError as e:
                    print(f"删除日志文件失败 {log_file}: {e}")
    except Exception as e:
        print(f"清理日志文件时出错: {e}")


def reset_logging():
    """
    重置日志配置（主要用于测试）
    """
    global _handlers_initialized, _configured_loggers

    with _logger_lock:
        # 清理所有处理器
        root_logger = logging.getLogger()
        for handler in root_logger.handlers[:]:
            handler.close()
            root_logger.removeHandler(handler)

        # 重置状态
        _handlers_initialized = False
        _configured_loggers.clear()
