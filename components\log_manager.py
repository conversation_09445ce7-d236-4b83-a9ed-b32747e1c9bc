import logging
from logging.handlers import RotatingFileHand<PERSON>, TimedRotatingFileHandler
import os
from pathlib import Path

def get_logger(name):
    logger = logging.getLogger(name)
    if not logger.handlers:
        logger.setLevel(logging.DEBUG)
        log_dir = Path(__file__).parent.parent / "logs"
        log_dir.mkdir(exist_ok=True)
        # Create handlers
        c_handler = logging.StreamHandler()
        f_handler = TimedRotatingFileHandler(log_dir / 'all.log', when='midnight', backupCount=7)
        e_handler = RotatingFileHandler(log_dir / 'error.log', maxBytes=1024*1024*5, backupCount=5)

        # Create formatters and add it to handlers
        c_format = logging.Formatter('%(name)s - %(levelname)s - %(message)s')
        f_format = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        c_handler.setFormatter(c_format)
        f_handler.setFormatter(f_format)
        e_handler.setFormatter(f_format)

        # Add handlers to the logger
        logger.addHandler(c_handler)
        logger.addHandler(f_handler)
        logger.addHandler(e_handler)

    return logger
