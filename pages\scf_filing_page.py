#!/usr/bin/env python3
"""
SCF 备案申请页面对象
包含多步骤备案申请流程
"""

import allure
from playwright.sync_api import Page
from pages.base_page import BasePage
from components.log_manager import get_logger
from pathlib import Path
# 移除未使用的导入


class SCFFilingPage(BasePage):
    """SCF 备案申请页面对象类"""
    
    def __init__(self, page: Page):
        super().__init__(page)
        self.logger = get_logger(__name__)
        
        # 通用元素定位符
        self.next_button = "role=button[name='下一步']"
        self.save_button = "role=button[name='保存']"
        self.previous_button = "role=button[name='上一步']"
        self.submit_button = "role=button[name='提交']"
        
        # 文件上传相关
        self.upload_card = ".upload-card"
        self.upload_icon_text = ".upload-icon-text"
        self.upload_attachment_text = "text=点击此处上传附件"
        
        # 单选按钮
        self.radio_yes = "text=是"
        self.radio_no = "text=否"
        
        # 文本输入框
        self.description_textbox = "role=textbox[name='情况说明（必填），最多1000字符']"
        
        # 专业机构选择
        self.institution_selector = "role=textbox[name='请选择专业机构']"
        self.institution_option = "text=天津中互金数据科技有限公司"
        
    @allure.step("点击下一步")
    def click_next_button(self):
        """点击下一步按钮"""
        self.logger.info("点击下一步按钮")
        self.click(self.next_button)
        # 等待页面加载
        self.page.wait_for_timeout(2000)
        return self
    
    @allure.step("点击保存")
    def click_save_button(self):
        """点击保存按钮"""
        self.logger.info("点击保存按钮")
        self.click(self.save_button)
        return self
    
    @allure.step("点击上一步")
    def click_previous_button(self):
        """点击上一步按钮"""
        self.logger.info("点击上一步按钮")
        self.click(self.previous_button)
        return self
    
    @allure.step("上传文件: {file_path}")
    def upload_file(self, file_path: str, upload_selector: str = None):
        """
        上传文件
        
        Args:
            file_path: 文件路径
            upload_selector: 上传按钮选择器（可选）
        """
        self.logger.info(f"上传文件: {file_path}")
        
        try:
            # 如果指定了上传选择器，先点击它
            if upload_selector:
                self.click(upload_selector)
            else:
                # 使用通用的上传卡片
                self.click(f"{self.upload_card}.first")
            
            # 设置文件
            self.page.locator("body").set_input_files(file_path)
            
            # 等待上传完成
            self.page.wait_for_timeout(1000)
            
            self.logger.info(f"文件上传成功: {file_path}")
            
        except Exception as e:
            self.logger.error(f"文件上传失败: {e}")
            raise
        
        return self
    
    @allure.step("选择单选按钮: {option} 在 {field_id}")
    def select_radio_option(self, field_id: str, option: str):
        """
        选择单选按钮选项
        
        Args:
            field_id: 字段ID
            option: 选项值 ("是" 或 "否")
        """
        self.logger.info(f"在字段 {field_id} 选择选项: {option}")
        
        try:
            selector = f"#{field_id}"
            radio_selector = f"{selector} >> text={option}"
            self.click(radio_selector)
            
            self.logger.info(f"成功选择选项: {option}")
            
        except Exception as e:
            self.logger.error(f"选择单选按钮失败: {e}")
            raise
        
        return self
    
    @allure.step("填写情况说明: {description} 在 {field_id}")
    def fill_description(self, field_id: str, description: str):
        """
        填写情况说明
        
        Args:
            field_id: 字段ID
            description: 说明内容
        """
        self.logger.info(f"在字段 {field_id} 填写情况说明")
        
        try:
            # 尝试多种选择器
            selectors = [
                f"#{field_id} >> {self.description_textbox}",
                f"#{field_id}Des >> {self.description_textbox}",
                f"#{field_id} .arco-textarea",
                f"#{field_id}Des .arco-textarea"
            ]
            
            filled = False
            for selector in selectors:
                try:
                    if self.is_visible(selector):
                        self.fill(selector, description)
                        filled = True
                        break
                except:
                    continue
            
            if not filled:
                # 使用通用选择器
                self.fill(self.description_textbox, description)
            
            self.logger.info(f"成功填写情况说明: {description}")
            
        except Exception as e:
            self.logger.error(f"填写情况说明失败: {e}")
            raise
        
        return self
    
    @allure.step("上传附件到字段: {field_id}")
    def upload_attachment_to_field(self, field_id: str, file_path: str):
        """
        上传附件到指定字段
        
        Args:
            field_id: 字段ID
            file_path: 文件路径
        """
        self.logger.info(f"上传附件到字段: {field_id}")
        
        try:
            # 构建上传按钮选择器
            upload_selector = f"#{field_id} >> {self.upload_attachment_text}"
            
            # 如果找不到，尝试其他选择器
            if not self.is_visible(upload_selector):
                upload_selector = f"#{field_id} >> {self.upload_card}"
            
            self.upload_file(file_path, upload_selector)
            
            self.logger.info(f"成功上传附件到字段: {field_id}")
            
        except Exception as e:
            self.logger.error(f"上传附件失败: {e}")
            raise
        
        return self
    
    @allure.step("选择专业机构")
    def select_institution(self, institution_name: str = "天津中互金数据科技有限公司"):
        """
        选择专业机构
        
        Args:
            institution_name: 机构名称
        """
        self.logger.info(f"选择专业机构: {institution_name}")
        
        try:
            # 点击选择框
            self.click(self.institution_selector)
            
            # 选择机构
            self.click(f"text={institution_name}")
            
            self.logger.info(f"成功选择专业机构: {institution_name}")
            
        except Exception as e:
            self.logger.error(f"选择专业机构失败: {e}")
            raise
        
        return self
    
    @allure.step("验证当前步骤页面")
    def verify_current_step(self, step_indicator: str = None):
        """
        验证当前步骤页面是否正确加载
        
        Args:
            step_indicator: 步骤指示器（可选）
        """
        self.logger.info("验证当前步骤页面")
        
        try:
            # 等待页面稳定
            self.wait_for_load_state("networkidle")
            
            # 验证下一步按钮存在（除了最后一步）
            if self.is_visible(self.next_button):
                self.logger.info("发现下一步按钮，当前不是最后一步")
            elif self.is_visible(self.save_button):
                self.logger.info("发现保存按钮，可能是最后一步")
            
            self.logger.info("当前步骤页面验证完成")
            
        except Exception as e:
            self.logger.error(f"验证当前步骤页面失败: {e}")
            raise
        
        return self

    # 具体步骤方法

    @allure.step("完成第一步：基础材料上传")
    def complete_step1_basic_materials(self, test_files: dict[str, str]):
        """
        完成第一步：基础材料上传

        Args:
            test_files: 测试文件字典
        """
        self.logger.info("开始第一步：基础材料上传")

        try:
            # 上传基础文档
            if "basic_doc" in test_files:
                self.upload_file(test_files["basic_doc"], f"{self.upload_card}.first")

            # 上传关联文档
            if "assoc_doc" in test_files:
                self.upload_attachment_to_field("bfmArtAssocId", test_files["assoc_doc"])

            # 点击下一步
            self.click_next_button()

            self.logger.info("第一步完成：基础材料上传")

        except Exception as e:
            self.logger.error(f"第一步失败: {e}")
            raise

        return self

    @allure.step("完成第二步：补充材料上传")
    def complete_step2_supplementary_materials(self, test_files: dict[str, str]):
        """
        完成第二步：补充材料上传

        Args:
            test_files: 测试文件字典
        """
        self.logger.info("开始第二步：补充材料上传")

        try:
            # 上传各种补充材料
            material_fields = [
                "bfmArtAssocId", "bfmAuditRptId", "bfmFrameCoopId",
                "bfmClearSetId", "bfmICPCertId", "bfmSCStatusId"
            ]

            for field in material_fields:
                if "supplement_doc" in test_files:
                    self.upload_attachment_to_field(field, test_files["supplement_doc"])

            # 点击下一步
            self.click_next_button()

            self.logger.info("第二步完成：补充材料上传")

        except Exception as e:
            self.logger.error(f"第二步失败: {e}")
            raise

        return self
