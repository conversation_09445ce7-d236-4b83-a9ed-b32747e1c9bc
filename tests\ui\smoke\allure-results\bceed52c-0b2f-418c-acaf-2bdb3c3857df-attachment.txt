INFO     fixtures.scf_fixtures:scf_fixtures.py:79 准备SCF登录凭证
INFO     components.ocr_util:ocr_util.py:60 OCR引擎初始化成功
INFO     SCFRegistrationWorkflow:scf_registration_workflow.py:110 开始登录系统
INFO     SCFLoginPage:scf_login_page.py:55 导航到登录页面: http://172.18.12.128:8080/login
INFO     pages.base_page:base_page.py:30 导航到页面: http://172.18.12.128:8080/login
INFO     SCFLoginPage:scf_login_page.py:272 开始登录流程，用户名: admin
INFO     SCFLoginPage:scf_login_page.py:62 点击账号登录标签
INFO     pages.base_page:base_page.py:190 等待元素 text=账号登录 状态: visible
DEBUG    components.retry_util:retry_util.py:87 执行函数 click，尝试 1/3
INFO     pages.base_page:base_page.py:106 点击元素: text=账号登录
DEBUG    pages.base_page:base_page.py:65 元素 text=账号登录 已稳定
INFO     SCFLoginPage:scf_login_page.py:95 输入用户名: admin
DEBUG    components.retry_util:retry_util.py:87 执行函数 fill，尝试 1/3
INFO     pages.base_page:base_page.py:130 填充文本到元素 input[placeholder='请输入账号']: admin
DEBUG    pages.base_page:base_page.py:65 元素 input[placeholder='请输入账号'] 已稳定
INFO     SCFLoginPage:scf_login_page.py:119 输入密码
DEBUG    components.retry_util:retry_util.py:87 执行函数 fill，尝试 1/3
INFO     pages.base_page:base_page.py:130 填充文本到元素 input[type='password']: Admin123456.
DEBUG    pages.base_page:base_page.py:65 元素 input[type='password'] 已稳定
INFO     SCFLoginPage:scf_login_page.py:166 开始自动识别验证码
INFO     SCFLoginPage:scf_login_page.py:170 验证码识别尝试 1/3
DEBUG    SCFLoginPage:scf_login_page.py:183 尝试选择器: .arco-image img
INFO     pages.base_page:base_page.py:190 等待元素 .arco-image img 状态: visible
INFO     SCFLoginPage:scf_login_page.py:187 找到验证码元素，使用选择器: .arco-image img
DEBUG    SCFLoginPage:scf_login_page.py:206 验证码图片已保存到: logs/captcha_debug_1.png
DEBUG    PIL.PngImagePlugin:PngImagePlugin.py:201 STREAM b'IHDR' 16 13
DEBUG    PIL.PngImagePlugin:PngImagePlugin.py:201 STREAM b'sRGB' 41 1
DEBUG    PIL.PngImagePlugin:PngImagePlugin.py:201 STREAM b'IDAT' 54 4558
DEBUG    PIL.PngImagePlugin:PngImagePlugin.py:201 STREAM b'IHDR' 16 13
DEBUG    PIL.PngImagePlugin:PngImagePlugin.py:201 STREAM b'sRGB' 41 1
DEBUG    PIL.PngImagePlugin:PngImagePlugin.py:201 STREAM b'IDAT' 54 4558
WARNING  components.ocr_util:ocr_util.py:260 图像复杂度分析失败: name 'ImageFilter' is not defined
INFO     components.ocr_util:ocr_util.py:87 OCR识别结果: 2549, 置信度: 0.8
INFO     SCFLoginPage:scf_login_page.py:215 验证码识别结果: 2549, 置信度: 0.8
INFO     SCFLoginPage:scf_login_page.py:132 输入图形验证码: 2549
DEBUG    components.retry_util:retry_util.py:87 执行函数 fill，尝试 1/3
INFO     pages.base_page:base_page.py:130 填充文本到元素 input[placeholder='请输入图形验证码']: 2549
DEBUG    pages.base_page:base_page.py:65 元素 input[placeholder='请输入图形验证码'] 已稳定
INFO     SCFLoginPage:scf_login_page.py:257 点击登录按钮
DEBUG    components.retry_util:retry_util.py:87 执行函数 click，尝试 1/3
INFO     pages.base_page:base_page.py:106 点击元素: button[type='submit']
DEBUG    pages.base_page:base_page.py:65 元素 button[type='submit'] 已稳定
INFO     SCFLoginPage:scf_login_page.py:297 登录操作完成
INFO     SCFLoginPage:scf_login_page.py:353 验证登录成功状态
INFO     pages.base_page:base_page.py:410 等待页面加载状态: networkidle