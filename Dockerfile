# 企业级 Web 自动化测试平台 Dockerfile
# 基于 Playwright 官方镜像，使用 uv 进行依赖管理

FROM mcr.microsoft.com/playwright/python:v1.49.0-jammy

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV UV_CACHE_DIR=/tmp/uv-cache

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# 安装 uv
RUN curl -LsSf https://astral.sh/uv/install.sh | sh
ENV PATH="/root/.cargo/bin:$PATH"

# 复制项目文件
COPY pyproject.toml uv.lock ./

# 创建虚拟环境并安装依赖
RUN uv venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# 安装项目依赖
RUN uv pip install -e ".[dev]"

# 安装 Playwright 浏览器
RUN playwright install chromium
RUN playwright install-deps chromium

# 复制项目源码
COPY . .

# 创建必要的目录
RUN mkdir -p logs allure-results reports

# 设置权限
RUN chmod +x /app

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import playwright; print('Playwright is ready')" || exit 1

# 默认命令
CMD ["uv", "run", "pytest", "--alluredir=allure-results", "-v"]

# 标签信息
LABEL maintainer="Test Team"
LABEL version="1.0.0"
LABEL description="企业级 Web 自动化测试平台"
LABEL org.opencontainers.image.source="https://github.com/your-org/playwright-python-template"
