{"name": "test_login_page_elements[chromium]", "status": "passed", "description": "验证登录页面必要元素存在", "steps": [{"name": "导航到登录页面", "status": "passed", "steps": [{"name": "导航到登录页面", "status": "passed", "steps": [{"name": "导航到页面: 'http://*************:8080/login'", "status": "passed", "parameters": [{"name": "url", "value": "'http://*************:8080/login'"}, {"name": "wait_until", "value": "'domcontentloaded'"}], "start": 1751637797769, "stop": 1751637798486}], "parameters": [{"name": "base_url", "value": "'http://*************:8080'"}], "start": 1751637797769, "stop": 1751637798486}], "start": 1751637797769, "stop": 1751637798487}, {"name": "验证登录页面元素", "status": "passed", "steps": [{"name": "检查元素是否可见: 'text=账号登录'", "status": "passed", "parameters": [{"name": "locator", "value": "'text=账号登录'"}, {"name": "timeout", "value": "None"}], "start": 1751637798487, "stop": 1751637798597}, {"name": "点击账号登录标签", "status": "passed", "steps": [{"name": "智能点击: 'text=账号登录'", "status": "passed", "steps": [{"name": "等待元素稳定: 'text=账号登录'", "status": "passed", "parameters": [{"name": "locator", "value": "'text=账号登录'"}, {"name": "timeout", "value": "None"}], "start": 1751637798599, "stop": 1751637799007}], "parameters": [{"name": "locator", "value": "'text=账号登录'"}, {"name": "force", "value": "False"}, {"name": "timeout", "value": "None"}], "start": 1751637798598, "stop": 1751637799106}], "start": 1751637798597, "stop": 1751637799106}, {"name": "检查元素是否可见: 'role=textbox[name='请输入账号']'", "status": "passed", "parameters": [{"name": "locator", "value": "'role=textbox[name='请输入账号']'"}, {"name": "timeout", "value": "None"}], "start": 1751637799106, "stop": 1751637799117}, {"name": "检查元素是否可见: 'role=textbox[name='请输入密码']'", "status": "passed", "parameters": [{"name": "locator", "value": "'role=textbox[name='请输入密码']'"}, {"name": "timeout", "value": "None"}], "start": 1751637799117, "stop": 1751637799126}, {"name": "检查元素是否可见: 'role=textbox[name='请输入图形验证码']'", "status": "passed", "parameters": [{"name": "locator", "value": "'role=textbox[name='请输入图形验证码']'"}, {"name": "timeout", "value": "None"}], "start": 1751637799126, "stop": 1751637799136}, {"name": "检查元素是否可见: 'role=button[name='登录']'", "status": "passed", "parameters": [{"name": "locator", "value": "'role=button[name='登录']'"}, {"name": "timeout", "value": "None"}], "start": 1751637799136, "stop": 1751637799147}], "start": 1751637798487, "stop": 1751637799147}], "attachments": [{"name": "log", "source": "216e3133-7c27-4b82-9ddc-e839b79d15a5-attachment.txt", "type": "text/plain"}, {"name": "stderr", "source": "c9faf382-e5d4-4e1f-ae32-409df38f806e-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "browser_name", "value": "'chromium'"}], "start": 1751637797651, "stop": 1751637799148, "uuid": "5c28aca8-30c9-47ad-bcb3-de803f37b700", "historyId": "7869c781375e544239aebc49d435f721", "testCaseId": "bf3beae4a949bf431d6c971553df874b", "fullName": "tests.ui.smoke.test_scf_login.TestSCFLogin#test_login_page_elements", "labels": [{"name": "epic", "value": "供应链金融备案系统"}, {"name": "story", "value": "登录页面元素验证"}, {"name": "severity", "value": "minor"}, {"name": "feature", "value": "用户登录"}, {"name": "tag", "value": "component"}, {"name": "parentSuite", "value": "tests.ui.smoke"}, {"name": "suite", "value": "test_scf_login"}, {"name": "subSuite", "value": "TestSC<PERSON><PERSON>in"}, {"name": "host", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "thread", "value": "31596-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.ui.smoke.test_scf_login"}]}