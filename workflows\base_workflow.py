"""
基础业务流程类
提供通用的业务流程功能和工具方法
"""

import allure
from playwright.sync_api import Page
from components.log_manager import get_logger
from typing import Optional, Dict, Any

logger = get_logger(__name__)


class BaseWorkflow:
    """基础业务流程类"""
    
    def __init__(self, page: Page):
        """
        初始化基础流程
        
        Args:
            page: Playwright页面对象
        """
        self.page = page
        self.logger = get_logger(self.__class__.__name__)
    
    @allure.step("等待页面加载完成")
    def wait_for_page_load(self, timeout: Optional[int] = None):
        """
        等待页面加载完成
        
        Args:
            timeout: 超时时间（毫秒）
        """
        self.logger.info("等待页面加载完成")
        self.page.wait_for_load_state("domcontentloaded", timeout=timeout)
        self.page.wait_for_load_state("networkidle", timeout=timeout or 30000)
    
    @allure.step("导航到页面: {url}")
    def navigate_to(self, url: str, wait_for_load: bool = True):
        """
        导航到指定页面
        
        Args:
            url: 目标URL
            wait_for_load: 是否等待页面加载完成
        """
        self.logger.info(f"导航到页面: {url}")
        self.page.goto(url)
        
        if wait_for_load:
            self.wait_for_page_load()
    
    @allure.step("验证当前页面URL")
    def verify_current_url(self, expected_url: str, exact_match: bool = False):
        """
        验证当前页面URL
        
        Args:
            expected_url: 期望的URL
            exact_match: 是否精确匹配
        """
        current_url = self.page.url
        self.logger.info(f"验证URL - 当前: {current_url}, 期望: {expected_url}")
        
        if exact_match:
            assert current_url == expected_url, f"URL不匹配: 期望 {expected_url}, 实际 {current_url}"
        else:
            assert expected_url in current_url, f"URL不包含期望内容: 期望包含 {expected_url}, 实际 {current_url}"
    
    @allure.step("验证页面标题")
    def verify_page_title(self, expected_title: str, exact_match: bool = False):
        """
        验证页面标题
        
        Args:
            expected_title: 期望的标题
            exact_match: 是否精确匹配
        """
        current_title = self.page.title()
        self.logger.info(f"验证标题 - 当前: {current_title}, 期望: {expected_title}")
        
        if exact_match:
            assert current_title == expected_title, f"标题不匹配: 期望 {expected_title}, 实际 {current_title}"
        else:
            assert expected_title in current_title, f"标题不包含期望内容: 期望包含 {expected_title}, 实际 {current_title}"
    
    @allure.step("等待元素出现并验证")
    def wait_and_verify_element(self, locator: str, timeout: Optional[int] = None, should_be_visible: bool = True):
        """
        等待元素出现并验证其状态
        
        Args:
            locator: 元素定位符
            timeout: 超时时间
            should_be_visible: 元素是否应该可见
        """
        self.logger.info(f"等待并验证元素: {locator}")
        
        if should_be_visible:
            self.page.locator(locator).wait_for(state="visible", timeout=timeout or 30000)
            assert self.page.locator(locator).is_visible(), f"元素应该可见但实际不可见: {locator}"
        else:
            self.page.locator(locator).wait_for(state="hidden", timeout=timeout or 30000)
            assert not self.page.locator(locator).is_visible(), f"元素应该隐藏但实际可见: {locator}"
    
    @allure.step("填写表单数据")
    def fill_form_data(self, form_data: Dict[str, str], form_selector: str = "form"):
        """
        批量填写表单数据
        
        Args:
            form_data: 表单数据字典，键为字段定位符，值为要填入的内容
            form_selector: 表单选择器
        """
        self.logger.info(f"填写表单数据，字段数量: {len(form_data)}")
        
        for field_locator, value in form_data.items():
            with allure.step(f"填写字段 {field_locator} = {value}"):
                element = self.page.locator(field_locator)
                element.wait_for(state="visible")
                element.clear()
                element.fill(str(value))
                self.logger.debug(f"已填写字段: {field_locator} = {value}")
    
    @allure.step("提交表单")
    def submit_form(self, submit_button_locator: str = "button[type='submit']", wait_for_response: bool = True):
        """
        提交表单
        
        Args:
            submit_button_locator: 提交按钮定位符
            wait_for_response: 是否等待响应
        """
        self.logger.info("提交表单")
        
        if wait_for_response:
            with self.page.expect_response(lambda response: response.status < 400) as response_info:
                self.page.locator(submit_button_locator).click()
            response = response_info.value
            self.logger.info(f"表单提交响应: {response.status} {response.url}")
        else:
            self.page.locator(submit_button_locator).click()
    
    @allure.step("处理弹窗")
    def handle_dialog(self, action: str = "accept", prompt_text: str = ""):
        """
        处理JavaScript弹窗
        
        Args:
            action: 操作类型 ('accept', 'dismiss')
            prompt_text: 如果是prompt弹窗，要输入的文本
        """
        self.logger.info(f"处理弹窗，操作: {action}")
        
        def dialog_handler(dialog):
            self.logger.info(f"弹窗消息: {dialog.message}")
            if dialog.type == "prompt" and prompt_text:
                dialog.accept(prompt_text)
            elif action == "accept":
                dialog.accept()
            else:
                dialog.dismiss()
        
        self.page.on("dialog", dialog_handler)
    
    @allure.step("截图并附加到报告")
    def take_screenshot_for_report(self, name: str = "screenshot"):
        """
        截图并附加到Allure报告
        
        Args:
            name: 截图名称
        """
        self.logger.info(f"截图: {name}")
        screenshot = self.page.screenshot()
        allure.attach(
            screenshot,
            name=name,
            attachment_type=allure.attachment_type.PNG
        )
    
    @allure.step("等待网络空闲")
    def wait_for_network_idle(self, timeout: Optional[int] = None):
        """
        等待网络空闲
        
        Args:
            timeout: 超时时间（毫秒）
        """
        self.logger.info("等待网络空闲")
        self.page.wait_for_load_state("networkidle", timeout=timeout or 30000)
    
    @allure.step("验证元素文本内容")
    def verify_element_text(self, locator: str, expected_text: str, exact_match: bool = False):
        """
        验证元素文本内容
        
        Args:
            locator: 元素定位符
            expected_text: 期望的文本
            exact_match: 是否精确匹配
        """
        element = self.page.locator(locator)
        element.wait_for(state="visible")
        actual_text = element.text_content() or ""
        
        self.logger.info(f"验证元素文本 - 定位符: {locator}, 期望: {expected_text}, 实际: {actual_text}")
        
        if exact_match:
            assert actual_text == expected_text, f"文本不匹配: 期望 '{expected_text}', 实际 '{actual_text}'"
        else:
            assert expected_text in actual_text, f"文本不包含期望内容: 期望包含 '{expected_text}', 实际 '{actual_text}'"
    
    @allure.step("验证元素属性")
    def verify_element_attribute(self, locator: str, attribute: str, expected_value: str):
        """
        验证元素属性值
        
        Args:
            locator: 元素定位符
            attribute: 属性名
            expected_value: 期望的属性值
        """
        element = self.page.locator(locator)
        element.wait_for(state="visible")
        actual_value = element.get_attribute(attribute) or ""
        
        self.logger.info(f"验证元素属性 - 定位符: {locator}, 属性: {attribute}, 期望: {expected_value}, 实际: {actual_value}")
        assert actual_value == expected_value, f"属性值不匹配: 期望 '{expected_value}', 实际 '{actual_value}'"
    
    @allure.step("滚动到页面底部")
    def scroll_to_bottom(self):
        """滚动到页面底部"""
        self.logger.info("滚动到页面底部")
        self.page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
    
    @allure.step("滚动到页面顶部")
    def scroll_to_top(self):
        """滚动到页面顶部"""
        self.logger.info("滚动到页面顶部")
        self.page.evaluate("window.scrollTo(0, 0)")
    
    @allure.step("刷新页面")
    def refresh_page(self, wait_for_load: bool = True):
        """
        刷新页面
        
        Args:
            wait_for_load: 是否等待页面加载完成
        """
        self.logger.info("刷新页面")
        self.page.reload()
        
        if wait_for_load:
            self.wait_for_page_load()
    
    def get_current_url(self) -> str:
        """获取当前页面URL"""
        return self.page.url
    
    def get_page_title(self) -> str:
        """获取页面标题"""
        return self.page.title()
    
    def add_allure_environment_info(self, key: str, value: str):
        """
        添加环境信息到Allure报告
        
        Args:
            key: 信息键
            value: 信息值
        """
        allure.dynamic.parameter(key, value)
