"""
ddddOCR 验证码识别封装
支持置信度校验与重试机制
"""

import ddddocr
from typing import Tuple, Optional, Union
import time
import os
from pathlib import Path
from PIL import Image
import io
import base64
from components.log_manager import get_logger

logger = get_logger(__name__)


class OCRResult:
    """OCR识别结果类"""
    
    def __init__(self, text: str, confidence: float, success: bool = True, error_msg: str = ""):
        self.text = text
        self.confidence = confidence
        self.success = success
        self.error_msg = error_msg
    
    def __str__(self):
        return f"OCRResult(text='{self.text}', confidence={self.confidence}, success={self.success})"
    
    def __repr__(self):
        return self.__str__()


class OCRHandler:
    """验证码识别处理器"""
    
    def __init__(self, 
                 confidence_threshold: float = 0.8,
                 max_retries: int = 3,
                 retry_delay: float = 1.0,
                 save_failed_images: bool = True):
        """
        初始化OCR处理器
        
        Args:
            confidence_threshold: 置信度阈值，低于此值认为识别不可信
            max_retries: 最大重试次数
            retry_delay: 重试间隔（秒）
            save_failed_images: 是否保存识别失败的图片
        """
        self.confidence_threshold = confidence_threshold
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.save_failed_images = save_failed_images
        
        # 初始化OCR引擎
        try:
            self.ocr = ddddocr.DdddOcr(show_ad=False)
            logger.info("OCR引擎初始化成功")
        except Exception as e:
            logger.error(f"OCR引擎初始化失败: {e}")
            raise
        
        # 创建失败图片保存目录
        if self.save_failed_images:
            self.failed_images_dir = Path("logs/failed_ocr_images")
            self.failed_images_dir.mkdir(parents=True, exist_ok=True)
    
    def recognize_from_bytes(self, image_bytes: bytes) -> OCRResult:
        """
        从字节数据识别验证码
        
        Args:
            image_bytes: 图片字节数据
            
        Returns:
            OCRResult: 识别结果
        """
        try:
            # 使用ddddocr识别
            result_text = self.ocr.classification(image_bytes)
            
            # 计算置信度（这里使用简单的启发式方法）
            confidence = self._calculate_confidence(result_text, image_bytes)
            
            logger.info(f"OCR识别结果: {result_text}, 置信度: {confidence}")
            
            return OCRResult(
                text=result_text,
                confidence=confidence,
                success=True
            )
            
        except Exception as e:
            logger.error(f"OCR识别失败: {e}")
            return OCRResult(
                text="",
                confidence=0.0,
                success=False,
                error_msg=str(e)
            )
    
    def recognize_from_file(self, image_path: Union[str, Path]) -> OCRResult:
        """
        从文件识别验证码
        
        Args:
            image_path: 图片文件路径
            
        Returns:
            OCRResult: 识别结果
        """
        try:
            with open(image_path, 'rb') as f:
                image_bytes = f.read()
            return self.recognize_from_bytes(image_bytes)
        except Exception as e:
            logger.error(f"读取图片文件失败: {e}")
            return OCRResult(
                text="",
                confidence=0.0,
                success=False,
                error_msg=f"读取文件失败: {e}"
            )
    
    def recognize_from_base64(self, base64_str: str) -> OCRResult:
        """
        从base64字符串识别验证码
        
        Args:
            base64_str: base64编码的图片字符串
            
        Returns:
            OCRResult: 识别结果
        """
        try:
            # 移除可能的数据URL前缀
            if base64_str.startswith('data:image'):
                base64_str = base64_str.split(',')[1]
            
            image_bytes = base64.b64decode(base64_str)
            return self.recognize_from_bytes(image_bytes)
        except Exception as e:
            logger.error(f"解析base64图片失败: {e}")
            return OCRResult(
                text="",
                confidence=0.0,
                success=False,
                error_msg=f"解析base64失败: {e}"
            )
    
    def recognize_with_retry(self, 
                           image_bytes: bytes, 
                           refresh_callback: Optional[callable] = None) -> OCRResult:
        """
        带重试机制的验证码识别
        
        Args:
            image_bytes: 图片字节数据
            refresh_callback: 刷新验证码的回调函数，当置信度低时调用
            
        Returns:
            OCRResult: 最终识别结果
        """
        last_result = None
        
        for attempt in range(self.max_retries + 1):
            logger.info(f"OCR识别尝试 {attempt + 1}/{self.max_retries + 1}")
            
            result = self.recognize_from_bytes(image_bytes)
            last_result = result
            
            if not result.success:
                logger.warning(f"识别失败: {result.error_msg}")
                if attempt < self.max_retries and refresh_callback:
                    logger.info("调用刷新回调函数")
                    try:
                        new_image_bytes = refresh_callback()
                        if new_image_bytes:
                            image_bytes = new_image_bytes
                    except Exception as e:
                        logger.error(f"刷新验证码失败: {e}")
                continue
            
            if result.confidence >= self.confidence_threshold:
                logger.info(f"识别成功，置信度满足要求: {result.confidence}")
                return result
            else:
                logger.warning(f"置信度不足: {result.confidence} < {self.confidence_threshold}")
                
                # 保存低置信度的图片
                if self.save_failed_images:
                    self._save_failed_image(image_bytes, f"low_confidence_{attempt}")
                
                # 如果不是最后一次尝试，且有刷新回调，则刷新验证码
                if attempt < self.max_retries and refresh_callback:
                    logger.info("尝试刷新验证码")
                    try:
                        new_image_bytes = refresh_callback()
                        if new_image_bytes:
                            image_bytes = new_image_bytes
                            time.sleep(self.retry_delay)
                    except Exception as e:
                        logger.error(f"刷新验证码失败: {e}")
        
        # 所有尝试都失败了，保存最后的图片
        if self.save_failed_images and last_result:
            self._save_failed_image(image_bytes, "final_failed")
        
        return last_result or OCRResult("", 0.0, False, "所有识别尝试都失败")
    
    def _calculate_confidence(self, text: str, image_bytes: bytes) -> float:
        """
        计算识别置信度（启发式方法）
        
        Args:
            text: 识别出的文本
            image_bytes: 原始图片字节
            
        Returns:
            float: 置信度值 (0.0-1.0)
        """
        if not text:
            return 0.0
        
        confidence = 0.5  # 基础置信度
        
        # 根据文本长度调整置信度
        if 3 <= len(text) <= 6:  # 常见验证码长度
            confidence += 0.2
        elif len(text) < 3 or len(text) > 8:
            confidence -= 0.2
        
        # 根据字符类型调整置信度
        if text.isalnum():  # 只包含字母和数字
            confidence += 0.1
        
        # 检查是否包含常见的误识别字符
        problematic_chars = ['|', '\\', '/', '`', '~']
        if any(char in text for char in problematic_chars):
            confidence -= 0.2
        
        # 根据图片大小调整置信度（简单估算）
        image_size = len(image_bytes)
        if 1000 < image_size < 50000:  # 合理的验证码图片大小
            confidence += 0.1
        
        return max(0.0, min(1.0, confidence))
    
    def _save_failed_image(self, image_bytes: bytes, suffix: str = ""):
        """
        保存识别失败的图片
        
        Args:
            image_bytes: 图片字节数据
            suffix: 文件名后缀
        """
        try:
            timestamp = int(time.time())
            filename = f"failed_ocr_{timestamp}_{suffix}.png"
            filepath = self.failed_images_dir / filename
            
            with open(filepath, 'wb') as f:
                f.write(image_bytes)
            
            logger.info(f"已保存失败的OCR图片: {filepath}")
        except Exception as e:
            logger.error(f"保存失败图片时出错: {e}")
    
    def set_confidence_threshold(self, threshold: float):
        """设置置信度阈值"""
        self.confidence_threshold = max(0.0, min(1.0, threshold))
        logger.info(f"置信度阈值已设置为: {self.confidence_threshold}")
    
    def cleanup_failed_images(self, days_old: int = 7):
        """
        清理旧的失败图片
        
        Args:
            days_old: 删除多少天前的图片
        """
        if not self.save_failed_images:
            return
        
        try:
            current_time = time.time()
            cutoff_time = current_time - (days_old * 24 * 60 * 60)
            
            deleted_count = 0
            for image_file in self.failed_images_dir.glob("failed_ocr_*.png"):
                if image_file.stat().st_mtime < cutoff_time:
                    image_file.unlink()
                    deleted_count += 1
            
            logger.info(f"已清理 {deleted_count} 个旧的失败OCR图片")
        except Exception as e:
            logger.error(f"清理失败图片时出错: {e}")


# 全局实例
ocr_handler = OCRHandler()


def get_ocr_handler(**kwargs) -> OCRHandler:
    """
    获取OCR处理器实例
    
    Args:
        **kwargs: OCRHandler的初始化参数
        
    Returns:
        OCRHandler实例
    """
    return OCRHandler(**kwargs)
