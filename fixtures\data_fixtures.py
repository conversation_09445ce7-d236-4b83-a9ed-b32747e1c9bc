"""
数据注入与清理 Fixtures
提供测试数据的创建、管理和清理功能
"""

import pytest
from typing import Dict, Any, List, Optional
from components.faker_util import get_faker_factory
from components.log_manager import get_logger
from pathlib import Path
import json
import yaml

logger = get_logger(__name__)


class TestUser:
    """测试用户数据类"""
    
    def __init__(self, username: str, password: str, email: str, **kwargs):
        self.username = username
        self.password = password
        self.email = email
        self.first_name = kwargs.get('first_name', '')
        self.last_name = kwargs.get('last_name', '')
        self.phone = kwargs.get('phone', '')
        self.address = kwargs.get('address', '')
        self.role = kwargs.get('role', 'user')
        self.is_active = kwargs.get('is_active', True)
        
        # 存储额外属性
        for key, value in kwargs.items():
            if not hasattr(self, key):
                setattr(self, key, value)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'username': self.username,
            'password': self.password,
            'email': self.email,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'phone': self.phone,
            'address': self.address,
            'role': self.role,
            'is_active': self.is_active
        }
    
    def __str__(self):
        return f"TestUser(username='{self.username}', email='{self.email}', role='{self.role}')"


class TestProduct:
    """测试商品数据类"""
    
    def __init__(self, name: str, price: float, category: str, **kwargs):
        self.name = name
        self.price = price
        self.category = category
        self.description = kwargs.get('description', '')
        self.brand = kwargs.get('brand', '')
        self.sku = kwargs.get('sku', '')
        self.stock = kwargs.get('stock', 0)
        self.is_active = kwargs.get('is_active', True)
        
        # 存储额外属性
        for key, value in kwargs.items():
            if not hasattr(self, key):
                setattr(self, key, value)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'name': self.name,
            'price': self.price,
            'category': self.category,
            'description': self.description,
            'brand': self.brand,
            'sku': self.sku,
            'stock': self.stock,
            'is_active': self.is_active
        }
    
    def __str__(self):
        return f"TestProduct(name='{self.name}', price={self.price}, category='{self.category}')"


@pytest.fixture(scope="session")
def data_factory():
    """数据工厂 fixture"""
    return get_faker_factory()


@pytest.fixture(scope="function")
def standard_user(data_factory) -> TestUser:
    """标准测试用户"""
    user_data = data_factory.generate_user_data({
        'username': 'test_user_001',
        'password': 'Test123456!',
        'email': '<EMAIL>',
        'role': 'user'
    })
    
    user = TestUser(**user_data)
    logger.info(f"创建标准测试用户: {user}")
    return user


@pytest.fixture(scope="function")
def admin_user(data_factory) -> TestUser:
    """管理员测试用户"""
    user_data = data_factory.generate_user_data({
        'username': 'admin_user_001',
        'password': 'Admin123456!',
        'email': '<EMAIL>',
        'role': 'admin'
    })
    
    user = TestUser(**user_data)
    logger.info(f"创建管理员测试用户: {user}")
    return user


@pytest.fixture(scope="function")
def premium_user(data_factory) -> TestUser:
    """高级用户"""
    user_data = data_factory.generate_user_data({
        'username': 'premium_user_001',
        'password': 'Premium123456!',
        'email': '<EMAIL>',
        'role': 'premium'
    })
    
    user = TestUser(**user_data)
    logger.info(f"创建高级测试用户: {user}")
    return user


@pytest.fixture(scope="function")
def random_user(data_factory) -> TestUser:
    """随机测试用户"""
    user_data = data_factory.generate_user_data()
    user = TestUser(**user_data)
    logger.info(f"创建随机测试用户: {user}")
    return user


@pytest.fixture(scope="function")
def test_product(data_factory) -> TestProduct:
    """测试商品"""
    product_data = data_factory.generate_product_data({
        'name': 'Playwright 测试商品',
        'price': 99.99,
        'category': '测试用品'
    })
    
    product = TestProduct(**product_data)
    logger.info(f"创建测试商品: {product}")
    return product


@pytest.fixture(scope="function")
def random_product(data_factory) -> TestProduct:
    """随机测试商品"""
    product_data = data_factory.generate_product_data()
    product = TestProduct(**product_data)
    logger.info(f"创建随机测试商品: {product}")
    return product


@pytest.fixture(scope="function")
def multiple_users(data_factory) -> List[TestUser]:
    """多个测试用户"""
    users = []
    user_data_list = data_factory.generate_batch_data('user', 5)
    
    for i, user_data in enumerate(user_data_list):
        user_data['username'] = f'test_user_{i+1:03d}'
        user_data['password'] = f'TestPass{i+1:03d}!'
        user = TestUser(**user_data)
        users.append(user)
    
    logger.info(f"创建 {len(users)} 个测试用户")
    return users


@pytest.fixture(scope="function")
def multiple_products(data_factory) -> List[TestProduct]:
    """多个测试商品"""
    products = []
    product_data_list = data_factory.generate_batch_data('product', 5)
    
    for i, product_data in enumerate(product_data_list):
        product_data['name'] = f'测试商品_{i+1:03d}'
        product = TestProduct(**product_data)
        products.append(product)
    
    logger.info(f"创建 {len(products)} 个测试商品")
    return products


@pytest.fixture(scope="function")
def test_data_from_file(request):
    """从文件加载测试数据"""
    def load_data(file_path: str, data_type: str = 'json') -> Any:
        """
        从文件加载数据
        
        Args:
            file_path: 文件路径（相对于 data/ 目录）
            data_type: 数据类型 ('json', 'yaml')
            
        Returns:
            加载的数据
        """
        data_dir = Path(__file__).parent.parent / "data" / "static"
        full_path = data_dir / file_path
        
        if not full_path.exists():
            raise FileNotFoundError(f"测试数据文件不存在: {full_path}")
        
        try:
            with open(full_path, 'r', encoding='utf-8') as f:
                if data_type.lower() == 'json':
                    data = json.load(f)
                elif data_type.lower() in ['yaml', 'yml']:
                    data = yaml.safe_load(f)
                else:
                    raise ValueError(f"不支持的数据类型: {data_type}")
            
            logger.info(f"从文件加载测试数据: {file_path}")
            return data
            
        except Exception as e:
            logger.error(f"加载测试数据文件失败: {file_path}, 错误: {e}")
            raise
    
    return load_data


@pytest.fixture(scope="function")
def data_cleanup_manager():
    """数据清理管理器"""
    cleanup_functions = []
    
    def register_cleanup(cleanup_func, *args, **kwargs):
        """
        注册清理函数
        
        Args:
            cleanup_func: 清理函数
            *args: 清理函数的位置参数
            **kwargs: 清理函数的关键字参数
        """
        cleanup_functions.append((cleanup_func, args, kwargs))
        logger.debug(f"注册清理函数: {cleanup_func.__name__}")
    
    def cleanup_all():
        """执行所有清理函数"""
        for cleanup_func, args, kwargs in cleanup_functions:
            try:
                cleanup_func(*args, **kwargs)
                logger.info(f"执行清理函数成功: {cleanup_func.__name__}")
            except Exception as e:
                logger.error(f"执行清理函数失败: {cleanup_func.__name__}, 错误: {e}")
    
    # 返回注册函数
    yield register_cleanup
    
    # 测试结束后执行清理
    cleanup_all()


@pytest.fixture(scope="function")
def test_credentials(env_config) -> Dict[str, Dict[str, str]]:
    """测试凭证信息"""
    credentials = {
        'standard_user': {
            'username': env_config.get('credentials', {}).get('username', 'test_user'),
            'password': env_config.get('credentials', {}).get('password', 'test_password')
        },
        'admin_user': {
            'username': env_config.get('admin_credentials', {}).get('username', 'admin'),
            'password': env_config.get('admin_credentials', {}).get('password', 'admin_password')
        }
    }
    
    logger.info("加载测试凭证信息")
    return credentials


@pytest.fixture(scope="function")
def test_urls(env_config) -> Dict[str, str]:
    """测试URL信息"""
    base_url = env_config.get('url', 'https://example.com')
    
    urls = {
        'base': base_url,
        'login': f"{base_url}/login",
        'dashboard': f"{base_url}/dashboard",
        'profile': f"{base_url}/profile",
        'products': f"{base_url}/products",
        'orders': f"{base_url}/orders",
        'admin': f"{base_url}/admin"
    }
    
    logger.info(f"加载测试URL信息，基础URL: {base_url}")
    return urls


# 参数化数据 fixtures
@pytest.fixture(params=[
    {'role': 'user', 'active': True},
    {'role': 'admin', 'active': True},
    {'role': 'premium', 'active': True}
])
def parameterized_user(request, data_factory) -> TestUser:
    """参数化用户数据"""
    params = request.param
    user_data = data_factory.generate_user_data(params)
    user = TestUser(**user_data)
    logger.info(f"创建参数化用户: {user}")
    return user


@pytest.fixture(params=[
    {'category': '电子产品', 'price_range': (100, 1000)},
    {'category': '服装', 'price_range': (50, 500)},
    {'category': '图书', 'price_range': (10, 100)}
])
def parameterized_product(request, data_factory) -> TestProduct:
    """参数化商品数据"""
    params = request.param
    price_min, price_max = params.pop('price_range', (10, 1000))
    
    product_data = data_factory.generate_product_data(params)
    product_data['price'] = data_factory.fake.pyfloat(min_value=price_min, max_value=price_max, right_digits=2)
    
    product = TestProduct(**product_data)
    logger.info(f"创建参数化商品: {product}")
    return product
