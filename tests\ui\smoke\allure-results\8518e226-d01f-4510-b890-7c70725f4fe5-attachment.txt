fixtures.scf_fixtures - INFO - 准备SCF登录凭证
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.12.11-windows-x86_64-none\Lib\logging\handlers.py", line 74, in emit
    self.doRollover()
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.12.11-windows-x86_64-none\Lib\logging\handlers.py", line 446, in doRollover
    self.rotate(self.baseFilename, dfn)
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.12.11-windows-x86_64-none\Lib\logging\handlers.py", line 115, in rotate
    os.rename(source, dest)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'G:\\nifa\\playwright-python-template\\logs\\all.log' -> 'G:\\nifa\\playwright-python-template\\logs\\all.log.2025-07-04'
Call stack:
  File "D:\PyCharm 2025.1.2\plugins\python-ce\helpers\pycharm\_jb_pytest_runner.py", line 75, in <module>
    sys.exit(pytest.main(args, plugins_to_load + [Plugin]))
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\config\__init__.py", line 175, in main
    ret: ExitCode | int = config.hook.pytest_cmdline_main(config=config)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_hooks.py", line 512, in __call__
    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_manager.py", line 120, in _hookexec
    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_callers.py", line 121, in _multicall
    res = hook_impl.function(*args)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\main.py", line 336, in pytest_cmdline_main
    return wrap_session(config, _main)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\main.py", line 289, in wrap_session
    session.exitstatus = doit(config, session) or 0
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\main.py", line 343, in _main
    config.hook.pytest_runtestloop(session=session)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_hooks.py", line 512, in __call__
    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_manager.py", line 120, in _hookexec
    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_callers.py", line 121, in _multicall
    res = hook_impl.function(*args)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\main.py", line 367, in pytest_runtestloop
    item.config.hook.pytest_runtest_protocol(item=item, nextitem=nextitem)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_hooks.py", line 512, in __call__
    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_manager.py", line 120, in _hookexec
    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_callers.py", line 121, in _multicall
    res = hook_impl.function(*args)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\runner.py", line 117, in pytest_runtest_protocol
    runtestprotocol(item, nextitem=nextitem)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\runner.py", line 130, in runtestprotocol
    rep = call_and_report(item, "setup", log)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\runner.py", line 245, in call_and_report
    call = CallInfo.from_call(
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\runner.py", line 344, in from_call
    result: TResult | None = func()
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\runner.py", line 246, in <lambda>
    lambda: runtest_hook(item=item, **kwds), when=when, reraise=reraise
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_hooks.py", line 512, in __call__
    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_manager.py", line 120, in _hookexec
    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_callers.py", line 121, in _multicall
    res = hook_impl.function(*args)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\runner.py", line 164, in pytest_runtest_setup
    item.session._setupstate.setup(item)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\runner.py", line 514, in setup
    col.setup()
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\python.py", line 1674, in setup
    self._request._fillfixtures()
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\fixtures.py", line 719, in _fillfixtures
    item.funcargs[argname] = self.getfixturevalue(argname)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\fixtures.py", line 548, in getfixturevalue
    fixturedef = self._get_active_fixturedef(argname)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\fixtures.py", line 639, in _get_active_fixturedef
    fixturedef.execute(request=subrequest)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\fixtures.py", line 1127, in execute
    result = ihook.pytest_fixture_setup(fixturedef=self, request=request)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_hooks.py", line 512, in __call__
    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_manager.py", line 120, in _hookexec
    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_callers.py", line 121, in _multicall
    res = hook_impl.function(*args)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\fixtures.py", line 1195, in pytest_fixture_setup
    result = call_fixture_func(fixturefunc, request, kwargs)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\fixtures.py", line 929, in call_fixture_func
    fixture_result = fixturefunc(**kwargs)
  File "G:\nifa\playwright-python-template\fixtures\scf_fixtures.py", line 79, in scf_login_credentials
    logger.info("准备SCF登录凭证")
Message: '准备SCF登录凭证'
Arguments: ()
components.ocr_util - INFO - OCR引擎初始化成功
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.12.11-windows-x86_64-none\Lib\logging\handlers.py", line 74, in emit
    self.doRollover()
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.12.11-windows-x86_64-none\Lib\logging\handlers.py", line 446, in doRollover
    self.rotate(self.baseFilename, dfn)
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.12.11-windows-x86_64-none\Lib\logging\handlers.py", line 115, in rotate
    os.rename(source, dest)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'G:\\nifa\\playwright-python-template\\logs\\all.log' -> 'G:\\nifa\\playwright-python-template\\logs\\all.log.2025-07-04'
Call stack:
  File "D:\PyCharm 2025.1.2\plugins\python-ce\helpers\pycharm\_jb_pytest_runner.py", line 75, in <module>
    sys.exit(pytest.main(args, plugins_to_load + [Plugin]))
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\config\__init__.py", line 175, in main
    ret: ExitCode | int = config.hook.pytest_cmdline_main(config=config)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_hooks.py", line 512, in __call__
    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_manager.py", line 120, in _hookexec
    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_callers.py", line 121, in _multicall
    res = hook_impl.function(*args)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\main.py", line 336, in pytest_cmdline_main
    return wrap_session(config, _main)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\main.py", line 289, in wrap_session
    session.exitstatus = doit(config, session) or 0
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\main.py", line 343, in _main
    config.hook.pytest_runtestloop(session=session)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_hooks.py", line 512, in __call__
    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_manager.py", line 120, in _hookexec
    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_callers.py", line 121, in _multicall
    res = hook_impl.function(*args)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\main.py", line 367, in pytest_runtestloop
    item.config.hook.pytest_runtest_protocol(item=item, nextitem=nextitem)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_hooks.py", line 512, in __call__
    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_manager.py", line 120, in _hookexec
    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_callers.py", line 121, in _multicall
    res = hook_impl.function(*args)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\runner.py", line 117, in pytest_runtest_protocol
    runtestprotocol(item, nextitem=nextitem)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\runner.py", line 136, in runtestprotocol
    reports.append(call_and_report(item, "call", log))
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\runner.py", line 245, in call_and_report
    call = CallInfo.from_call(
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\runner.py", line 344, in from_call
    result: TResult | None = func()
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\runner.py", line 246, in <lambda>
    lambda: runtest_hook(item=item, **kwds), when=when, reraise=reraise
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_hooks.py", line 512, in __call__
    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_manager.py", line 120, in _hookexec
    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_callers.py", line 121, in _multicall
    res = hook_impl.function(*args)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\runner.py", line 178, in pytest_runtest_call
    item.runtest()
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\python.py", line 1671, in runtest
    self.ihook.pytest_pyfunc_call(pyfuncitem=self)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_hooks.py", line 512, in __call__
    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_manager.py", line 120, in _hookexec
    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_callers.py", line 121, in _multicall
    res = hook_impl.function(*args)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\python.py", line 157, in pytest_pyfunc_call
    result = testfunction(**testargs)
  File "G:\nifa\playwright-python-template\tests\ui\smoke\test_scf_login.py", line 83, in test_login_failure_scenarios
    workflow = SCFRegistrationWorkflow(page)
  File "G:\nifa\playwright-python-template\workflows\scf_registration_workflow.py", line 28, in __init__
    self.login_page = SCFLoginPage(page)
  File "G:\nifa\playwright-python-template\pages\scf_login_page.py", line 27, in __init__
    self.ocr_handler = get_ocr_handler()
  File "G:\nifa\playwright-python-template\components\ocr_util.py", line 328, in get_ocr_handler
    return OCRHandler(**kwargs)
  File "G:\nifa\playwright-python-template\components\ocr_util.py", line 60, in __init__
    logger.info("OCR引擎初始化成功")
Message: 'OCR引擎初始化成功'
Arguments: ()
SCFRegistrationWorkflow - INFO - 开始登录系统
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.12.11-windows-x86_64-none\Lib\logging\handlers.py", line 74, in emit
    self.doRollover()
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.12.11-windows-x86_64-none\Lib\logging\handlers.py", line 446, in doRollover
    self.rotate(self.baseFilename, dfn)
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.12.11-windows-x86_64-none\Lib\logging\handlers.py", line 115, in rotate
    os.rename(source, dest)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'G:\\nifa\\playwright-python-template\\logs\\all.log' -> 'G:\\nifa\\playwright-python-template\\logs\\all.log.2025-07-04'
Call stack:
  File "D:\PyCharm 2025.1.2\plugins\python-ce\helpers\pycharm\_jb_pytest_runner.py", line 75, in <module>
    sys.exit(pytest.main(args, plugins_to_load + [Plugin]))
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\config\__init__.py", line 175, in main
    ret: ExitCode | int = config.hook.pytest_cmdline_main(config=config)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_hooks.py", line 512, in __call__
    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_manager.py", line 120, in _hookexec
    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_callers.py", line 121, in _multicall
    res = hook_impl.function(*args)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\main.py", line 336, in pytest_cmdline_main
    return wrap_session(config, _main)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\main.py", line 289, in wrap_session
    session.exitstatus = doit(config, session) or 0
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\main.py", line 343, in _main
    config.hook.pytest_runtestloop(session=session)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_hooks.py", line 512, in __call__
    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_manager.py", line 120, in _hookexec
    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_callers.py", line 121, in _multicall
    res = hook_impl.function(*args)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\main.py", line 367, in pytest_runtestloop
    item.config.hook.pytest_runtest_protocol(item=item, nextitem=nextitem)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_hooks.py", line 512, in __call__
    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_manager.py", line 120, in _hookexec
    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_callers.py", line 121, in _multicall
    res = hook_impl.function(*args)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\runner.py", line 117, in pytest_runtest_protocol
    runtestprotocol(item, nextitem=nextitem)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\runner.py", line 136, in runtestprotocol
    reports.append(call_and_report(item, "call", log))
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\runner.py", line 245, in call_and_report
    call = CallInfo.from_call(
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\runner.py", line 344, in from_call
    result: TResult | None = func()
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\runner.py", line 246, in <lambda>
    lambda: runtest_hook(item=item, **kwds), when=when, reraise=reraise
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_hooks.py", line 512, in __call__
    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_manager.py", line 120, in _hookexec
    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_callers.py", line 121, in _multicall
    res = hook_impl.function(*args)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\runner.py", line 178, in pytest_runtest_call
    item.runtest()
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\python.py", line 1671, in runtest
    self.ihook.pytest_pyfunc_call(pyfuncitem=self)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_hooks.py", line 512, in __call__
    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_manager.py", line 120, in _hookexec
    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_callers.py", line 121, in _multicall
    res = hook_impl.function(*args)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\python.py", line 157, in pytest_pyfunc_call
    result = testfunction(**testargs)
  File "G:\nifa\playwright-python-template\tests\ui\smoke\test_scf_login.py", line 89, in test_login_failure_scenarios
    workflow._login_to_system(credentials, scf_base_url)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\allure_commons\_allure.py", line 202, in impl
    return func(*a, **kw)
  File "G:\nifa\playwright-python-template\workflows\scf_registration_workflow.py", line 110, in _login_to_system
    self.logger.info("开始登录系统")
Message: '开始登录系统'
Arguments: ()
SCFLoginPage - INFO - 导航到登录页面: http://172.18.12.128:8080/login
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.12.11-windows-x86_64-none\Lib\logging\handlers.py", line 74, in emit
    self.doRollover()
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.12.11-windows-x86_64-none\Lib\logging\handlers.py", line 446, in doRollover
    self.rotate(self.baseFilename, dfn)
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.12.11-windows-x86_64-none\Lib\logging\handlers.py", line 115, in rotate
    os.rename(source, dest)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'G:\\nifa\\playwright-python-template\\logs\\all.log' -> 'G:\\nifa\\playwright-python-template\\logs\\all.log.2025-07-04'
Call stack:
  File "D:\PyCharm 2025.1.2\plugins\python-ce\helpers\pycharm\_jb_pytest_runner.py", line 75, in <module>
    sys.exit(pytest.main(args, plugins_to_load + [Plugin]))
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\config\__init__.py", line 175, in main
    ret: ExitCode | int = config.hook.pytest_cmdline_main(config=config)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_hooks.py", line 512, in __call__
    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_manager.py", line 120, in _hookexec
    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_callers.py", line 121, in _multicall
    res = hook_impl.function(*args)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\main.py", line 336, in pytest_cmdline_main
    return wrap_session(config, _main)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\main.py", line 289, in wrap_session
    session.exitstatus = doit(config, session) or 0
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\main.py", line 343, in _main
    config.hook.pytest_runtestloop(session=session)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_hooks.py", line 512, in __call__
    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_manager.py", line 120, in _hookexec
    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_callers.py", line 121, in _multicall
    res = hook_impl.function(*args)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\main.py", line 367, in pytest_runtestloop
    item.config.hook.pytest_runtest_protocol(item=item, nextitem=nextitem)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_hooks.py", line 512, in __call__
    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_manager.py", line 120, in _hookexec
    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_callers.py", line 121, in _multicall
    res = hook_impl.function(*args)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\runner.py", line 117, in pytest_runtest_protocol
    runtestprotocol(item, nextitem=nextitem)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\runner.py", line 136, in runtestprotocol
    reports.append(call_and_report(item, "call", log))
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\runner.py", line 245, in call_and_report
    call = CallInfo.from_call(
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\runner.py", line 344, in from_call
    result: TResult | None = func()
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\runner.py", line 246, in <lambda>
    lambda: runtest_hook(item=item, **kwds), when=when, reraise=reraise
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_hooks.py", line 512, in __call__
    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_manager.py", line 120, in _hookexec
    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_callers.py", line 121, in _multicall
    res = hook_impl.function(*args)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\runner.py", line 178, in pytest_runtest_call
    item.runtest()
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\python.py", line 1671, in runtest
    self.ihook.pytest_pyfunc_call(pyfuncitem=self)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_hooks.py", line 512, in __call__
    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_manager.py", line 120, in _hookexec
    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_callers.py", line 121, in _multicall
    res = hook_impl.function(*args)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\python.py", line 157, in pytest_pyfunc_call
    result = testfunction(**testargs)
  File "G:\nifa\playwright-python-template\tests\ui\smoke\test_scf_login.py", line 89, in test_login_failure_scenarios
    workflow._login_to_system(credentials, scf_base_url)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\allure_commons\_allure.py", line 202, in impl
    return func(*a, **kw)
  File "G:\nifa\playwright-python-template\workflows\scf_registration_workflow.py", line 113, in _login_to_system
    self.login_page.navigate_to_login(base_url)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\allure_commons\_allure.py", line 202, in impl
    return func(*a, **kw)
  File "G:\nifa\playwright-python-template\pages\scf_login_page.py", line 50, in navigate_to_login
    self.logger.info(f"导航到登录页面: {full_url}")
Message: '导航到登录页面: http://172.18.12.128:8080/login'
Arguments: ()
pages.base_page - INFO - 导航到页面: http://172.18.12.128:8080/login
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.12.11-windows-x86_64-none\Lib\logging\handlers.py", line 74, in emit
    self.doRollover()
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.12.11-windows-x86_64-none\Lib\logging\handlers.py", line 446, in doRollover
    self.rotate(self.baseFilename, dfn)
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.12.11-windows-x86_64-none\Lib\logging\handlers.py", line 115, in rotate
    os.rename(source, dest)
PermissionError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'G:\\nifa\\playwright-python-template\\logs\\all.log' -> 'G:\\nifa\\playwright-python-template\\logs\\all.log.2025-07-04'
Call stack:
  File "D:\PyCharm 2025.1.2\plugins\python-ce\helpers\pycharm\_jb_pytest_runner.py", line 75, in <module>
    sys.exit(pytest.main(args, plugins_to_load + [Plugin]))
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\config\__init__.py", line 175, in main
    ret: ExitCode | int = config.hook.pytest_cmdline_main(config=config)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_hooks.py", line 512, in __call__
    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_manager.py", line 120, in _hookexec
    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_callers.py", line 121, in _multicall
    res = hook_impl.function(*args)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\main.py", line 336, in pytest_cmdline_main
    return wrap_session(config, _main)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\main.py", line 289, in wrap_session
    session.exitstatus = doit(config, session) or 0
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\main.py", line 343, in _main
    config.hook.pytest_runtestloop(session=session)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_hooks.py", line 512, in __call__
    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_manager.py", line 120, in _hookexec
    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_callers.py", line 121, in _multicall
    res = hook_impl.function(*args)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\main.py", line 367, in pytest_runtestloop
    item.config.hook.pytest_runtest_protocol(item=item, nextitem=nextitem)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_hooks.py", line 512, in __call__
    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_manager.py", line 120, in _hookexec
    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_callers.py", line 121, in _multicall
    res = hook_impl.function(*args)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\runner.py", line 117, in pytest_runtest_protocol
    runtestprotocol(item, nextitem=nextitem)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\runner.py", line 136, in runtestprotocol
    reports.append(call_and_report(item, "call", log))
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\runner.py", line 245, in call_and_report
    call = CallInfo.from_call(
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\runner.py", line 344, in from_call
    result: TResult | None = func()
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\runner.py", line 246, in <lambda>
    lambda: runtest_hook(item=item, **kwds), when=when, reraise=reraise
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_hooks.py", line 512, in __call__
    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_manager.py", line 120, in _hookexec
    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_callers.py", line 121, in _multicall
    res = hook_impl.function(*args)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\runner.py", line 178, in pytest_runtest_call
    item.runtest()
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\python.py", line 1671, in runtest
    self.ihook.pytest_pyfunc_call(pyfuncitem=self)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_hooks.py", line 512, in __call__
    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_manager.py", line 120, in _hookexec
    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\pluggy\_callers.py", line 121, in _multicall
    res = hook_impl.function(*args)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\_pytest\python.py", line 157, in pytest_pyfunc_call
    result = testfunction(**testargs)
  File "G:\nifa\playwright-python-template\tests\ui\smoke\test_scf_login.py", line 89, in test_login_failure_scenarios
    workflow._login_to_system(credentials, scf_base_url)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\allure_commons\_allure.py", line 202, in impl
    return func(*a, **kw)
  File "G:\nifa\playwright-python-template\workflows\scf_registration_workflow.py", line 113, in _login_to_system
    self.login_page.navigate_to_login(base_url)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\allure_commons\_allure.py", line 202, in impl
    return func(*a, **kw)
  File "G:\nifa\playwright-python-template\pages\scf_login_page.py", line 51, in navigate_to_login
    self.navigate(full_url)
  File "G:\nifa\playwright-python-template\.venv\Lib\site-packages\allure_commons\_allure.py", line 202, in impl
    return func(*a, **kw)
  File "G:\nifa\playwright-python-template\pages\base_page.py", line 30, in navigate
    logger.info(f"导航到页面: {url}")
Message: '导航到页面: http://172.18.12.128:8080/login'
Arguments: ()
fixtures.conftest - ERROR - 测试失败，已保存调试信息: test_login_failure_scenarios[chromium-empty_credentials-\u5e94\u8be5\u63d0\u793a\u8f93\u5165\u7528\u6237\u540d\u548c\u5bc6\u7801]
