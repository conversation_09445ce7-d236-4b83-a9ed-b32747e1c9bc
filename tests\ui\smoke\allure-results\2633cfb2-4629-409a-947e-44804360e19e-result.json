{"name": "test_standard_user_login_success[chromium]", "status": "broken", "statusDetails": {"message": "AttributeError: 'SCFLoginPage' object has no attribute 'take_screenshot_for_report'", "trace": "..\\..\\..\\pages\\scf_login_page.py:110: in auto_input_captcha\n    self.wait_for_element(self.captcha_image)\n..\\..\\..\\pages\\base_page.py:192: in wait_for_element\n    element.wait_for(state=state, timeout=timeout or self.default_timeout)\n..\\..\\..\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py:17937: in wait_for\n    self._sync(self._impl_obj.wait_for(timeout=timeout, state=state))\n..\\..\\..\\.venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py:693: in wait_for\n    await self._frame.wait_for_selector(\n..\\..\\..\\.venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py:341: in wait_for_selector\n    await self._channel.send(\n..\\..\\..\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:69: in send\n    return await self._connection.wrap_api_call(\n..\\..\\..\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:558: in wrap_api_call\n    raise rewrite_error(error, f\"{parsed_st['apiName']}: {error}\") from None\nE   playwright._impl._errors.TimeoutError: Locator.wait_for: Timeout 30000ms exceeded.\nE   Call log:\nE     - waiting for locator(\".captcha-image\") to be visible\n\nDuring handling of the above exception, another exception occurred:\ntest_scf_login.py:37: in test_standard_user_login_success\n    workflow._login_to_system(credentials, scf_base_url)\n..\\..\\..\\workflows\\scf_registration_workflow.py:123: in _login_to_system\n    self.login_page.login(username, password, auto_captcha=True)\n..\\..\\..\\pages\\scf_login_page.py:170: in login\n    self.auto_input_captcha()\n..\\..\\..\\pages\\scf_login_page.py:133: in auto_input_captcha\n    self.take_screenshot_for_report(\"验证码识别失败\")\n    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nE   AttributeError: 'SCFLoginPage' object has no attribute 'take_screenshot_for_report'"}, "description": "测试标准用户成功登录", "steps": [{"name": "执行标准用户登录", "status": "broken", "statusDetails": {"message": "AttributeError: 'SCFLoginPage' object has no attribute 'take_screenshot_for_report'\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\tests\\ui\\smoke\\test_scf_login.py\", line 37, in test_standard_user_login_success\n    workflow._login_to_system(credentials, scf_base_url)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\workflows\\scf_registration_workflow.py\", line 123, in _login_to_system\n    self.login_page.login(username, password, auto_captcha=True)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 170, in login\n    self.auto_input_captcha()\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 133, in auto_input_captcha\n    self.take_screenshot_for_report(\"验证码识别失败\")\n    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "steps": [{"name": "登录系统", "status": "broken", "statusDetails": {"message": "AttributeError: 'SCFLoginPage' object has no attribute 'take_screenshot_for_report'\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\workflows\\scf_registration_workflow.py\", line 123, in _login_to_system\n    self.login_page.login(username, password, auto_captcha=True)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 170, in login\n    self.auto_input_captcha()\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 133, in auto_input_captcha\n    self.take_screenshot_for_report(\"验证码识别失败\")\n    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "steps": [{"name": "导航到登录页面", "status": "passed", "steps": [{"name": "导航到页面: 'http://*************:8080/login'", "status": "passed", "parameters": [{"name": "url", "value": "'http://*************:8080/login'"}, {"name": "wait_until", "value": "'domcontentloaded'"}], "start": 1751852912453, "stop": 1751852913687}], "parameters": [{"name": "base_url", "value": "'http://*************:8080'"}], "start": 1751852912453, "stop": 1751852913688}, {"name": "执行登录操作", "status": "broken", "statusDetails": {"message": "AttributeError: 'SCFLoginPage' object has no attribute 'take_screenshot_for_report'\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 170, in login\n    self.auto_input_captcha()\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 133, in auto_input_captcha\n    self.take_screenshot_for_report(\"验证码识别失败\")\n    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "steps": [{"name": "点击账号登录标签", "status": "passed", "steps": [{"name": "智能点击: 'text=账号登录'", "status": "passed", "steps": [{"name": "等待元素稳定: 'text=账号登录'", "status": "passed", "parameters": [{"name": "locator", "value": "'text=账号登录'"}, {"name": "timeout", "value": "None"}], "start": 1751852913690, "stop": 1751852914262}], "parameters": [{"name": "locator", "value": "'text=账号登录'"}, {"name": "force", "value": "False"}, {"name": "timeout", "value": "None"}], "start": 1751852913689, "stop": 1751852914353}], "start": 1751852913689, "stop": 1751852914353}, {"name": "输入用户名: 'scf_4nuioc'", "status": "passed", "steps": [{"name": "智能填充: 'role=textbox[name='请输入账号']' = 'scf_4nuioc'", "status": "passed", "steps": [{"name": "等待元素稳定: 'role=textbox[name='请输入账号']'", "status": "passed", "parameters": [{"name": "locator", "value": "'role=textbox[name='请输入账号']'"}, {"name": "timeout", "value": "None"}], "start": 1751852914354, "stop": 1751852914701}], "parameters": [{"name": "locator", "value": "'role=textbox[name='请输入账号']'"}, {"name": "text", "value": "'scf_4nuioc'"}, {"name": "clear_first", "value": "True"}, {"name": "timeout", "value": "None"}], "start": 1751852914353, "stop": 1751852914749}], "parameters": [{"name": "username", "value": "'scf_4nuioc'"}], "start": 1751852914353, "stop": 1751852914749}, {"name": "输入密码", "status": "passed", "steps": [{"name": "智能填充: 'role=textbox[name='请输入密码']' = 'Scf123456.'", "status": "passed", "steps": [{"name": "等待元素稳定: 'role=textbox[name='请输入密码']'", "status": "passed", "parameters": [{"name": "locator", "value": "'role=textbox[name='请输入密码']'"}, {"name": "timeout", "value": "None"}], "start": 1751852914749, "stop": 1751852915086}], "parameters": [{"name": "locator", "value": "'role=textbox[name='请输入密码']'"}, {"name": "text", "value": "'Scf123456.'"}, {"name": "clear_first", "value": "True"}, {"name": "timeout", "value": "None"}], "start": 1751852914749, "stop": 1751852915138}], "parameters": [{"name": "password", "value": "'Scf123456.'"}], "start": 1751852914749, "stop": 1751852915138}, {"name": "自动识别并输入验证码", "status": "broken", "statusDetails": {"message": "AttributeError: 'SCFLoginPage' object has no attribute 'take_screenshot_for_report'\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 133, in auto_input_captcha\n    self.take_screenshot_for_report(\"验证码识别失败\")\n    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "steps": [{"name": "等待元素出现: '.captcha-image'", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.TimeoutError: Locator.wait_for: Timeout 30000ms exceeded.\nCall log:\n  - waiting for locator(\".captcha-image\") to be visible\n\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 192, in wait_for_element\n    element.wait_for(state=state, timeout=timeout or self.default_timeout)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 17937, in wait_for\n    self._sync(self._impl_obj.wait_for(timeout=timeout, state=state))\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 115, in _sync\n    return task.result()\n           ^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py\", line 693, in wait_for\n    await self._frame.wait_for_selector(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py\", line 341, in wait_for_selector\n    await self._channel.send(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 69, in send\n    return await self._connection.wrap_api_call(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 558, in wrap_api_call\n    raise rewrite_error(error, f\"{parsed_st['apiName']}: {error}\") from None\n"}, "parameters": [{"name": "locator", "value": "'.captcha-image'"}, {"name": "state", "value": "'visible'"}, {"name": "timeout", "value": "None"}], "start": 1751852915139, "stop": 1751852945147}], "start": 1751852915138, "stop": 1751852945148}], "parameters": [{"name": "username", "value": "'scf_4nuioc'"}, {"name": "password", "value": "'Scf123456.'"}, {"name": "<PERSON><PERSON>a", "value": "None"}, {"name": "auto_captcha", "value": "True"}], "start": 1751852913688, "stop": 1751852945150}], "parameters": [{"name": "credentials", "value": "{'username': 'scf_4nuioc', 'password': 'Scf123456.', 'description': '标准测试用户'}"}, {"name": "base_url", "value": "'http://*************:8080'"}], "start": 1751852912453, "stop": 1751852945152}], "start": 1751852912453, "stop": 1751852945154}], "attachments": [{"name": "失败截图", "source": "4dcd2b7b-deaf-46aa-9136-37563d4c7f19-attachment.png", "type": "image/png"}, {"name": "页面HTML", "source": "be459fd6-a512-4b21-825e-2b709ab57fa1-attachment.html", "type": "text/html"}, {"name": "当前URL", "source": "0812bf31-9270-408c-884e-ea53c62e6e49-attachment.txt", "type": "text/plain"}, {"name": "log", "source": "3532bc3e-742d-4df3-bc89-3f5ecb559be2-attachment.txt", "type": "text/plain"}, {"name": "stdout", "source": "90a32b0f-38fc-48ae-99f7-39906ae6d412-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "browser_name", "value": "'chromium'"}], "start": 1751852912413, "stop": 1751852945156, "uuid": "9cbad0c7-1aed-41d6-baf2-79d53e1ca5dd", "historyId": "83c32d878021effc5e3dcc4d134c4bd1", "testCaseId": "2cf6f352a9af813bd18233473cb242da", "fullName": "tests.ui.smoke.test_scf_login.TestSCFLogin#test_standard_user_login_success", "labels": [{"name": "severity", "value": "blocker"}, {"name": "story", "value": "标准用户登录"}, {"name": "feature", "value": "用户登录"}, {"name": "epic", "value": "供应链金融备案系统"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "tests.ui.smoke"}, {"name": "suite", "value": "test_scf_login"}, {"name": "subSuite", "value": "TestSC<PERSON><PERSON>in"}, {"name": "host", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "thread", "value": "7348-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.ui.smoke.test_scf_login"}]}