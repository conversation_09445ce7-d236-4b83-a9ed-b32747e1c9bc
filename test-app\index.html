<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试应用 - 首页</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f4f4f4;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        header {
            background: #2c3e50;
            color: white;
            padding: 1rem 0;
            margin-bottom: 2rem;
        }
        
        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 1.5rem;
            font-weight: bold;
        }
        
        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
        }
        
        .nav-links a {
            color: white;
            text-decoration: none;
            transition: color 0.3s;
        }
        
        .nav-links a:hover {
            color: #3498db;
        }
        
        .login-section {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 400px;
            margin: 2rem auto;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
        }
        
        input[type="text"],
        input[type="password"] {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 1rem;
        }
        
        input[type="text"]:focus,
        input[type="password"]:focus {
            outline: none;
            border-color: #3498db;
        }
        
        .btn {
            background: #3498db;
            color: white;
            padding: 0.75rem 2rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
            width: 100%;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #2980b9;
        }
        
        .error-message {
            color: #e74c3c;
            margin-top: 1rem;
            padding: 0.5rem;
            background: #fdf2f2;
            border: 1px solid #e74c3c;
            border-radius: 4px;
            display: none;
        }
        
        .success-message {
            color: #27ae60;
            margin-top: 1rem;
            padding: 0.5rem;
            background: #f2fdf2;
            border: 1px solid #27ae60;
            border-radius: 4px;
            display: none;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }
        
        .feature-card {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .feature-card h3 {
            color: #2c3e50;
            margin-bottom: 1rem;
        }
        
        @media (max-width: 768px) {
            .nav-links {
                flex-direction: column;
                gap: 1rem;
            }
            
            .container {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <nav>
                <div class="logo">测试应用</div>
                <ul class="nav-links">
                    <li><a href="#home">首页</a></li>
                    <li><a href="#products">商品</a></li>
                    <li><a href="#about">关于</a></li>
                    <li><a href="#contact">联系</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <div class="container">
        <div class="login-section">
            <h2>用户登录</h2>
            <form id="login-form">
                <div class="form-group">
                    <label for="username">用户名:</label>
                    <input type="text" id="username" name="username" required>
                </div>
                
                <div class="form-group">
                    <label for="password">密码:</label>
                    <input type="password" id="password" name="password" required>
                </div>
                
                <button type="submit" id="login-button" class="btn">登录</button>
                
                <div id="error-message" class="error-message"></div>
                <div id="success-message" class="success-message"></div>
            </form>
        </div>

        <div class="features">
            <div class="feature-card">
                <h3>自动化测试</h3>
                <p>支持完整的 Web 自动化测试流程，包括功能测试、回归测试和性能测试。</p>
            </div>
            
            <div class="feature-card">
                <h3>跨浏览器支持</h3>
                <p>支持 Chrome、Firefox、Safari 等主流浏览器的自动化测试。</p>
            </div>
            
            <div class="feature-card">
                <h3>详细报告</h3>
                <p>生成详细的测试报告，包含截图、日志和执行轨迹。</p>
            </div>
        </div>
    </div>

    <script>
        // 模拟登录功能
        document.getElementById('login-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const errorMessage = document.getElementById('error-message');
            const successMessage = document.getElementById('success-message');
            
            // 隐藏之前的消息
            errorMessage.style.display = 'none';
            successMessage.style.display = 'none';
            
            // 模拟登录验证
            if (username === 'test_user' && password === 'test_password') {
                successMessage.textContent = '登录成功！';
                successMessage.style.display = 'block';
                
                // 模拟跳转到仪表板
                setTimeout(() => {
                    window.location.href = '/dashboard.html';
                }, 1000);
            } else if (username === 'admin' && password === 'admin_password') {
                successMessage.textContent = '管理员登录成功！';
                successMessage.style.display = 'block';
                
                setTimeout(() => {
                    window.location.href = '/admin.html';
                }, 1000);
            } else if (!username || !password) {
                errorMessage.textContent = '请输入用户名和密码';
                errorMessage.style.display = 'block';
            } else if (!username) {
                errorMessage.textContent = '请输入用户名';
                errorMessage.style.display = 'block';
            } else if (!password) {
                errorMessage.textContent = '请输入密码';
                errorMessage.style.display = 'block';
            } else {
                errorMessage.textContent = '用户名或密码错误';
                errorMessage.style.display = 'block';
            }
        });
        
        // 添加一些测试用的元素ID
        document.addEventListener('DOMContentLoaded', function() {
            // 为测试添加一些隐藏的元素
            const testElements = document.createElement('div');
            testElements.style.display = 'none';
            testElements.innerHTML = `
                <div id="user-menu" style="display: none;">用户菜单</div>
                <div id="cart-count">0</div>
                <div id="welcome-message"></div>
            `;
            document.body.appendChild(testElements);
        });
    </script>
</body>
</html>
