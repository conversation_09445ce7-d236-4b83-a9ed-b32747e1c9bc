import pytest
import yaml
from pathlib import Path

# 导入fixtures
pytest_plugins = [
    "fixtures.conftest",
    "fixtures.data_fixtures",
    "fixtures.scf_fixtures",
]

def pytest_addoption(parser):
    parser.addoption("--env", action="store", default="test", help="environment to run tests against")

@pytest.fixture(scope="session")
def env_config(request):
    env = request.config.getoption("env")
    config_path = Path(__file__).parent.parent / "config"
    with open(config_path / "base.yaml") as f:
        config = yaml.safe_load(f)
    with open(config_path / f"{env}.yaml") as f:
        env_specific_config = yaml.safe_load(f)
    config.update(env_specific_config)
    return config
