"""
供应链金融备案系统专用 Fixtures
Supply Chain Finance (SCF) System Fixtures
"""

import pytest
import yaml
import json
from pathlib import Path
from typing import Dict, Any
from components.log_manager import get_logger

logger = get_logger(__name__)


@pytest.fixture(scope="session")
def scf_config():
    """
    供应链金融系统配置 fixture
    
    Returns:
        Dict: SCF系统配置
    """
    config_path = Path(__file__).parent.parent / "config" / "scf.yaml"
    
    if not config_path.exists():
        raise FileNotFoundError(f"SCF配置文件不存在: {config_path}")
    
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    logger.info("加载SCF系统配置成功")
    return config


@pytest.fixture(scope="session")
def scf_test_data():
    """
    供应链金融测试数据 fixture
    
    Returns:
        Dict: 测试数据
    """
    data_path = Path(__file__).parent.parent / "data" / "static" / "scf_application_data.json"
    
    if not data_path.exists():
        raise FileNotFoundError(f"SCF测试数据文件不存在: {data_path}")
    
    with open(data_path, 'r', encoding='utf-8') as f:
        test_data = json.load(f)
    
    logger.info("加载SCF测试数据成功")
    return test_data


@pytest.fixture(scope="function")
def scf_login_credentials(scf_config, scf_test_data):
    """
    SCF登录凭证 fixture
    
    Returns:
        Dict: 登录凭证信息
    """
    # 优先使用配置文件中的凭证
    config_credentials = scf_config.get("credentials", {})
    
    # 如果配置文件中没有，使用测试数据中的凭证
    test_credentials = scf_test_data.get("login_credentials", {})
    
    # 合并凭证信息
    credentials = {
        "standard_user": config_credentials.get("standard_user", test_credentials.get("valid_user", {})),
        "admin_user": config_credentials.get("admin_user", {}),
        "test_user": config_credentials.get("test_user", {}),
        "invalid_user": test_credentials.get("invalid_user", {}),
        "empty_credentials": test_credentials.get("empty_credentials", {})
    }
    
    logger.info("准备SCF登录凭证")
    return credentials


@pytest.fixture(scope="function")
def scf_application_data(scf_test_data):
    """
    SCF申请数据 fixture
    
    Returns:
        Dict: 完整的申请数据
    """
    return scf_test_data["test_scenarios"]["complete_application"]["data"]


@pytest.fixture(scope="function")
def scf_minimal_application_data(scf_test_data):
    """
    SCF最小化申请数据 fixture
    
    Returns:
        Dict: 最小化申请数据
    """
    return scf_test_data["test_scenarios"]["minimal_application"]["data"]


@pytest.fixture(scope="function")
def scf_negative_test_data(scf_test_data):
    """
    SCF异常测试数据 fixture
    
    Returns:
        Dict: 异常测试数据
    """
    return scf_test_data["test_scenarios"]["negative_test"]["data"]


@pytest.fixture(scope="function")
def scf_file_paths(scf_test_data):
    """
    SCF测试文件路径 fixture
    
    Returns:
        Dict: 文件路径信息
    """
    return scf_test_data.get("file_paths", {})


@pytest.fixture(scope="function")
def scf_base_url(scf_config, env_config):
    """
    SCF系统基础URL fixture
    
    Returns:
        str: 基础URL
    """
    # 优先使用环境配置中的URL
    if "scf_url" in env_config:
        return env_config["scf_url"]
    
    # 使用SCF配置中的URL
    return scf_config["urls"]["base_url"]


@pytest.fixture(scope="function")
def scf_test_files_validator():
    """
    SCF测试文件验证器 fixture
    
    Returns:
        callable: 文件验证函数
    """
    def validate_test_files(file_paths: Dict[str, str]) -> Dict[str, bool]:
        """
        验证测试文件是否存在
        
        Args:
            file_paths: 文件路径字典
            
        Returns:
            Dict[str, bool]: 文件存在状态
        """
        project_root = Path(__file__).parent.parent
        validation_results = {}
        
        for file_key, file_path in file_paths.items():
            full_path = project_root / file_path
            exists = full_path.exists()
            validation_results[file_key] = exists
            
            if not exists:
                logger.warning(f"测试文件不存在: {file_key} -> {full_path}")
            else:
                logger.debug(f"测试文件验证通过: {file_key} -> {full_path}")
        
        return validation_results
    
    return validate_test_files


@pytest.fixture(scope="function")
def scf_data_cleanup():
    """
    SCF数据清理 fixture
    
    Returns:
        callable: 数据清理函数
    """
    cleanup_tasks = []
    
    def register_cleanup(cleanup_func, *args, **kwargs):
        """注册清理任务"""
        cleanup_tasks.append((cleanup_func, args, kwargs))
        logger.debug(f"注册SCF数据清理任务: {cleanup_func.__name__}")
    
    yield register_cleanup
    
    # 执行清理任务
    for cleanup_func, args, kwargs in cleanup_tasks:
        try:
            cleanup_func(*args, **kwargs)
            logger.info(f"SCF数据清理任务执行成功: {cleanup_func.__name__}")
        except Exception as e:
            logger.error(f"SCF数据清理任务执行失败: {cleanup_func.__name__}, 错误: {e}")


@pytest.fixture(scope="function", params=[
    "complete_application",
    "minimal_application"
])
def scf_parameterized_application_data(request, scf_test_data):
    """
    参数化的SCF申请数据 fixture
    
    Returns:
        Dict: 参数化的申请数据
    """
    scenario_name = request.param
    scenario_data = scf_test_data["test_scenarios"][scenario_name]
    
    logger.info(f"使用参数化申请数据: {scenario_name}")
    return {
        "scenario_name": scenario_name,
        "scenario_description": scenario_data["description"],
        "data": scenario_data["data"]
    }


@pytest.fixture(scope="function")
def scf_environment_config(scf_config, env_config):
    """
    SCF环境配置 fixture
    
    Returns:
        Dict: 环境特定配置
    """
    # 获取当前环境
    current_env = env_config.get("environment", "test")
    
    # 获取环境特定配置
    env_specific_config = scf_config.get("environments", {}).get(current_env, {})
    
    # 合并基础配置和环境特定配置
    merged_config = {**scf_config, **env_specific_config}
    
    logger.info(f"加载SCF环境配置: {current_env}")
    return merged_config


@pytest.fixture(scope="function")
def scf_form_defaults(scf_config):
    """
    SCF表单默认值 fixture
    
    Returns:
        Dict: 表单默认值
    """
    return scf_config.get("form_defaults", {
        "description_text": "测试",
        "default_option": "否",
        "max_description_length": 1000
    })


@pytest.fixture(scope="function")
def scf_timeout_config(scf_config):
    """
    SCF超时配置 fixture
    
    Returns:
        Dict: 超时配置
    """
    return scf_config.get("timeouts", {
        "page_load": 30000,
        "element_wait": 10000,
        "file_upload": 60000,
        "form_submit": 30000
    })


@pytest.fixture(scope="function")
def scf_institutions_list(scf_config):
    """
    SCF专业机构列表 fixture
    
    Returns:
        Dict: 专业机构信息
    """
    return scf_config.get("institutions", {
        "default": "天津中互金数据科技有限公司",
        "alternatives": ["天津中互金数据科技有限公司"]
    })


# 标记相关的 fixtures
@pytest.fixture(scope="function")
def scf_smoke_test_data(scf_minimal_application_data):
    """冒烟测试数据"""
    return scf_minimal_application_data


@pytest.fixture(scope="function") 
def scf_regression_test_data(scf_application_data):
    """回归测试数据"""
    return scf_application_data


@pytest.fixture(scope="function")
def scf_e2e_test_data(scf_application_data):
    """端到端测试数据"""
    return scf_application_data
