DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.address`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.address` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.automotive`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.automotive` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.bank`.
DEBUG    faker.factory:factory.py:88 Specified locale `en_US` is not available for provider `faker.providers.bank`. Locale reset to `en_GB` for this provider.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.barcode`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.barcode` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.color`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.color` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.company`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.company` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.credit_card`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.credit_card` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.currency`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.currency` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.date_time`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.date_time` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:108 Provider `faker.providers.doi` does not feature localization. Specified locale `en_US` is not utilized for this provider.
DEBUG    faker.factory:factory.py:108 Provider `faker.providers.emoji` does not feature localization. Specified locale `en_US` is not utilized for this provider.
DEBUG    faker.factory:factory.py:108 Provider `faker.providers.file` does not feature localization. Specified locale `en_US` is not utilized for this provider.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.geo`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.geo` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.internet`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.internet` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.isbn`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.isbn` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.job`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.job` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.lorem`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.lorem` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.misc`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.misc` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.passport`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.passport` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.person`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.person` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.phone_number`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.phone_number` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:108 Provider `faker.providers.profile` does not feature localization. Specified locale `en_US` is not utilized for this provider.
DEBUG    faker.factory:factory.py:108 Provider `faker.providers.python` does not feature localization. Specified locale `en_US` is not utilized for this provider.
DEBUG    faker.factory:factory.py:108 Provider `faker.providers.sbn` does not feature localization. Specified locale `en_US` is not utilized for this provider.
DEBUG    faker.factory:factory.py:78 Looking for locale `en_US` in provider `faker.providers.ssn`.
DEBUG    faker.factory:factory.py:97 Provider `faker.providers.ssn` has been localized to `en_US`.
DEBUG    faker.factory:factory.py:108 Provider `faker.providers.user_agent` does not feature localization. Specified locale `en_US` is not utilized for this provider.
DEBUG    urllib3.connectionpool:connectionpool.py:241 Starting new HTTP connection (1): 172.18.12.128:8080
DEBUG    urllib3.connectionpool:connectionpool.py:544 http://172.18.12.128:8080 "GET / HTTP/1.1" 200 833
INFO     conftest:conftest.py:192 测试服务器连接正常
INFO     conftest:conftest.py:29 ============================================================
INFO     conftest:conftest.py:30 开始 SCF 备案流程测试会话
INFO     conftest:conftest.py:31 ============================================================
DEBUG    asyncio:proactor_events.py:634 Using proactor: IocpProactor
INFO     conftest:conftest.py:53 开始测试类: TestSCFFilingBoundary
INFO     fixtures.filing_fixtures:filing_fixtures.py:174 开始备案测试: test_concurrent_operations_boundary[chromium]
INFO     components.ocr_util:ocr_util.py:60 OCR引擎初始化成功
INFO     components.filing_data_manager:filing_data_manager.py:42 生成备案测试数据，场景: normal
INFO     components.filing_data_manager:filing_data_manager.py:211 会话数据已保存: e76b45c4
INFO     components.filing_data_manager:filing_data_manager.py:62 测试数据生成完成，会话ID: e76b45c4
INFO     workflows.scf_filing_workflow:scf_filing_workflow.py:83 登录系统，用户名: scf_4nuioc
INFO     SCFLoginPage:scf_login_page.py:366 开始登录流程（带重试），用户名: scf_4nuioc, 最大重试次数: 5
INFO     SCFLoginPage:scf_login_page.py:62 点击账号登录标签
INFO     pages.base_page:base_page.py:190 等待元素 text=账号登录 状态: visible
WARNING  SCFLoginPage:scf_login_page.py:70 点击账号登录标签失败: Locator.wait_for: Timeout 10000ms exceeded.
Call log:
  - waiting for locator("text=账号登录") to be visible

INFO     SCFLoginPage:scf_login_page.py:95 输入用户名: scf_4nuioc
DEBUG    components.retry_util:retry_util.py:87 执行函数 fill，尝试 1/3
INFO     pages.base_page:base_page.py:130 填充文本到元素 input[placeholder='请输入账号']: scf_4nuioc
WARNING  components.retry_util:retry_util.py:97 函数 fill 第 1 次尝试失败: Locator.wait_for: Timeout 30000ms exceeded.
Call log:
  - waiting for locator("input[placeholder='请输入账号']") to be visible

DEBUG    components.retry_util:retry_util.py:115 等待 0.50 秒后重试
DEBUG    components.retry_util:retry_util.py:87 执行函数 fill，尝试 2/3
INFO     pages.base_page:base_page.py:130 填充文本到元素 input[placeholder='请输入账号']: scf_4nuioc
WARNING  components.retry_util:retry_util.py:97 函数 fill 第 2 次尝试失败: Locator.wait_for: Timeout 30000ms exceeded.
Call log:
  - waiting for locator("input[placeholder='请输入账号']") to be visible

DEBUG    components.retry_util:retry_util.py:115 等待 0.98 秒后重试
DEBUG    components.retry_util:retry_util.py:87 执行函数 fill，尝试 3/3
INFO     pages.base_page:base_page.py:130 填充文本到元素 input[placeholder='请输入账号']: scf_4nuioc
WARNING  components.retry_util:retry_util.py:97 函数 fill 第 3 次尝试失败: Locator.wait_for: Timeout 30000ms exceeded.
Call log:
  - waiting for locator("input[placeholder='请输入账号']") to be visible

ERROR    components.retry_util:retry_util.py:124 函数 fill 在 3 次尝试后仍然失败
INFO     conftest:conftest.py:135 完成测试: test_concurrent_operations_boundary[chromium]
INFO     components.filing_data_manager:filing_data_manager.py:248 清理了 0 个旧会话数据
INFO     fixtures.filing_fixtures:filing_fixtures.py:199 备案测试完成: test_concurrent_operations_boundary[chromium]
INFO     conftest:conftest.py:57 结束测试类: TestSCFFilingBoundary
INFO     conftest:conftest.py:38 ============================================================
INFO     conftest:conftest.py:39 结束 SCF 备案流程测试会话
INFO     conftest:conftest.py:40 ============================================================
INFO     conftest:conftest.py:201 测试环境检查完成