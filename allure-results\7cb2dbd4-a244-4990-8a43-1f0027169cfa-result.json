{"name": "test_login_page_elements[chromium]", "status": "broken", "statusDetails": {"message": "KeyError: 'locator'", "trace": "tests\\ui\\smoke\\test_scf_login.py:110: in test_login_page_elements\n    workflow.login_page.click_account_login_tab()\npages\\scf_login_page.py:58: in click_account_login_tab\n    self.click(self.account_tab)\nE   KeyError: 'locator'"}, "description": "验证登录页面必要元素存在", "steps": [{"name": "导航到登录页面", "status": "passed", "steps": [{"name": "导航到登录页面", "status": "passed", "steps": [{"name": "导航到页面: 'http://*************:8080/login'", "status": "passed", "parameters": [{"name": "url", "value": "'http://*************:8080/login'"}, {"name": "wait_until", "value": "'domcontentloaded'"}], "start": *************, "stop": *************}], "parameters": [{"name": "base_url", "value": "'http://*************:8080'"}], "start": *************, "stop": *************}], "start": *************, "stop": *************}, {"name": "验证登录页面元素", "status": "broken", "statusDetails": {"message": "KeyError: 'locator'\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\tests\\ui\\smoke\\test_scf_login.py\", line 110, in test_login_page_elements\n    workflow.login_page.click_account_login_tab()\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 58, in click_account_login_tab\n    self.click(self.account_tab)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 201, in impl\n    with StepContext(self.title.format(*args, **params), params):\n                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "steps": [{"name": "检查元素是否可见: 'text=账号登录'", "status": "passed", "parameters": [{"name": "locator", "value": "'text=账号登录'"}, {"name": "timeout", "value": "None"}], "start": *************, "stop": *************}, {"name": "点击账号登录标签", "status": "broken", "statusDetails": {"message": "KeyError: 'locator'\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 58, in click_account_login_tab\n    self.click(self.account_tab)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 201, in impl\n    with StepContext(self.title.format(*args, **params), params):\n                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "start": *************, "stop": *************}], "start": *************, "stop": *************}], "attachments": [{"name": "失败截图", "source": "5e4a8ebe-2eac-45db-ba46-62b773094356-attachment.png", "type": "image/png"}, {"name": "页面HTML", "source": "6c0d2793-c49c-4c11-9ebe-b678b58163f6-attachment.html", "type": "text/html"}, {"name": "当前URL", "source": "e74fb612-6577-450f-8770-dd54948b707d-attachment.txt", "type": "text/plain"}, {"name": "log", "source": "93e8372a-2297-4b03-91af-542c1b2eceee-attachment.txt", "type": "text/plain"}, {"name": "stderr", "source": "2f173c8a-4880-4fdd-bb38-69e576c065ca-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "browser_name", "value": "'chromium'"}], "start": 1751630309540, "stop": 1751630310438, "uuid": "4419483c-dfcc-4a9c-8018-3ce835dc7f7e", "historyId": "7869c781375e544239aebc49d435f721", "testCaseId": "bf3beae4a949bf431d6c971553df874b", "fullName": "tests.ui.smoke.test_scf_login.TestSCFLogin#test_login_page_elements", "labels": [{"name": "severity", "value": "minor"}, {"name": "epic", "value": "供应链金融备案系统"}, {"name": "story", "value": "登录页面元素验证"}, {"name": "feature", "value": "用户登录"}, {"name": "tag", "value": "component"}, {"name": "parentSuite", "value": "tests.ui.smoke"}, {"name": "suite", "value": "test_scf_login"}, {"name": "subSuite", "value": "TestSC<PERSON><PERSON>in"}, {"name": "host", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "thread", "value": "30168-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.ui.smoke.test_scf_login"}]}