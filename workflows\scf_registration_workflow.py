"""
供应链金融备案申请业务流程
Supply Chain Finance (SCF) Registration Workflow
"""

import allure
from typing import Dict, Any, Optional
from pathlib import Path
from workflows.base_workflow import BaseWorkflow
from pages.scf_login_page import SCFLoginPage
from pages.scf_registration_page import SCFRegistrationPage, SCFFormStepsPage
from components.log_manager import get_logger

logger = get_logger(__name__)


class SCFRegistrationWorkflow(BaseWorkflow):
    """供应链金融备案申请业务流程"""
    
    def __init__(self, page):
        """
        初始化业务流程
        
        Args:
            page: Playwright页面对象
        """
        super().__init__(page)
        self.login_page = SCFLoginPage(page)
        self.registration_page = SCFRegistrationPage(page)
        self.form_steps_page = SCFFormStepsPage(page)
        self.logger = get_logger(self.__class__.__name__)
    
    @allure.step("执行完整的备案申请流程")
    def complete_registration_process(self, 
                                    login_credentials: Dict[str, str],
                                    application_data: Dict[str, Any],
                                    base_url: str):
        """
        执行完整的备案申请流程
        
        Args:
            login_credentials: 登录凭证 {"username": "", "password": "", "captcha": ""}
            application_data: 申请数据
            base_url: 基础URL
        """
        self.logger.info("开始执行完整的备案申请流程")
        
        try:
            # 1. 登录系统
            self._login_to_system(login_credentials, base_url)
            
            # 2. 进入备案申请
            self._navigate_to_registration()
            
            # 3. 填写基础材料
            self._fill_basic_materials(application_data.get("basic_materials", {}))
            
            # 4. 填写业务合规信息
            self._fill_business_compliance(application_data.get("business_compliance", {}))
            
            # 5. 填写系统合规信息
            self._fill_system_compliance(application_data.get("system_compliance", {}))
            
            # 6. 填写业务管理信息
            self._fill_business_management(application_data.get("business_management", {}))
            
            # 7. 填写业务流程与合规信息
            self._fill_business_process_compliance(application_data.get("business_process_compliance", {}))
            
            # 8. 填写操作风险信息
            self._fill_operational_risk(application_data.get("operational_risk", {}))
            
            # 9. 填写第三方合作信息
            self._fill_third_party_cooperation(application_data.get("third_party_cooperation", {}))
            
            # 10. 填写资金方信息
            self._fill_funding_party(application_data.get("funding_party", {}))
            
            # 11. 填写付款方信息
            self._fill_payer(application_data.get("payer", {}))
            
            # 12. 填写费用管理信息
            self._fill_fee_management(application_data.get("fee_management", {}))
            
            # 13. 填写信息报送信息
            self._fill_information_reporting(application_data.get("information_reporting", {}))
            
            # 14. 上传推荐函等材料
            self._upload_recommendation_materials(application_data.get("recommendation_materials", {}))
            
            # 15. 选择专业机构并保存
            self._select_institution_and_save(application_data.get("institution", "天津中互金数据科技有限公司"))
            
            self.logger.info("备案申请流程执行完成")
            
        except Exception as e:
            self.logger.error(f"备案申请流程执行失败: {e}")
            self.take_screenshot_for_report("备案申请流程失败")
            raise
    
    @allure.step("登录系统")
    def _login_to_system(self, credentials: Dict[str, str], base_url: str):
        """
        登录系统
        
        Args:
            credentials: 登录凭证
            base_url: 基础URL
        """
        self.logger.info("开始登录系统")
        
        # 导航到登录页面
        self.login_page.navigate_to_login(base_url)
        
        # 执行登录
        username = credentials.get("username", "")
        password = credentials.get("password", "")
        captcha = credentials.get("captcha")
        
        if captcha:
            self.login_page.login(username, password, captcha, auto_captcha=False)
        else:
            self.login_page.login(username, password, auto_captcha=True)
        
        # 验证登录成功
        self.login_page.verify_login_success()
        self.logger.info("系统登录成功")
    
    @allure.step("进入备案申请")
    def _navigate_to_registration(self):
        """进入备案申请"""
        self.logger.info("进入备案申请")
        self.registration_page.click_registration_menu()
        self.registration_page.click_registration_apply()
    
    @allure.step("填写基础材料")
    def _fill_basic_materials(self, materials_data: Dict[str, Any]):
        """
        填写基础材料
        
        Args:
            materials_data: 基础材料数据
        """
        self.logger.info("填写基础材料")
        
        if not materials_data:
            self.logger.warning("基础材料数据为空，跳过此步骤")
            self.registration_page.click_next_step()
            return
        
        # 上传文件
        if "file_uploads" in materials_data:
            self.registration_page.upload_files_to_areas(materials_data["file_uploads"])
        
        # 点击下一步
        self.registration_page.click_next_step()
    
    @allure.step("填写业务合规信息")
    def _fill_business_compliance(self, compliance_data: Dict[str, Any]):
        """
        填写业务合规信息
        
        Args:
            compliance_data: 业务合规数据
        """
        self.logger.info("填写业务合规信息")
        
        if compliance_data:
            self.registration_page.fill_business_compliance_form(compliance_data)
        
        self.registration_page.click_next_step()
    
    @allure.step("填写系统合规信息")
    def _fill_system_compliance(self, system_data: Dict[str, Any]):
        """
        填写系统合规信息
        
        Args:
            system_data: 系统合规数据
        """
        self.logger.info("填写系统合规信息")
        
        if system_data:
            self.registration_page.fill_system_compliance_form(system_data)
        
        self.registration_page.click_next_step()
    
    @allure.step("填写业务管理信息")
    def _fill_business_management(self, management_data: Dict[str, Any]):
        """
        填写业务管理信息
        
        Args:
            management_data: 业务管理数据
        """
        self.logger.info("填写业务管理信息")
        
        if management_data:
            self.form_steps_page.fill_business_management_form(management_data)
        
        self.registration_page.click_next_step()
    
    @allure.step("填写业务流程与合规信息")
    def _fill_business_process_compliance(self, process_data: Dict[str, Any]):
        """
        填写业务流程与合规信息
        
        Args:
            process_data: 业务流程合规数据
        """
        self.logger.info("填写业务流程与合规信息")
        
        if process_data:
            self.form_steps_page.fill_business_process_compliance_form(process_data)
        
        self.registration_page.click_next_step()
    
    @allure.step("填写操作风险信息")
    def _fill_operational_risk(self, risk_data: Dict[str, Any]):
        """
        填写操作风险信息
        
        Args:
            risk_data: 操作风险数据
        """
        self.logger.info("填写操作风险信息")
        
        if risk_data:
            self.form_steps_page.fill_operational_risk_form(risk_data)
        
        self.registration_page.click_next_step()
    
    @allure.step("填写第三方合作信息")
    def _fill_third_party_cooperation(self, cooperation_data: Dict[str, Any]):
        """
        填写第三方合作信息
        
        Args:
            cooperation_data: 第三方合作数据
        """
        self.logger.info("填写第三方合作信息")
        
        if cooperation_data:
            self.form_steps_page.fill_third_party_cooperation_form(cooperation_data)
        
        self.registration_page.click_next_step()
    
    @allure.step("填写资金方信息")
    def _fill_funding_party(self, funding_data: Dict[str, Any]):
        """
        填写资金方信息
        
        Args:
            funding_data: 资金方数据
        """
        self.logger.info("填写资金方信息")
        
        if funding_data:
            self.form_steps_page.fill_funding_party_form(funding_data)
        
        self.registration_page.click_next_step()
    
    @allure.step("填写付款方信息")
    def _fill_payer(self, payer_data: Dict[str, Any]):
        """
        填写付款方信息
        
        Args:
            payer_data: 付款方数据
        """
        self.logger.info("填写付款方信息")
        
        if payer_data:
            self.form_steps_page.fill_payer_form(payer_data)
        
        self.registration_page.click_next_step()
    
    @allure.step("填写费用管理信息")
    def _fill_fee_management(self, fee_data: Dict[str, Any]):
        """
        填写费用管理信息
        
        Args:
            fee_data: 费用管理数据
        """
        self.logger.info("填写费用管理信息")
        
        if fee_data:
            self.form_steps_page.fill_fee_management_form(fee_data)
        
        self.registration_page.click_next_step()
    
    @allure.step("填写信息报送信息")
    def _fill_information_reporting(self, reporting_data: Dict[str, Any]):
        """
        填写信息报送信息
        
        Args:
            reporting_data: 信息报送数据
        """
        self.logger.info("填写信息报送信息")
        
        if reporting_data:
            self.form_steps_page.fill_information_reporting_form(reporting_data)
        
        self.registration_page.click_next_step()
    
    @allure.step("上传推荐函等材料")
    def _upload_recommendation_materials(self, materials_data: Dict[str, Any]):
        """
        上传推荐函等材料
        
        Args:
            materials_data: 推荐材料数据
        """
        self.logger.info("上传推荐函等材料")
        
        if "recommendation_letters" in materials_data:
            # 上传推荐函
            for file_path in materials_data["recommendation_letters"]:
                upload_selector = "#irmRecLetterIds text=点击此处上传附件"
                self.registration_page.upload_file(file_path, upload_selector)
        
        if "other_materials" in materials_data:
            # 上传其他材料
            for file_path in materials_data["other_materials"]:
                upload_selector = "#omOtherSuppId text=点击此处上传附件"
                self.registration_page.upload_file(file_path, upload_selector)
    
    @allure.step("选择专业机构并保存")
    def _select_institution_and_save(self, institution_name: str):
        """
        选择专业机构并保存
        
        Args:
            institution_name: 机构名称
        """
        self.logger.info(f"选择专业机构并保存: {institution_name}")
        
        # 选择专业机构
        self.registration_page.select_institution(institution_name)
        
        # 保存申请
        self.registration_page.save_application()
    
    @allure.step("验证申请提交成功")
    def verify_application_submitted(self):
        """验证申请提交成功"""
        self.logger.info("验证申请提交成功")
        
        # 等待页面加载
        self.wait_for_load_state("networkidle")
        
        # 可以根据实际页面添加具体的验证逻辑
        # 例如检查成功消息、URL变化等
        current_url = self.get_current_url()
        self.logger.info(f"申请提交后的URL: {current_url}")
        
        # 截图记录
        self.take_screenshot_for_report("申请提交完成")
        
        return self
