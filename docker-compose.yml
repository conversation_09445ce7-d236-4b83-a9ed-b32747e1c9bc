# 企业级 Web 自动化测试平台 Docker Compose 配置
# 支持本地一键启动测试环境

version: '3.8'

services:
  # 主测试服务
  playwright-tests:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: playwright-tests
    volumes:
      # 挂载源码目录（开发模式）
      - .:/app
      # 挂载日志目录
      - ./logs:/app/logs
      # 挂载报告目录
      - ./allure-results:/app/allure-results
      - ./reports:/app/reports
      # 挂载缓存目录
      - uv-cache:/tmp/uv-cache
    environment:
      # 测试环境配置
      - TEST_ENV=docker
      - PYTHONPATH=/app
      - ALLURE_RESULTS_DIR=/app/allure-results
      # Playwright 配置
      - PLAYWRIGHT_BROWSERS_PATH=/ms-playwright
      - PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD=1
      # 显示配置（用于调试）
      - DISPLAY=:99
    networks:
      - test-network
    depends_on:
      - test-app
    command: >
      sh -c "
        echo 'Waiting for test application to be ready...' &&
        sleep 10 &&
        uv run pytest tests/ui/smoke/ --env=docker --alluredir=allure-results -v
      "

  # 被测试的应用服务（示例）
  test-app:
    image: nginx:alpine
    container_name: test-app
    ports:
      - "8080:80"
    volumes:
      - ./test-app:/usr/share/nginx/html:ro
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    networks:
      - test-network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  # Allure 报告服务
  allure-server:
    image: frankescobar/allure-docker-service
    container_name: allure-server
    ports:
      - "5050:5050"
    environment:
      CHECK_RESULTS_EVERY_SECONDS: 3
      KEEP_HISTORY: 1
    volumes:
      - ./allure-results:/app/allure-results
      - ./reports:/app/default-reports
    networks:
      - test-network
    depends_on:
      - playwright-tests

  # 数据库服务（如果需要）
  test-db:
    image: postgres:15-alpine
    container_name: test-db
    environment:
      POSTGRES_DB: testdb
      POSTGRES_USER: testuser
      POSTGRES_PASSWORD: testpass
    ports:
      - "5432:5432"
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./db-init:/docker-entrypoint-initdb.d
    networks:
      - test-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U testuser -d testdb"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis 缓存服务（如果需要）
  test-redis:
    image: redis:7-alpine
    container_name: test-redis
    ports:
      - "6379:6379"
    networks:
      - test-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

# 网络配置
networks:
  test-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷配置
volumes:
  postgres-data:
    driver: local
  uv-cache:
    driver: local

# 扩展配置
x-common-variables: &common-variables
  PYTHONPATH: /app
  TEST_ENV: docker

# 开发环境覆盖配置
# 使用方式: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up
