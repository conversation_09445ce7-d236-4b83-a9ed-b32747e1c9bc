{"name": "test_login_page_elements[chromium]", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: Page.goto: net::ERR_CONNECTION_TIMED_OUT at http://*************:8080/login\nCall log:\n  - navigating to \"http://*************:8080/login\", waiting until \"domcontentloaded\"", "trace": "test_scf_login.py:110: in test_login_page_elements\n    workflow.login_page.navigate_to_login(scf_base_url)\n..\\..\\..\\pages\\scf_login_page.py:51: in navigate_to_login\n    self.navigate(full_url)\n..\\..\\..\\pages\\base_page.py:31: in navigate\n    self.page.goto(url, wait_until=wait_until, timeout=self.default_timeout)\n..\\..\\..\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py:9002: in goto\n    self._sync(\n..\\..\\..\\.venv\\Lib\\site-packages\\playwright\\_impl\\_page.py:556: in goto\n    return await self._main_frame.goto(**locals_to_params(locals()))\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n..\\..\\..\\.venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py:146: in goto\n    await self._channel.send(\n..\\..\\..\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:69: in send\n    return await self._connection.wrap_api_call(\n..\\..\\..\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:558: in wrap_api_call\n    raise rewrite_error(error, f\"{parsed_st['apiName']}: {error}\") from None\nE   playwright._impl._errors.Error: Page.goto: net::ERR_CONNECTION_TIMED_OUT at http://*************:8080/login\nE   Call log:\nE     - navigating to \"http://*************:8080/login\", waiting until \"domcontentloaded\""}, "description": "验证登录页面必要元素存在", "steps": [{"name": "导航到登录页面", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: Page.goto: net::ERR_CONNECTION_TIMED_OUT at http://*************:8080/login\nCall log:\n  - navigating to \"http://*************:8080/login\", waiting until \"domcontentloaded\"\n\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\tests\\ui\\smoke\\test_scf_login.py\", line 110, in test_login_page_elements\n    workflow.login_page.navigate_to_login(scf_base_url)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 51, in navigate_to_login\n    self.navigate(full_url)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 31, in navigate\n    self.page.goto(url, wait_until=wait_until, timeout=self.default_timeout)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 9002, in goto\n    self._sync(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 115, in _sync\n    return task.result()\n           ^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_page.py\", line 556, in goto\n    return await self._main_frame.goto(**locals_to_params(locals()))\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py\", line 146, in goto\n    await self._channel.send(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 69, in send\n    return await self._connection.wrap_api_call(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 558, in wrap_api_call\n    raise rewrite_error(error, f\"{parsed_st['apiName']}: {error}\") from None\n"}, "steps": [{"name": "导航到登录页面", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: Page.goto: net::ERR_CONNECTION_TIMED_OUT at http://*************:8080/login\nCall log:\n  - navigating to \"http://*************:8080/login\", waiting until \"domcontentloaded\"\n\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 51, in navigate_to_login\n    self.navigate(full_url)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 31, in navigate\n    self.page.goto(url, wait_until=wait_until, timeout=self.default_timeout)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 9002, in goto\n    self._sync(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 115, in _sync\n    return task.result()\n           ^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_page.py\", line 556, in goto\n    return await self._main_frame.goto(**locals_to_params(locals()))\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py\", line 146, in goto\n    await self._channel.send(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 69, in send\n    return await self._connection.wrap_api_call(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 558, in wrap_api_call\n    raise rewrite_error(error, f\"{parsed_st['apiName']}: {error}\") from None\n"}, "steps": [{"name": "导航到页面: 'http://*************:8080/login'", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: Page.goto: net::ERR_CONNECTION_TIMED_OUT at http://*************:8080/login\nCall log:\n  - navigating to \"http://*************:8080/login\", waiting until \"domcontentloaded\"\n\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 31, in navigate\n    self.page.goto(url, wait_until=wait_until, timeout=self.default_timeout)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 9002, in goto\n    self._sync(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 115, in _sync\n    return task.result()\n           ^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_page.py\", line 556, in goto\n    return await self._main_frame.goto(**locals_to_params(locals()))\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py\", line 146, in goto\n    await self._channel.send(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 69, in send\n    return await self._connection.wrap_api_call(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 558, in wrap_api_call\n    raise rewrite_error(error, f\"{parsed_st['apiName']}: {error}\") from None\n"}, "parameters": [{"name": "url", "value": "'http://*************:8080/login'"}, {"name": "wait_until", "value": "'domcontentloaded'"}], "start": 1751851554076, "stop": 1751851575100}], "parameters": [{"name": "base_url", "value": "'http://*************:8080'"}], "start": 1751851554074, "stop": 1751851575101}], "start": 1751851554074, "stop": 1751851575102}], "attachments": [{"name": "失败截图", "source": "b36ea93c-e5e3-4053-8f50-9285d63dfdfd-attachment.png", "type": "image/png"}, {"name": "页面HTML", "source": "222dea4c-0756-4aaa-a48a-6435472fa64f-attachment.html", "type": "text/html"}, {"name": "当前URL", "source": "a6ff87f7-a61d-4774-bab7-1a7651c9de0d-attachment.txt", "type": "text/plain"}, {"name": "log", "source": "c451a405-256e-47e2-a1ea-e82d3c2c6a56-attachment.txt", "type": "text/plain"}, {"name": "stderr", "source": "f2e3b831-a9e4-4894-9052-b4739ad5592a-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "browser_name", "value": "'chromium'"}], "start": 1751851553911, "stop": 1751851575104, "uuid": "3b32b021-5bf0-43e5-bcbc-e43c8df6a98d", "historyId": "7869c781375e544239aebc49d435f721", "testCaseId": "bf3beae4a949bf431d6c971553df874b", "fullName": "tests.ui.smoke.test_scf_login.TestSCFLogin#test_login_page_elements", "labels": [{"name": "feature", "value": "用户登录"}, {"name": "severity", "value": "minor"}, {"name": "epic", "value": "供应链金融备案系统"}, {"name": "story", "value": "登录页面元素验证"}, {"name": "tag", "value": "component"}, {"name": "parentSuite", "value": "tests.ui.smoke"}, {"name": "suite", "value": "test_scf_login"}, {"name": "subSuite", "value": "TestSC<PERSON><PERSON>in"}, {"name": "host", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "thread", "value": "24864-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.ui.smoke.test_scf_login"}]}