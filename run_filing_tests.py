#!/usr/bin/env python3
"""
SCF 备案流程测试运行脚本
提供便捷的测试执行和管理功能
"""

import subprocess
import sys
import argparse
from pathlib import Path
import json
from datetime import datetime


class FilingTestRunner:
    """备案测试运行器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.test_dir = self.project_root / "tests" / "ui" / "filing"
        
    def run_tests(self, test_type="all", browser="chromium", headed=True, workers=1):
        """
        运行备案测试
        
        Args:
            test_type: 测试类型 (all, normal, exception, boundary, smoke)
            browser: 浏览器类型 (chromium, firefox, webkit)
            headed: 是否显示浏览器界面
            workers: 并行工作进程数
        """
        print(f"🚀 开始运行 SCF 备案流程测试")
        print(f"📁 测试类型: {test_type}")
        print(f"🌐 浏览器: {browser}")
        print(f"👁️  界面显示: {'是' if headed else '否'}")
        print(f"⚡ 并行进程: {workers}")
        print("=" * 60)
        
        # 构建测试命令
        cmd = [
            "uv", "run", "pytest",
            "--alluredir=allure-results",
            "--clean-alluredir",
            f"--browser={browser}",
            f"--workers={workers}",
            "-v"
        ]
        
        # 添加界面显示选项
        if headed:
            cmd.extend(["--headed", "--slowmo=500"])
        
        # 根据测试类型选择测试文件
        if test_type == "all":
            cmd.append(str(self.test_dir))
        elif test_type == "normal":
            cmd.append(str(self.test_dir / "test_scf_filing_normal.py"))
        elif test_type == "exception":
            cmd.append(str(self.test_dir / "test_scf_filing_exceptions.py"))
        elif test_type == "boundary":
            cmd.append(str(self.test_dir / "test_scf_filing_boundary.py"))
        elif test_type == "smoke":
            cmd.extend(["-m", "smoke", str(self.test_dir)])
        else:
            print(f"❌ 未知的测试类型: {test_type}")
            return False
        
        try:
            # 运行测试
            print(f"执行命令: {' '.join(cmd)}")
            result = subprocess.run(cmd, cwd=self.project_root)
            
            if result.returncode == 0:
                print("✅ 测试执行成功")
                self.generate_report()
                return True
            else:
                print("❌ 测试执行失败")
                return False
                
        except KeyboardInterrupt:
            print("\n⚠️ 测试被用户中断")
            return False
        except Exception as e:
            print(f"❌ 运行测试时出错: {e}")
            return False
    
    def generate_report(self):
        """生成测试报告"""
        try:
            print("\n📊 生成 Allure 测试报告...")
            
            # 生成 Allure 报告
            cmd = ["uv", "run", "allure", "generate", "allure-results", "-o", "allure-report", "--clean"]
            result = subprocess.run(cmd, cwd=self.project_root, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ Allure 报告生成成功")
                print(f"📁 报告位置: {self.project_root / 'allure-report' / 'index.html'}")
                
                # 尝试打开报告
                try:
                    import webbrowser
                    report_path = self.project_root / "allure-report" / "index.html"
                    webbrowser.open(f"file://{report_path.absolute()}")
                    print("🌐 已在浏览器中打开测试报告")
                except Exception as e:
                    print(f"⚠️ 无法自动打开报告: {e}")
            else:
                print(f"❌ 生成报告失败: {result.stderr}")
                
        except Exception as e:
            print(f"❌ 生成报告时出错: {e}")
    
    def list_tests(self):
        """列出所有可用的测试"""
        print("📋 可用的备案流程测试:")
        print()
        
        test_files = [
            ("test_scf_filing_normal.py", "正常流程测试", "测试完整的备案申请流程"),
            ("test_scf_filing_exceptions.py", "异常场景测试", "测试各种异常情况和错误处理"),
            ("test_scf_filing_boundary.py", "边界值测试", "测试各种边界条件和极限值")
        ]
        
        for filename, title, description in test_files:
            file_path = self.test_dir / filename
            if file_path.exists():
                print(f"✅ {title}")
                print(f"   📄 文件: {filename}")
                print(f"   📝 描述: {description}")
                print()
            else:
                print(f"❌ {title}")
                print(f"   📄 文件: {filename} (不存在)")
                print()
    
    def check_environment(self):
        """检查测试环境"""
        print("🔍 检查测试环境...")
        print()
        
        # 检查 Python 环境
        print(f"🐍 Python 版本: {sys.version}")
        
        # 检查项目依赖
        try:
            import playwright  # noqa: F401
            print("✅ Playwright 已安装")
        except ImportError:
            print("❌ Playwright 未安装")
            return False
        
        # 检查测试文件
        required_files = [
            "pages/scf_main_page.py",
            "pages/scf_filing_page.py", 
            "workflows/scf_filing_workflow.py",
            "components/filing_data_manager.py"
        ]
        
        missing_files = []
        for file_path in required_files:
            if not (self.project_root / file_path).exists():
                missing_files.append(file_path)
        
        if missing_files:
            print("❌ 缺少必要文件:")
            for file_path in missing_files:
                print(f"   - {file_path}")
            return False
        else:
            print("✅ 所有必要文件存在")
        
        # 检查测试数据目录
        test_data_dir = self.project_root / "test_data" / "files"
        if not test_data_dir.exists():
            print("⚠️ 测试数据目录不存在，将自动创建")
            test_data_dir.mkdir(parents=True, exist_ok=True)
        else:
            print("✅ 测试数据目录存在")
        
        print()
        print("✅ 环境检查完成")
        return True
    
    def clean_test_data(self):
        """清理测试数据"""
        print("🧹 清理测试数据...")
        
        # 清理 Allure 结果
        allure_results = self.project_root / "allure-results"
        if allure_results.exists():
            import shutil
            shutil.rmtree(allure_results)
            print("✅ 清理 Allure 结果")
        
        # 清理测试会话数据
        session_data_file = self.project_root / "test_data" / "session_data.json"
        if session_data_file.exists():
            session_data_file.unlink()
            print("✅ 清理会话数据")
        
        print("✅ 测试数据清理完成")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="SCF 备案流程测试运行器")
    parser.add_argument("--type", "-t", choices=["all", "normal", "exception", "boundary", "smoke"], 
                       default="all", help="测试类型")
    parser.add_argument("--browser", "-b", choices=["chromium", "firefox", "webkit"], 
                       default="chromium", help="浏览器类型")
    parser.add_argument("--headless", action="store_true", help="无界面模式")
    parser.add_argument("--workers", "-w", type=int, default=1, help="并行工作进程数")
    parser.add_argument("--list", action="store_true", help="列出所有测试")
    parser.add_argument("--check", action="store_true", help="检查测试环境")
    parser.add_argument("--clean", action="store_true", help="清理测试数据")
    
    args = parser.parse_args()
    
    runner = FilingTestRunner()
    
    if args.list:
        runner.list_tests()
        return
    
    if args.check:
        if not runner.check_environment():
            sys.exit(1)
        return
    
    if args.clean:
        runner.clean_test_data()
        return
    
    # 检查环境
    if not runner.check_environment():
        print("❌ 环境检查失败，请修复后重试")
        sys.exit(1)
    
    # 运行测试
    success = runner.run_tests(
        test_type=args.type,
        browser=args.browser,
        headed=not args.headless,
        workers=args.workers
    )
    
    if not success:
        sys.exit(1)


if __name__ == "__main__":
    main()
