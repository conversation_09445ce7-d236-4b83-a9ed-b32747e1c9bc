{"name": "test_captcha_functionality[chromium]", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: Page.goto: net::ERR_CONNECTION_TIMED_OUT at http://*************:8080/login\nCall log:\n  - navigating to \"http://*************:8080/login\", waiting until \"domcontentloaded\"", "trace": "test_scf_login.py:135: in test_captcha_functionality\n    workflow.login_page.navigate_to_login(scf_base_url)\n..\\..\\..\\pages\\scf_login_page.py:51: in navigate_to_login\n    self.navigate(full_url)\n..\\..\\..\\pages\\base_page.py:31: in navigate\n    self.page.goto(url, wait_until=wait_until, timeout=self.default_timeout)\n..\\..\\..\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py:9002: in goto\n    self._sync(\n..\\..\\..\\.venv\\Lib\\site-packages\\playwright\\_impl\\_page.py:556: in goto\n    return await self._main_frame.goto(**locals_to_params(locals()))\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n..\\..\\..\\.venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py:146: in goto\n    await self._channel.send(\n..\\..\\..\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:69: in send\n    return await self._connection.wrap_api_call(\n..\\..\\..\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:558: in wrap_api_call\n    raise rewrite_error(error, f\"{parsed_st['apiName']}: {error}\") from None\nE   playwright._impl._errors.Error: Page.goto: net::ERR_CONNECTION_TIMED_OUT at http://*************:8080/login\nE   Call log:\nE     - navigating to \"http://*************:8080/login\", waiting until \"domcontentloaded\""}, "description": "测试验证码功能", "steps": [{"name": "导航到登录页面", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: Page.goto: net::ERR_CONNECTION_TIMED_OUT at http://*************:8080/login\nCall log:\n  - navigating to \"http://*************:8080/login\", waiting until \"domcontentloaded\"\n\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\tests\\ui\\smoke\\test_scf_login.py\", line 135, in test_captcha_functionality\n    workflow.login_page.navigate_to_login(scf_base_url)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 51, in navigate_to_login\n    self.navigate(full_url)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 31, in navigate\n    self.page.goto(url, wait_until=wait_until, timeout=self.default_timeout)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 9002, in goto\n    self._sync(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 115, in _sync\n    return task.result()\n           ^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_page.py\", line 556, in goto\n    return await self._main_frame.goto(**locals_to_params(locals()))\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py\", line 146, in goto\n    await self._channel.send(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 69, in send\n    return await self._connection.wrap_api_call(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 558, in wrap_api_call\n    raise rewrite_error(error, f\"{parsed_st['apiName']}: {error}\") from None\n"}, "steps": [{"name": "导航到登录页面", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: Page.goto: net::ERR_CONNECTION_TIMED_OUT at http://*************:8080/login\nCall log:\n  - navigating to \"http://*************:8080/login\", waiting until \"domcontentloaded\"\n\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\scf_login_page.py\", line 51, in navigate_to_login\n    self.navigate(full_url)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 31, in navigate\n    self.page.goto(url, wait_until=wait_until, timeout=self.default_timeout)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 9002, in goto\n    self._sync(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 115, in _sync\n    return task.result()\n           ^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_page.py\", line 556, in goto\n    return await self._main_frame.goto(**locals_to_params(locals()))\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py\", line 146, in goto\n    await self._channel.send(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 69, in send\n    return await self._connection.wrap_api_call(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 558, in wrap_api_call\n    raise rewrite_error(error, f\"{parsed_st['apiName']}: {error}\") from None\n"}, "steps": [{"name": "导航到页面: 'http://*************:8080/login'", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: Page.goto: net::ERR_CONNECTION_TIMED_OUT at http://*************:8080/login\nCall log:\n  - navigating to \"http://*************:8080/login\", waiting until \"domcontentloaded\"\n\n", "trace": "  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\allure_commons\\_allure.py\", line 202, in impl\n    return func(*a, **kw)\n           ^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\pages\\base_page.py\", line 31, in navigate\n    self.page.goto(url, wait_until=wait_until, timeout=self.default_timeout)\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 9002, in goto\n    self._sync(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 115, in _sync\n    return task.result()\n           ^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_page.py\", line 556, in goto\n    return await self._main_frame.goto(**locals_to_params(locals()))\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py\", line 146, in goto\n    await self._channel.send(\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 69, in send\n    return await self._connection.wrap_api_call(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"G:\\nifa\\playwright-python-template\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 558, in wrap_api_call\n    raise rewrite_error(error, f\"{parsed_st['apiName']}: {error}\") from None\n"}, "parameters": [{"name": "url", "value": "'http://*************:8080/login'"}, {"name": "wait_until", "value": "'domcontentloaded'"}], "start": 1751851575716, "stop": 1751851596771}], "parameters": [{"name": "base_url", "value": "'http://*************:8080'"}], "start": 1751851575714, "stop": 1751851596772}], "start": 1751851575714, "stop": 1751851596773}], "attachments": [{"name": "失败截图", "source": "78e94375-1832-4a98-8c29-2a6b22dd4877-attachment.png", "type": "image/png"}, {"name": "页面HTML", "source": "4771a5da-bd5d-494e-99b9-98836c9d0169-attachment.html", "type": "text/html"}, {"name": "当前URL", "source": "8fe6c548-58e5-48ff-a319-9e2a6daccad2-attachment.txt", "type": "text/plain"}, {"name": "log", "source": "2a9f32a4-a8ab-48b3-96a4-d4e80b229272-attachment.txt", "type": "text/plain"}, {"name": "stderr", "source": "d98a93a0-18ef-442f-a93c-efbea8651579-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "browser_name", "value": "'chromium'"}], "start": 1751851575563, "stop": 1751851596775, "uuid": "b9589a04-d90f-44d8-96ec-505b82c7d2ee", "historyId": "9c48d705d01d7be2c7e42debe8eca4c9", "testCaseId": "c5ee54436ca1b45fcb049d6e49e06abd", "fullName": "tests.ui.smoke.test_scf_login.TestSCFLogin#test_captcha_functionality", "labels": [{"name": "feature", "value": "用户登录"}, {"name": "severity", "value": "normal"}, {"name": "story", "value": "验证码功能测试"}, {"name": "epic", "value": "供应链金融备案系统"}, {"name": "tag", "value": "component"}, {"name": "parentSuite", "value": "tests.ui.smoke"}, {"name": "suite", "value": "test_scf_login"}, {"name": "subSuite", "value": "TestSC<PERSON><PERSON>in"}, {"name": "host", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "thread", "value": "24864-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.ui.smoke.test_scf_login"}]}