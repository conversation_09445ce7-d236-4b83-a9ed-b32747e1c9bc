{"name": "test_file_upload_functionality[chromium]", "status": "skipped", "statusDetails": {"message": "Skipped: 所有测试文件都不存在", "trace": "('G:\\\\nifa\\\\playwright-python-template\\\\tests\\\\ui\\\\smoke\\\\test_scf_registration.py', 92, 'Skipped: 所有测试文件都不存在')"}, "description": "测试文件上传功能", "attachments": [{"name": "log", "source": "07f2be00-bc88-41f0-8026-5115a4487bd8-attachment.txt", "type": "text/plain"}, {"name": "stdout", "source": "123dafd1-d890-4162-b086-04126ec5ffec-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "browser_name", "value": "'chromium'"}], "start": 1751854947778, "stop": 1751854947779, "uuid": "1d28c325-08f3-432b-9a69-0d92aef1db9b", "historyId": "374030f51da60a715346b4d3655abca8", "testCaseId": "294bb0af011bffa19ef5be1dccf5b606", "fullName": "tests.ui.smoke.test_scf_registration.TestSCFRegistration#test_file_upload_functionality", "labels": [{"name": "severity", "value": "normal"}, {"name": "feature", "value": "备案申请"}, {"name": "epic", "value": "供应链金融备案系统"}, {"name": "story", "value": "文件上传功能"}, {"name": "tag", "value": "component"}, {"name": "parentSuite", "value": "tests.ui.smoke"}, {"name": "suite", "value": "test_scf_registration"}, {"name": "subSuite", "value": "TestSCFRegistration"}, {"name": "host", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "thread", "value": "24016-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.ui.smoke.test_scf_registration"}]}